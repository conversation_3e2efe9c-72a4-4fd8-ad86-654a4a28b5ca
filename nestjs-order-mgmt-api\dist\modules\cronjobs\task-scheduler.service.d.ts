import { Repository } from 'typeorm';
import { Cronjob } from './entities/cronjob.entity';
import { Order } from '../orders/entities/order.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { EmailService } from '../email/email.service';
export declare class TaskSchedulerService {
    private readonly orderRepository;
    private readonly scheduleRepository;
    private readonly emailService;
    private readonly logger;
    constructor(orderRepository: Repository<Order>, scheduleRepository: Repository<Schedule>, emailService: EmailService);
    executeJob(cronjob: Cronjob): Promise<any>;
    private sendEmailReminders;
    private cleanupOldLogs;
    private generateReports;
    private syncExternalData;
    private backupDatabase;
    private updateSchedules;
    private sendNotifications;
    private executeCustomJob;
}
