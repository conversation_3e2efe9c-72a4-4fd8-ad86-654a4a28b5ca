import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException } from '@nestjs/common';

import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { OrderStatus } from './entities/order.entity';

describe('OrdersController', () => {
  let controller: OrdersController;
  let service: OrdersService;

  const mockOrdersService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    assignInspectors: jest.fn(),
    assignInspector: jest.fn(),
    scheduleInspection: jest.fn(),
    completeInspection: jest.fn(),
    cancelOrder: jest.fn(),
    getOrderStats: jest.fn(),
    getClientOrders: jest.fn(),
    getInspectorOrders: jest.fn(),
    addClientToOrder: jest.fn(),
    removeClientFromOrder: jest.fn(),
    updateOrderClients: jest.fn(),
    getOrdersForClients: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrdersController],
      providers: [
        {
          provide: OrdersService,
          useValue: mockOrdersService,
        },
      ],
    }).compile();

    controller = module.get<OrdersController>(OrdersController);
    service = module.get<OrdersService>(OrdersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create an order', async () => {
      const createOrderDto: CreateOrderDto = {
        propertyType: 'residential',
        addressLine1: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
      };
      const user = { role: 'client', userId: 1 };
      const expectedResult = {
        order: { id: 1, orderNumber: 'ORD-2024-0001', ...createOrderDto },
        message: 'Order created successfully',
      };

      mockOrdersService.create.mockResolvedValue(expectedResult);

      const result = await controller.create(createOrderDto, user);

      expect(result).toEqual(expectedResult);
      expect(service.create).toHaveBeenCalledWith(createOrderDto, user);
    });
  });

  describe('findAll', () => {
    it('should return paginated orders', async () => {
      const query: OrderQueryDto = { page: 1, limit: 10 };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        orders: [{ id: 1, orderNumber: 'ORD-2024-0001' }],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      };

      mockOrdersService.findAll.mockResolvedValue(expectedResult);

      const result = await controller.findAll(query, user);

      expect(result).toEqual(expectedResult);
      expect(service.findAll).toHaveBeenCalledWith(query, user);
    });
  });

  describe('findOne', () => {
    it('should return a single order', async () => {
      const orderId = 1;
      const user = { role: 'admin', userId: 1 };
      const expectedResult = { id: 1, orderNumber: 'ORD-2024-0001' };

      mockOrdersService.findOne.mockResolvedValue(expectedResult);

      const result = await controller.findOne(orderId, user);

      expect(result).toEqual(expectedResult);
      expect(service.findOne).toHaveBeenCalledWith(orderId, user);
    });
  });

  describe('update', () => {
    it('should update an order', async () => {
      const orderId = 1;
      const updateOrderDto: UpdateOrderDto = { status: OrderStatus.ASSIGNED };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        order: { id: 1, status: OrderStatus.ASSIGNED },
        message: 'Order updated successfully',
      };

      mockOrdersService.update.mockResolvedValue(expectedResult);

      const result = await controller.update(orderId, updateOrderDto, user);

      expect(result).toEqual(expectedResult);
      expect(service.update).toHaveBeenCalledWith(orderId, updateOrderDto, user);
    });
  });

  describe('remove', () => {
    it('should delete an order', async () => {
      const orderId = 1;
      const user = { role: 'admin', userId: 1 };
      const expectedResult = { message: 'Order deleted successfully' };

      mockOrdersService.remove.mockResolvedValue(expectedResult);

      const result = await controller.remove(orderId, user);

      expect(result).toEqual(expectedResult);
      expect(service.remove).toHaveBeenCalledWith(orderId, user);
    });
  });

  describe('assignInspectors', () => {
    it('should assign multiple inspectors to order', async () => {
      const orderId = 1;
      const assignData = { inspectorIds: [1, 2] };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Inspectors assigned successfully',
        order: { id: 1, assignedInspectorIds: [1, 2] },
      };

      mockOrdersService.assignInspectors.mockResolvedValue(expectedResult);

      const result = await controller.assignInspectors(orderId, assignData, user);

      expect(result).toEqual(expectedResult);
      expect(service.assignInspectors).toHaveBeenCalledWith(orderId, assignData.inspectorIds, user);
    });
  });

  describe('assignInspector', () => {
    it('should assign single inspector to order', async () => {
      const orderId = 1;
      const assignData = { inspectorId: 1 };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Inspector assigned successfully',
        order: { id: 1, assignedInspectorIds: [1] },
      };

      mockOrdersService.assignInspector.mockResolvedValue(expectedResult);

      const result = await controller.assignInspector(orderId, assignData, user);

      expect(result).toEqual(expectedResult);
      expect(service.assignInspector).toHaveBeenCalledWith(orderId, assignData.inspectorId, user);
    });
  });

  describe('schedule', () => {
    it('should schedule an order', async () => {
      const orderId = 1;
      const scheduleData = { scheduleId: 1 };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Order scheduled successfully',
        order: { id: 1, status: OrderStatus.SCHEDULED },
      };

      mockOrdersService.scheduleInspection.mockResolvedValue(expectedResult);

      const result = await controller.schedule(orderId, scheduleData, user);

      expect(result).toEqual(expectedResult);
      expect(service.scheduleInspection).toHaveBeenCalledWith(orderId, scheduleData, user);
    });
  });

  describe('complete', () => {
    it('should complete an inspection', async () => {
      const orderId = 1;
      const completionData = { report: 'Inspection completed', notes: 'All good' };
      const user = { role: 'inspector', userId: 1 };
      const expectedResult = {
        message: 'Inspection completed successfully',
        order: { id: 1, status: OrderStatus.COMPLETED },
      };

      mockOrdersService.completeInspection.mockResolvedValue(expectedResult);

      const result = await controller.complete(orderId, completionData, user);

      expect(result).toEqual(expectedResult);
      expect(service.completeInspection).toHaveBeenCalledWith(orderId, completionData, user);
    });
  });

  describe('cancel', () => {
    it('should cancel an order', async () => {
      const orderId = 1;
      const cancelData = { reason: 'Client requested cancellation' };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Order cancelled successfully',
        order: { id: 1, status: OrderStatus.CANCELLED },
      };

      mockOrdersService.cancelOrder.mockResolvedValue(expectedResult);

      const result = await controller.cancel(orderId, cancelData, user);

      expect(result).toEqual(expectedResult);
      expect(service.cancelOrder).toHaveBeenCalledWith(orderId, cancelData.reason, user);
    });
  });

  describe('getStats', () => {
    it('should return order statistics', async () => {
      const expectedResult = {
        totalOrders: 100,
        ordersByStatus: [{ status: 'pending', count: 10 }],
        recentOrders: [],
      };

      mockOrdersService.getOrderStats.mockResolvedValue(expectedResult);

      const result = await controller.getStats();

      expect(result).toEqual(expectedResult);
      expect(service.getOrderStats).toHaveBeenCalled();
    });
  });

  describe('getClientOrders', () => {
    it('should return orders for authorized client', async () => {
      const clientId = 1;
      const query = { page: 1, limit: 10 };
      const user = { role: 'client', userId: 1 };
      const expectedResult = {
        orders: [{ id: 1, clientIds: [1] }],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      };

      mockOrdersService.getClientOrders.mockResolvedValue(expectedResult);

      const result = await controller.getClientOrders(clientId, query, user);

      expect(result).toEqual(expectedResult);
      expect(service.getClientOrders).toHaveBeenCalledWith(clientId, query);
    });

    it('should throw ForbiddenException for unauthorized client', async () => {
      const clientId = 2;
      const query = { page: 1, limit: 10 };
      const user = { role: 'client', userId: 1 };

      await expect(controller.getClientOrders(clientId, query, user)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('getInspectorOrders', () => {
    it('should return orders for authorized inspector', async () => {
      const inspectorId = 1;
      const query = { page: 1, limit: 10 };
      const user = { role: 'inspector', userId: 1 };
      const expectedResult = {
        orders: [{ id: 1, assignedInspectorIds: [1] }],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      };

      mockOrdersService.getInspectorOrders.mockResolvedValue(expectedResult);

      const result = await controller.getInspectorOrders(inspectorId, query, user);

      expect(result).toEqual(expectedResult);
      expect(service.getInspectorOrders).toHaveBeenCalledWith(inspectorId, query);
    });

    it('should throw ForbiddenException for unauthorized inspector', async () => {
      const inspectorId = 2;
      const query = { page: 1, limit: 10 };
      const user = { role: 'inspector', userId: 1 };

      await expect(controller.getInspectorOrders(inspectorId, query, user)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('addClient', () => {
    it('should add client to order', async () => {
      const orderId = 1;
      const clientData = { clientId: 2 };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Client added to order successfully',
        order: { id: 1, clientIds: [1, 2] },
      };

      mockOrdersService.addClientToOrder.mockResolvedValue(expectedResult);

      const result = await controller.addClient(orderId, clientData, user);

      expect(result).toEqual(expectedResult);
      expect(service.addClientToOrder).toHaveBeenCalledWith(orderId, clientData.clientId, user);
    });
  });

  describe('removeClient', () => {
    it('should remove client from order', async () => {
      const orderId = 1;
      const clientId = 2;
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Client removed from order successfully',
        order: { id: 1, clientIds: [1] },
      };

      mockOrdersService.removeClientFromOrder.mockResolvedValue(expectedResult);

      const result = await controller.removeClient(orderId, clientId, user);

      expect(result).toEqual(expectedResult);
      expect(service.removeClientFromOrder).toHaveBeenCalledWith(orderId, clientId, user);
    });
  });

  describe('updateClients', () => {
    it('should update order clients', async () => {
      const orderId = 1;
      const clientData = { clientIds: [1, 2, 3] };
      const user = { role: 'admin', userId: 1 };
      const expectedResult = {
        message: 'Order clients updated successfully',
        order: { id: 1, clientIds: [1, 2, 3] },
      };

      mockOrdersService.updateOrderClients.mockResolvedValue(expectedResult);

      const result = await controller.updateClients(orderId, clientData, user);

      expect(result).toEqual(expectedResult);
      expect(service.updateOrderClients).toHaveBeenCalledWith(orderId, clientData.clientIds, user);
    });
  });

  describe('getOrdersForClients', () => {
    it('should return orders for multiple clients', async () => {
      const query = { clientIds: '1,2,3', page: 1, limit: 10 };
      const expectedResult = {
        orders: [{ id: 1, clientIds: [1, 2] }],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      };

      mockOrdersService.getOrdersForClients.mockResolvedValue(expectedResult);

      const result = await controller.getOrdersForClients(query);

      expect(result).toEqual(expectedResult);
      expect(service.getOrdersForClients).toHaveBeenCalledWith([1, 2, 3], query);
    });
  });
});
