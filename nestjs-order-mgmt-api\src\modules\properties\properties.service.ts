import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';

import { Property } from './entities/property.entity';
import { PropertyTag } from './entities/property-tag.entity';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { PropertyQueryDto } from './dto/property-query.dto';
import { CreatePropertyTagDto } from './dto/create-property-tag.dto';

@Injectable()
export class PropertiesService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(PropertyTag)
    private readonly propertyTagRepository: Repository<PropertyTag>,
  ) {}

  async create(createPropertyDto: CreatePropertyDto) {
    const property = this.propertyRepository.create(createPropertyDto);
    const savedProperty = await this.propertyRepository.save(property);

    return {
      property: savedProperty,
      message: 'Property created successfully',
    };
  }

  async findAll(query: PropertyQueryDto) {
    const {
      page = 1,
      limit = 10,
      propertyType,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    const queryBuilder = this.propertyRepository.createQueryBuilder('property');

    // Apply filters
    if (propertyType) {
      queryBuilder.andWhere('property.propertyType = :propertyType', {
        propertyType,
      });
    }

    if (status) {
      queryBuilder.andWhere('property.status = :status', { status });
    }

    if (search) {
      queryBuilder.andWhere(
        '(property.addressLine1 ILIKE :search OR property.city ILIKE :search OR property.zipCode ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Include relations
    queryBuilder.leftJoinAndSelect('property.tags', 'tags');
    queryBuilder.leftJoinAndSelect('property.orders', 'orders');

    // Apply sorting
    queryBuilder.orderBy(`property.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [properties, total] = await queryBuilder.getManyAndCount();

    return {
      properties,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const property = await this.propertyRepository.findOne({
      where: { id },
      relations: ['tags', 'orders'],
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    return property;
  }

  async update(id: number, updatePropertyDto: UpdatePropertyDto) {
    const property = await this.propertyRepository.findOne({ where: { id } });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    await this.propertyRepository.update(id, updatePropertyDto);

    const updatedProperty = await this.findOne(id);
    return {
      property: updatedProperty,
      message: 'Property updated successfully',
    };
  }

  async remove(id: number) {
    const property = await this.propertyRepository.findOne({ where: { id } });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    await this.propertyRepository.remove(property);

    return {
      message: 'Property deleted successfully',
    };
  }

  async searchByAddress(searchTerm: string) {
    const properties = await this.propertyRepository.find({
      where: [
        { addressLine1: Like(`%${searchTerm}%`) },
        { city: Like(`%${searchTerm}%`) },
        { zipCode: Like(`%${searchTerm}%`) },
        { state: Like(`%${searchTerm}%`) },
      ],
      take: 10, // Limit results for autocomplete
    });

    return properties;
  }

  async findNearby(latitude: number, longitude: number, radius: number) {
    // This would typically use PostGIS for geographic queries
    // For now, return a placeholder implementation
    const properties = await this.propertyRepository.find({
      take: 20,
    });

    return {
      properties,
      center: { latitude, longitude },
      radius,
    };
  }

  async getPropertyOrders(id: number) {
    const property = await this.propertyRepository.findOne({
      where: { id },
      relations: ['orders'],
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    return {
      propertyId: id,
      orders: property.orders,
      totalOrders: property.orders.length,
    };
  }

  // Property Tags methods
  async createTag(createTagDto: CreatePropertyTagDto) {
    const tag = this.propertyTagRepository.create(createTagDto);
    const savedTag = await this.propertyTagRepository.save(tag);

    return {
      tag: savedTag,
      message: 'Property tag created successfully',
    };
  }

  async getTags() {
    const tags = await this.propertyTagRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });

    return tags;
  }

  async updatePropertyTags(propertyId: number, tagIds: number[]) {
    const property = await this.propertyRepository.findOne({
      where: { id: propertyId },
      relations: ['tags'],
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    const tags = await this.propertyTagRepository.find({
      where: { id: In(tagIds) },
    });

    property.tags = tags;
    await this.propertyRepository.save(property);

    return {
      message: 'Property tags updated successfully',
      tags,
    };
  }

  async getPropertyStats() {
    const totalProperties = await this.propertyRepository.count();
    const propertiesByType = await this.propertyRepository
      .createQueryBuilder('property')
      .select('property.propertyType', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('property.propertyType')
      .getRawMany();

    const propertiesByStatus = await this.propertyRepository
      .createQueryBuilder('property')
      .select('property.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('property.status')
      .getRawMany();

    return {
      totalProperties,
      propertiesByType,
      propertiesByStatus,
    };
  }
}
