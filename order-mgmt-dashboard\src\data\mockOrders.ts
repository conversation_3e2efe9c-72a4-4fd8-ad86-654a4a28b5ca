import { Order, OrdersApiResponse, StatusType, PropertyType } from '@/types/order';

// Mock data for orders
const mockOrders: Order[] = [
  {
    id: 1,
    inspectionOrderId: "ORD-10001",
    clientName: "<PERSON>",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "123 Main St, Anytown, CA 90210",
    propertyType: "single",
    status: "completed",
    inspectionDate: "2025-05-10T14:00:00.000Z",
    inspectionFees: 350,
    assignedInspectorIds: [1, 2],
    createdAt: "2025-05-01T10:30:00.000Z",
    updatedAt: "2025-05-11T16:45:00.000Z"
  },
  {
    id: 2,
    inspectionOrderId: "ORD-10002",
    clientName: "<PERSON>",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "456 Oak Ave, Somewhere, CA 90211",
    propertyType: "multi",
    status: "scheduled",
    inspectionDate: "2025-05-20T10:00:00.000Z",
    inspectionFees: 450,
    assignedInspectorIds: [3],
    createdAt: "2025-05-05T09:15:00.000Z",
    updatedAt: "2025-05-05T09:15:00.000Z"
  },
  {
    id: 3,
    inspectionOrderId: "ORD-10003",
    clientName: "Robert Johnson",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "789 Pine St, Elsewhere, CA 90212",
    propertyType: "commercial",
    status: "pending",
    inspectionDate: "2025-05-25T13:30:00.000Z",
    inspectionFees: 650,
    assignedInspectorIds: [1, 4],
    createdAt: "2025-05-08T14:20:00.000Z",
    updatedAt: "2025-05-08T14:20:00.000Z"
  },
  {
    id: 4,
    inspectionOrderId: "ORD-10004",
    clientName: "Emily Wilson",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "101 Maple Dr, Nowhere, CA 90213",
    propertyType: "single",
    status: "in progress",
    inspectionDate: "2025-05-18T11:00:00.000Z",
    inspectionFees: 325,
    assignedInspectorIds: [2],
    createdAt: "2025-05-07T16:40:00.000Z",
    updatedAt: "2025-05-18T12:30:00.000Z"
  },
  {
    id: 5,
    inspectionOrderId: "ORD-10005",
    clientName: "Michael Brown",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "202 Cedar Ln, Someplace, CA 90214",
    propertyType: "land",
    status: "cancelled",
    inspectionDate: "2025-05-15T09:00:00.000Z",
    inspectionFees: 275,
    assignedInspectorIds: [],
    createdAt: "2025-05-03T11:25:00.000Z",
    updatedAt: "2025-05-10T08:15:00.000Z"
  },
  {
    id: 6,
    inspectionOrderId: "ORD-10006",
    clientName: "Sarah Davis",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "303 Birch Rd, Anywhere, CA 90215",
    propertyType: "single",
    status: "reported",
    inspectionDate: "2025-05-12T15:30:00.000Z",
    inspectionFees: 350,
    assignedInspectorIds: [3],
    createdAt: "2025-05-02T13:10:00.000Z",
    updatedAt: "2025-05-13T17:45:00.000Z"
  },
  {
    id: 7,
    inspectionOrderId: "ORD-10007",
    clientName: "David Miller",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "404 Elm St, Somewhere Else, CA 90216",
    propertyType: "multi",
    status: "inspected",
    inspectionDate: "2025-05-16T10:30:00.000Z",
    inspectionFees: 475,
    assignedInspectorIds: [1],
    createdAt: "2025-05-04T10:05:00.000Z",
    updatedAt: "2025-05-16T14:20:00.000Z"
  },
  {
    id: 8,
    inspectionOrderId: "ORD-10008",
    clientName: "Jennifer Taylor",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "505 Walnut Ave, Nowhere Else, CA 90217",
    propertyType: "commercial",
    status: "scheduled",
    inspectionDate: "2025-05-22T13:00:00.000Z",
    inspectionFees: 600,
    assignedInspectorIds: [2, 4],
    createdAt: "2025-05-09T15:30:00.000Z",
    updatedAt: "2025-05-09T15:30:00.000Z"
  },
  {
    id: 9,
    inspectionOrderId: "ORD-10009",
    clientName: "Thomas Anderson",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "606 Cherry St, Anyplace, CA 90218",
    propertyType: "single",
    status: "pending",
    inspectionDate: "2025-05-28T09:30:00.000Z",
    inspectionFees: 325,
    assignedInspectorIds: [3],
    createdAt: "2025-05-10T09:45:00.000Z",
    updatedAt: "2025-05-10T09:45:00.000Z"
  },
  {
    id: 10,
    inspectionOrderId: "ORD-10010",
    clientName: "Lisa Johnson",
    clientPhone: "+****************",
    clientEmail: "<EMAIL>",
    propertyAddress: "707 Spruce Dr, Someplace Else, CA 90219",
    propertyType: "other",
    status: "completed",
    inspectionDate: "2025-05-08T14:00:00.000Z",
    inspectionFees: 400,
    assignedInspectorIds: [4],
    createdAt: "2025-04-30T11:20:00.000Z",
    updatedAt: "2025-05-09T16:10:00.000Z"
  }
];

// Function to create a mock API response with pagination
export const getMockOrdersResponse = (page: number = 1, pageSize: number = 10): OrdersApiResponse => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedOrders = mockOrders.slice(startIndex, endIndex);
  const total = mockOrders.length;
  const totalPages = Math.ceil(total / pageSize);

  return {
    items: paginatedOrders,
    pagination: {
      page,
      pageSize,
      total,
      totalPages
    }
  };
};

export default mockOrders;
