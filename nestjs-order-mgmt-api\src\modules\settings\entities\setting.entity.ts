import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum SettingType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  JSON = 'json',
  EMAIL = 'email',
  URL = 'url',
  PASSWORD = 'password',
  FILE = 'file',
}

export enum SettingCategory {
  GENERAL = 'general',
  EMAIL = 'email',
  NOTIFICATION = 'notification',
  PAYMENT = 'payment',
  SECURITY = 'security',
  INTEGRATION = 'integration',
  APPEARANCE = 'appearance',
  BUSINESS = 'business',
}

@Entity('settings')
export class Setting {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, length: 100 })
  key: string;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SettingType,
    default: SettingType.STRING,
  })
  type: SettingType;

  @Column({
    type: 'enum',
    enum: SettingCategory,
    default: SettingCategory.GENERAL,
  })
  category: SettingCategory;

  @Column({ type: 'text', nullable: true })
  value: string;

  @Column({ type: 'text', nullable: true })
  defaultValue: string;

  @Column({ type: 'jsonb', nullable: true })
  options: {
    min?: number;
    max?: number;
    choices?: { value: string; label: string }[];
    placeholder?: string;
    helpText?: string;
    validation?: string;
    encrypted?: boolean;
  };

  @Column({ default: false })
  isRequired: boolean;

  @Column({ default: false })
  isSecret: boolean;

  @Column({ default: true })
  isEditable: boolean;

  @Column({ default: true })
  isVisible: boolean;

  @Column({ default: 0 })
  sortOrder: number;

  @Column({ type: 'text', nullable: true })
  group: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
