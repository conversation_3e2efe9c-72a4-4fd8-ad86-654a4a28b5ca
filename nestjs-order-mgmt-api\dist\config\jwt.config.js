"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshTokenConfig = exports.jwtConfig = void 0;
const jwtConfig = () => ({
    secret: process.env.JWT_SECRET || 'your-secret-key-at-least-32-chars-long',
    signOptions: {
        expiresIn: process.env.JWT_EXPIRES_IN || '15m',
    },
});
exports.jwtConfig = jwtConfig;
const refreshTokenConfig = () => ({
    secret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
    signOptions: {
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    },
});
exports.refreshTokenConfig = refreshTokenConfig;
//# sourceMappingURL=jwt.config.js.map