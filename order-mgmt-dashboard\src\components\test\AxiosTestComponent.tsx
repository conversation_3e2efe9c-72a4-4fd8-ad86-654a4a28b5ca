import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { apiClient, jwtAxios } from '@/lib/api-client';
import { toast } from 'sonner';

export default function AxiosTestComponent() {
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<string>('');

  const testApiCall = async () => {
    setLoading(true);
    setResponse('');
    
    try {
      // Test a simple API call
      const result = await apiClient('orders?page=1&pageSize=5');
      setResponse(JSON.stringify(result, null, 2));
      toast.success('API call successful!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setResponse(`Error: ${errorMessage}`);
      toast.error('API call failed');
    } finally {
      setLoading(false);
    }
  };

  const testDirectAxios = async () => {
    setLoading(true);
    setResponse('');
    
    try {
      // Test direct axios call
      const result = await jwtAxios.get('orders?page=1&pageSize=5');
      setResponse(JSON.stringify(result.data, null, 2));
      toast.success('Direct axios call successful!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setResponse(`Error: ${errorMessage}`);
      toast.error('Direct axios call failed');
    } finally {
      setLoading(false);
    }
  };

  const test401Response = async () => {
    setLoading(true);
    setResponse('');
    
    try {
      // Test 401 response to trigger token refresh
      const result = await jwtAxios.get('test-401-endpoint');
      setResponse(JSON.stringify(result.data, null, 2));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setResponse(`Error (expected): ${errorMessage}`);
      toast.info('401 test completed - check console for interceptor logs');
    } finally {
      setLoading(false);
    }
  };

  const clearTokens = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    toast.info('Tokens cleared');
  };

  const setMockTokens = () => {
    localStorage.setItem('access_token', 'mock-access-token');
    localStorage.setItem('refresh_token', 'mock-refresh-token');
    toast.info('Mock tokens set');
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Axios & JWT Interceptor Test</CardTitle>
        <CardDescription>
          Test the axios implementation and JWT token refresh functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <Button onClick={testApiCall} disabled={loading}>
            Test API Client
          </Button>
          <Button onClick={testDirectAxios} disabled={loading} variant="outline">
            Test Direct Axios
          </Button>
          <Button onClick={test401Response} disabled={loading} variant="secondary">
            Test 401 Response
          </Button>
          <Button onClick={clearTokens} disabled={loading} variant="destructive">
            Clear Tokens
          </Button>
          <Button onClick={setMockTokens} disabled={loading} variant="default">
            Set Mock Tokens
          </Button>
        </div>
        
        {loading && (
          <div className="text-center py-4">
            <p>Loading...</p>
          </div>
        )}
        
        {response && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">Response:</h3>
            <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto max-h-96">
              {response}
            </pre>
          </div>
        )}
        
        <div className="mt-4 p-4 bg-blue-50 rounded-md">
          <h4 className="font-semibold text-blue-800 mb-2">How to test:</h4>
          <ol className="text-sm text-blue-700 space-y-1">
            <li>1. Click "Set Mock Tokens" to add tokens to localStorage</li>
            <li>2. Click "Test API Client" to test the apiClient function</li>
            <li>3. Click "Test Direct Axios" to test direct axios calls</li>
            <li>4. Click "Test 401 Response" to simulate token refresh (will fail but shows interceptor working)</li>
            <li>5. Check browser console for detailed logs</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
