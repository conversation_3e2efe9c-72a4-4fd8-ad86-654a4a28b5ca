import { CronjobsService } from './cronjobs.service';
import { CreateCronjobDto } from './dto/create-cronjob.dto';
import { UpdateCronjobDto } from './dto/update-cronjob.dto';
import { CronjobQueryDto } from './dto/cronjob-query.dto';
export declare class CronjobsController {
    private readonly cronjobsService;
    constructor(cronjobsService: CronjobsService);
    create(createCronjobDto: CreateCronjobDto): Promise<{
        cronjob: import("./entities/cronjob.entity").Cronjob[];
        message: string;
    }>;
    findAll(query: CronjobQueryDto): Promise<{
        cronjobs: import("./entities/cronjob.entity").Cronjob[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getSystemStatus(): Promise<{
        totalJobs: number;
        activeJobs: number;
        runningJobs: number;
        failedJobs: number;
        recentExecutions: import("./entities/cronjob.entity").Cronjob[];
        systemHealth: string;
    }>;
    getLogs(jobId?: number, limit?: number): Promise<{
        logs: import("./entities/cronjob.entity").Cronjob[];
        total: number;
    }>;
    findOne(id: number): Promise<import("./entities/cronjob.entity").Cronjob>;
    update(id: number, updateCronjobDto: UpdateCronjobDto): Promise<{
        cronjob: import("./entities/cronjob.entity").Cronjob;
        message: string;
    }>;
    enable(id: number): Promise<{
        message: string;
    }>;
    disable(id: number): Promise<{
        message: string;
    }>;
    runJob(id: number): Promise<{
        message: string;
        result: any;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getExecutionHistory(id: number, limit?: number): Promise<{
        cronjobId: number;
        cronjobName: any;
        totalRuns: any;
        totalFailures: any;
        lastRun: any;
        lastCompleted: any;
        lastError: any;
        successRate: string;
    }>;
    cleanupLogs(cleanupData: {
        olderThanDays: number;
    }): Promise<{
        message: string;
        affectedRows: number;
    }>;
    getAvailableJobTypes(): Promise<{
        value: unknown;
        label: any;
    }[]>;
}
