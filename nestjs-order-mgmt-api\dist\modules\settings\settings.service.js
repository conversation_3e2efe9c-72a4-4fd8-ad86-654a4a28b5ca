"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const crypto = require("crypto");
const setting_entity_1 = require("./entities/setting.entity");
let SettingsService = class SettingsService {
    constructor(settingRepository) {
        this.settingRepository = settingRepository;
        this.encryptionKey = process.env.SETTINGS_ENCRYPTION_KEY || 'default-key-change-in-production';
    }
    async create(createSettingDto) {
        const existingSetting = await this.settingRepository.findOne({
            where: { key: createSettingDto.key },
        });
        if (existingSetting) {
            throw new common_1.BadRequestException('Setting with this key already exists');
        }
        let value = createSettingDto.value;
        if (createSettingDto.isSecret && value) {
            value = this.encryptValue(value);
        }
        const setting = this.settingRepository.create({
            ...createSettingDto,
            value,
        });
        const savedSetting = await this.settingRepository.save(setting);
        return {
            setting: this.formatSetting(savedSetting),
            message: 'Setting created successfully',
        };
    }
    async findAll(query) {
        const { category, group, isVisible, search } = query;
        const queryBuilder = this.settingRepository.createQueryBuilder('setting');
        if (category) {
            queryBuilder.andWhere('setting.category = :category', { category });
        }
        if (group) {
            queryBuilder.andWhere('setting.group = :group', { group });
        }
        if (isVisible !== undefined) {
            queryBuilder.andWhere('setting.isVisible = :isVisible', { isVisible });
        }
        if (search) {
            queryBuilder.andWhere('(setting.name ILIKE :search OR setting.key ILIKE :search OR setting.description ILIKE :search)', { search: `%${search}%` });
        }
        queryBuilder.orderBy('setting.category', 'ASC');
        queryBuilder.addOrderBy('setting.sortOrder', 'ASC');
        queryBuilder.addOrderBy('setting.name', 'ASC');
        const settings = await queryBuilder.getMany();
        return {
            settings: settings.map(setting => this.formatSetting(setting)),
        };
    }
    async findOne(id) {
        const setting = await this.settingRepository.findOne({ where: { id } });
        if (!setting) {
            throw new common_1.NotFoundException('Setting not found');
        }
        return this.formatSetting(setting);
    }
    async getByKey(key) {
        const setting = await this.settingRepository.findOne({ where: { key } });
        if (!setting) {
            throw new common_1.NotFoundException('Setting not found');
        }
        return this.formatSetting(setting);
    }
    async getValue(key, defaultValue) {
        const setting = await this.settingRepository.findOne({ where: { key } });
        if (!setting) {
            return defaultValue;
        }
        return this.parseValue(setting);
    }
    async getByCategory(category) {
        const settings = await this.settingRepository.find({
            where: { category: category },
            order: { sortOrder: 'ASC', name: 'ASC' },
        });
        return {
            category,
            settings: settings.map(setting => this.formatSetting(setting)),
        };
    }
    async update(id, updateSettingDto) {
        const setting = await this.settingRepository.findOne({ where: { id } });
        if (!setting) {
            throw new common_1.NotFoundException('Setting not found');
        }
        let value = updateSettingDto.value;
        if (setting.isSecret && value) {
            value = this.encryptValue(value);
        }
        await this.settingRepository.update(id, {
            ...updateSettingDto,
            value,
        });
        const updatedSetting = await this.findOne(id);
        return {
            setting: updatedSetting,
            message: 'Setting updated successfully',
        };
    }
    async updateValue(key, value) {
        const setting = await this.settingRepository.findOne({ where: { key } });
        if (!setting) {
            throw new common_1.NotFoundException('Setting not found');
        }
        if (!setting.isEditable) {
            throw new common_1.BadRequestException('This setting is not editable');
        }
        const validation = this.validateSetting(key, value);
        if (!validation.isValid) {
            throw new common_1.BadRequestException(`Invalid value: ${validation.errors.join(', ')}`);
        }
        let encryptedValue = value;
        if (setting.isSecret && value) {
            encryptedValue = this.encryptValue(String(value));
        }
        await this.settingRepository.update({ key }, { value: encryptedValue });
        return {
            message: 'Setting value updated successfully',
            value: this.parseValue({ ...setting, value: encryptedValue }),
        };
    }
    async bulkUpdate(settings) {
        const results = [];
        for (const { key, value } of settings) {
            try {
                const result = await this.updateValue(key, value);
                results.push({ key, success: true, ...result });
            }
            catch (error) {
                results.push({ key, success: false, error: error.message });
            }
        }
        return {
            results,
            message: 'Bulk update completed',
        };
    }
    async resetToDefault(key) {
        const setting = await this.settingRepository.findOne({ where: { key } });
        if (!setting) {
            throw new common_1.NotFoundException('Setting not found');
        }
        await this.settingRepository.update({ key }, { value: setting.defaultValue });
        return {
            message: 'Setting reset to default value',
            value: this.parseValue({ ...setting, value: setting.defaultValue }),
        };
    }
    async remove(id) {
        const setting = await this.settingRepository.findOne({ where: { id } });
        if (!setting) {
            throw new common_1.NotFoundException('Setting not found');
        }
        await this.settingRepository.remove(setting);
        return {
            message: 'Setting deleted successfully',
        };
    }
    async getPublicSettings() {
        const settings = await this.settingRepository.find({
            where: { isVisible: true, isSecret: false },
            order: { category: 'ASC', sortOrder: 'ASC' },
        });
        const groupedSettings = settings.reduce((acc, setting) => {
            const category = setting.category;
            if (!acc[category]) {
                acc[category] = [];
            }
            acc[category].push({
                key: setting.key,
                name: setting.name,
                value: this.parseValue(setting),
                type: setting.type,
            });
            return acc;
        }, {});
        return groupedSettings;
    }
    async getCategories() {
        return Object.values(setting_entity_1.SettingCategory).map((category) => ({
            value: category,
            label: category.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
        }));
    }
    async createBackup() {
        const settings = await this.settingRepository.find();
        const backup = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            settings: settings.map(setting => ({
                key: setting.key,
                value: setting.isSecret ? '[ENCRYPTED]' : setting.value,
                type: setting.type,
                category: setting.category,
            })),
        };
        return {
            backup,
            message: 'Settings backup created successfully',
        };
    }
    async restoreBackup(backupData) {
        const { settings } = backupData;
        const results = [];
        for (const { key, value } of settings) {
            if (value !== '[ENCRYPTED]') {
                try {
                    await this.updateValue(key, value);
                    results.push({ key, success: true });
                }
                catch (error) {
                    results.push({ key, success: false, error: error.message });
                }
            }
        }
        return {
            results,
            message: 'Settings restore completed',
        };
    }
    validateSetting(key, value) {
        const errors = [];
        if (value === null || value === undefined) {
            return { isValid: true, errors: [] };
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    formatSetting(setting) {
        const formatted = {
            ...setting,
            value: this.parseValue(setting),
        };
        if (setting.isSecret) {
            formatted.value = '[HIDDEN]';
        }
        return formatted;
    }
    parseValue(setting) {
        if (!setting.value) {
            return setting.defaultValue ? this.parseValueByType(setting.defaultValue, setting.type) : null;
        }
        let value = setting.value;
        if (setting.isSecret) {
            try {
                value = this.decryptValue(value);
            }
            catch (error) {
                return null;
            }
        }
        return this.parseValueByType(value, setting.type);
    }
    parseValueByType(value, type) {
        switch (type) {
            case setting_entity_1.SettingType.BOOLEAN:
                return value === 'true';
            case setting_entity_1.SettingType.NUMBER:
                return parseFloat(value);
            case setting_entity_1.SettingType.JSON:
                try {
                    return JSON.parse(value);
                }
                catch {
                    return null;
                }
            default:
                return value;
        }
    }
    encryptValue(value) {
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(value, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }
    decryptValue(encryptedValue) {
        const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
        let decrypted = decipher.update(encryptedValue, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
};
exports.SettingsService = SettingsService;
exports.SettingsService = SettingsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(setting_entity_1.Setting)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SettingsService);
//# sourceMappingURL=settings.service.js.map