"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.InspectorsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const inspectors_service_1 = require("./inspectors.service");
const create_inspector_dto_1 = require("./dto/create-inspector.dto");
const update_inspector_dto_1 = require("./dto/update-inspector.dto");
const inspector_query_dto_1 = require("./dto/inspector-query.dto");
const auth_decorator_1 = require("../../common/decorators/auth.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let InspectorsController = class InspectorsController {
    constructor(inspectorsService) {
        this.inspectorsService = inspectorsService;
    }
    async create(createInspectorDto, user) {
        return this.inspectorsService.create(createInspectorDto);
    }
    async findAll(query) {
        return this.inspectorsService.findAll(query);
    }
    async findAvailable(date, startTime, endTime) {
        return this.inspectorsService.findAvailable(date, startTime, endTime);
    }
    async findOne(id) {
        return this.inspectorsService.findOne(id);
    }
    async update(id, updateInspectorDto) {
        return this.inspectorsService.update(id, updateInspectorDto);
    }
    async remove(id) {
        return this.inspectorsService.remove(id);
    }
    async getSchedule(id, startDate, endDate) {
        return this.inspectorsService.getSchedule(id, startDate, endDate);
    }
    async getStats(id) {
        return this.inspectorsService.getStats(id);
    }
};
exports.InspectorsController = InspectorsController;
__decorate([
    (0, common_1.Post)(),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new inspector' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Inspector successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_inspector_dto_1.CreateInspectorDto !== "undefined" && create_inspector_dto_1.CreateInspectorDto) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all inspectors with filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Inspectors retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'isActive', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'isAvailable', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof inspector_query_dto_1.InspectorQueryDto !== "undefined" && inspector_query_dto_1.InspectorQueryDto) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('available'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available inspectors for a specific date/time' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Available inspectors retrieved' }),
    (0, swagger_1.ApiQuery)({ name: 'date', required: true, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'startTime', required: true, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endTime', required: true, type: String }),
    __param(0, (0, common_1.Query)('date')),
    __param(1, (0, common_1.Query)('startTime')),
    __param(2, (0, common_1.Query)('endTime')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "findAvailable", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Get inspector by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Inspector retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Inspector not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update inspector' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Inspector updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Inspector not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, typeof (_d = typeof update_inspector_dto_1.UpdateInspectorDto !== "undefined" && update_inspector_dto_1.UpdateInspectorDto) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete inspector' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Inspector deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Inspector not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id/schedule'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Get inspector schedule' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Inspector schedule retrieved' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "getSchedule", null);
__decorate([
    (0, common_1.Get)(':id/stats'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Get inspector statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Inspector statistics retrieved' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InspectorsController.prototype, "getStats", null);
exports.InspectorsController = InspectorsController = __decorate([
    (0, swagger_1.ApiTags)('Inspectors'),
    (0, common_1.Controller)('inspectors'),
    __metadata("design:paramtypes", [typeof (_a = typeof inspectors_service_1.InspectorsService !== "undefined" && inspectors_service_1.InspectorsService) === "function" ? _a : Object])
], InspectorsController);
//# sourceMappingURL=inspectors.controller.js.map