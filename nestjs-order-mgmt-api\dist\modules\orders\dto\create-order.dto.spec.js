"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const create_order_dto_1 = require("./create-order.dto");
describe('CreateOrderDto', () => {
    describe('Validation', () => {
        it('should pass validation with valid data', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                squareFootage: 2000,
                inspectionFee: 500,
                clientIds: [1, 2],
                assignedInspectorIds: [1],
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should fail validation with missing required fields', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {});
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const errorMessages = errors.map(error => Object.keys(error.constraints || {})).flat();
            expect(errorMessages).toContain('isEnum');
            expect(errorMessages).toContain('isNotEmpty');
        });
        it('should fail validation with invalid property type', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'invalid_type',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const propertyTypeError = errors.find(error => error.property === 'propertyType');
            expect(propertyTypeError).toBeDefined();
            expect(propertyTypeError?.constraints).toHaveProperty('isEnum');
        });
        it('should fail validation with invalid email format', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                contactEmail: 'invalid-email',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            const emailError = errors.find(error => error.property === 'contactEmail');
            if (emailError) {
                expect(emailError.constraints).toHaveProperty('isEmail');
            }
        });
        it('should fail validation with negative inspection fee', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                inspectionFee: -100,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            const feeError = errors.find(error => error.property === 'inspectionFee');
            if (feeError) {
                expect(feeError.constraints).toHaveProperty('min');
            }
        });
        it('should fail validation with invalid square footage', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                squareFootage: -500,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            const squareFootageError = errors.find(error => error.property === 'squareFootage');
            if (squareFootageError) {
                expect(squareFootageError.constraints).toHaveProperty('min');
            }
        });
        it('should pass validation with optional fields', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                addressLine2: 'Apt 4B',
                country: 'USA',
                yearBuilt: 1995,
                bedrooms: 3,
                bathrooms: 2,
                stories: 2,
                garageSpaces: 2,
                specialInstructions: 'Call before arrival',
                accessInstructions: 'Key under mat',
                alarmCode: '1234',
                note: 'Rush order',
                travelFee: 50,
                additionalFees: 25,
                processingFee: 15,
                clientIds: [1],
                assignedInspectorIds: [1, 2],
                propertyId: 1,
                clientId: 1,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should validate array fields correctly', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                clientIds: [1, 2, 3],
                assignedInspectorIds: [1, 2],
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.clientIds).toEqual([1, 2, 3]);
            expect(dto.assignedInspectorIds).toEqual([1, 2]);
        });
        it('should fail validation with invalid array elements', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                clientIds: ['invalid', 'ids'],
                assignedInspectorIds: ['not', 'numbers'],
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const clientIdsError = errors.find(error => error.property === 'clientIds');
            const inspectorIdsError = errors.find(error => error.property === 'assignedInspectorIds');
            if (clientIdsError) {
                expect(clientIdsError.constraints).toHaveProperty('isNumber');
            }
            if (inspectorIdsError) {
                expect(inspectorIdsError.constraints).toHaveProperty('isNumber');
            }
        });
        it('should validate year built range', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                yearBuilt: 1800,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            const yearBuiltError = errors.find(error => error.property === 'yearBuilt');
            if (yearBuiltError) {
                expect(yearBuiltError.constraints).toHaveProperty('min');
            }
        });
        it('should validate bathroom count', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                bathrooms: 2.5,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.bathrooms).toBe(2.5);
        });
        it('should handle empty arrays', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                clientIds: [],
                assignedInspectorIds: [],
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.clientIds).toEqual([]);
            expect(dto.assignedInspectorIds).toEqual([]);
        });
    });
    describe('Data Transformation', () => {
        it('should transform string numbers to actual numbers', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                squareFootage: '2000',
                inspectionFee: '500.50',
                yearBuilt: '1995',
                bedrooms: '3',
                bathrooms: '2.5',
            });
            expect(typeof dto.squareFootage).toBe('number');
            expect(typeof dto.inspectionFee).toBe('number');
            expect(typeof dto.yearBuilt).toBe('number');
            expect(typeof dto.bedrooms).toBe('number');
            expect(typeof dto.bathrooms).toBe('number');
            expect(dto.squareFootage).toBe(2000);
            expect(dto.inspectionFee).toBe(500.50);
            expect(dto.yearBuilt).toBe(1995);
            expect(dto.bedrooms).toBe(3);
            expect(dto.bathrooms).toBe(2.5);
        });
        it('should handle legacy clientId conversion', async () => {
            const dto = (0, class_transformer_1.plainToClass)(create_order_dto_1.CreateOrderDto, {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                clientId: 1,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.clientId).toBe(1);
        });
    });
});
//# sourceMappingURL=create-order.dto.spec.js.map