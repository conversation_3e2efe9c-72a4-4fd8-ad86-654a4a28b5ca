import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    create(createOrderDto: CreateOrderDto, user: any): Promise<{
        order: import("./entities/order.entity").Order;
        message: string;
    }>;
    findAll(query: OrderQueryDto, user: any): Promise<{
        orders: import("./entities/order.entity").Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number, user: any): Promise<import("./entities/order.entity").Order>;
    update(id: number, updateOrderDto: UpdateOrderDto, user: any): Promise<{
        order: import("./entities/order.entity").Order;
        message: string;
    }>;
    remove(id: number, user: any): Promise<{
        message: string;
    }>;
    assignInspectors(id: number, assignData: {
        inspectorIds: number[];
    }, user: any): Promise<{
        message: string;
        order: import("./entities/order.entity").Order;
    }>;
    assignInspector(id: number, assignData: {
        inspectorId: number;
    }, user: any): Promise<{
        message: string;
        order: import("./entities/order.entity").Order;
    }>;
    schedule(id: number, scheduleData: any, user: any): Promise<{
        message: string;
        order: import("./entities/order.entity").Order;
    }>;
    complete(id: number, completionData: any, user: any): Promise<{
        message: string;
        order: import("./entities/order.entity").Order;
    }>;
    cancel(id: number, cancelData: {
        reason: string;
    }, user: any): Promise<{
        message: string;
        order: import("./entities/order.entity").Order;
    }>;
    getStats(): Promise<{
        totalOrders: number;
        ordersByStatus: any[];
        recentOrders: import("./entities/order.entity").Order[];
    }>;
    getClientOrders(clientId: number, query: any, user: any): Promise<{
        orders: import("./entities/order.entity").Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getInspectorOrders(inspectorId: number, query: any, user: any): Promise<{
        orders: import("./entities/order.entity").Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
}
