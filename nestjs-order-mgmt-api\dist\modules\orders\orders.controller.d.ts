import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    create(createOrderDto: CreateOrderDto, user: any): Promise<{
        order: import("./entities/order.entity").Order;
        message: string;
    }>;
    findAll(query: OrderQueryDto, user: any): Promise<{
        orders: import("./entities/order.entity").Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number, user: any): Promise<import("./entities/order.entity").Order>;
    update(id: number, updateOrderDto: UpdateOrderDto, user: any): Promise<{
        order: import("./entities/order.entity").Order;
        message: string;
    }>;
    remove(id: number, user: any): Promise<{
        message: string;
    }>;
    schedule(id: number, scheduleData: any, user: any): Promise<any>;
    getHistory(id: number, user: any): Promise<any>;
}
