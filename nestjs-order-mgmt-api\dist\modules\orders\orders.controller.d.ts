import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    create(createOrderDto: CreateOrderDto, user: any): Promise<any>;
    findAll(query: OrderQueryDto, user: any): Promise<any>;
    findOne(id: number, user: any): Promise<any>;
    update(id: number, updateOrderDto: UpdateOrderDto, user: any): Promise<any>;
    remove(id: number, user: any): Promise<any>;
    schedule(id: number, scheduleData: any, user: any): Promise<any>;
    getHistory(id: number, user: any): Promise<any>;
}
