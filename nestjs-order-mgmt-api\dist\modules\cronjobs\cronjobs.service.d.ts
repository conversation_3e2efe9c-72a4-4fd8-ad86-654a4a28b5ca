import { Repository } from 'typeorm';
import { SchedulerRegistry } from '@nestjs/schedule';
import { Cronjob } from './entities/cronjob.entity';
import { CreateCronjobDto } from './dto/create-cronjob.dto';
import { UpdateCronjobDto } from './dto/update-cronjob.dto';
import { CronjobQueryDto } from './dto/cronjob-query.dto';
import { TaskSchedulerService } from './task-scheduler.service';
export declare class CronjobsService {
    private readonly cronjobRepository;
    private readonly schedulerRegistry;
    private readonly taskSchedulerService;
    private readonly logger;
    constructor(cronjobRepository: Repository<Cronjob>, schedulerRegistry: SchedulerRegistry, taskSchedulerService: TaskSchedulerService);
    create(createCronjobDto: CreateCronjobDto): Promise<{
        cronjob: Cronjob[];
        message: string;
    }>;
    findAll(query: CronjobQueryDto): Promise<{
        cronjobs: Cronjob[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<Cronjob>;
    update(id: number, updateCronjobDto: UpdateCronjobDto): Promise<{
        cronjob: Cronjob;
        message: string;
    }>;
    updateStatus(id: number, isActive: boolean): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    runJob(id: number): Promise<{
        message: string;
        result: any;
    }>;
    getSystemStatus(): Promise<{
        totalJobs: number;
        activeJobs: number;
        runningJobs: number;
        failedJobs: number;
        recentExecutions: Cronjob[];
        systemHealth: string;
    }>;
    getLogs(jobId?: number, limit?: number): Promise<{
        logs: Cronjob[];
        total: number;
    }>;
    getExecutionHistory(id: number, limit?: number): Promise<{
        cronjobId: number;
        cronjobName: any;
        totalRuns: any;
        totalFailures: any;
        lastRun: any;
        lastCompleted: any;
        lastError: any;
        successRate: string;
    }>;
    cleanupLogs(olderThanDays: number): Promise<{
        message: string;
        affectedRows: number;
    }>;
    getAvailableJobTypes(): Promise<{
        value: unknown;
        label: any;
    }[]>;
    private initializeActiveCronjobs;
    private scheduleJob;
    private unscheduleJob;
}
