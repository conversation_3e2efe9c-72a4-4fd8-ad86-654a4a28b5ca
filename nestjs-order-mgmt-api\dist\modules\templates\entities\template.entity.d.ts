export declare enum TemplateType {
    EMAIL = "email",
    SMS = "sms",
    NOTIFICATION = "notification",
    REPORT = "report",
    DOCUMENT = "document"
}
export declare enum TemplateCategory {
    ORDER_CONFIRMATION = "order_confirmation",
    SCHEDULE_NOTIFICATION = "schedule_notification",
    INSPECTION_REMINDER = "inspection_reminder",
    COMPLETION_NOTICE = "completion_notice",
    CANCELLATION_NOTICE = "cancellation_notice",
    PAYMENT_REMINDER = "payment_reminder",
    WELCOME_MESSAGE = "welcome_message",
    CUSTOM = "custom"
}
export declare class Template {
    id: number;
    name: string;
    subject: string;
    type: TemplateType;
    category: TemplateCategory;
    content: string;
    htmlContent: string;
    variables: {
        name: string;
        description: string;
        required: boolean;
        defaultValue?: string;
    }[];
    metadata: {
        description?: string;
        tags?: string[];
        version?: string;
        author?: string;
    };
    isActive: boolean;
    isDefault: boolean;
    language: string;
    previewText: string;
    styling: {
        backgroundColor?: string;
        textColor?: string;
        fontFamily?: string;
        fontSize?: string;
        customCss?: string;
    };
    createdAt: Date;
    updatedAt: Date;
}
