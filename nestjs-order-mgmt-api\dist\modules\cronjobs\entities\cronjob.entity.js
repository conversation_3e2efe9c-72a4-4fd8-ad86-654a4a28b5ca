"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cronjob = exports.CronjobType = exports.CronjobStatus = void 0;
const typeorm_1 = require("typeorm");
var CronjobStatus;
(function (CronjobStatus) {
    CronjobStatus["PENDING"] = "pending";
    CronjobStatus["RUNNING"] = "running";
    CronjobStatus["COMPLETED"] = "completed";
    CronjobStatus["FAILED"] = "failed";
    CronjobStatus["CANCELLED"] = "cancelled";
})(CronjobStatus || (exports.CronjobStatus = CronjobStatus = {}));
var CronjobType;
(function (CronjobType) {
    CronjobType["EMAIL"] = "email";
    CronjobType["NOTIFICATION"] = "notification";
    CronjobType["CLEANUP"] = "cleanup";
    CronjobType["REPORT"] = "report";
    CronjobType["SYNC"] = "sync";
})(CronjobType || (exports.CronjobType = CronjobType = {}));
let Cronjob = class Cronjob {
};
exports.Cronjob = Cronjob;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Cronjob.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CronjobType,
    }),
    __metadata("design:type", String)
], Cronjob.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CronjobStatus,
        default: CronjobStatus.PENDING,
    }),
    __metadata("design:type", String)
], Cronjob.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], Cronjob.prototype, "payload", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Cronjob.prototype, "scheduledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Cronjob.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Cronjob.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Cronjob.prototype, "attempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 3 }),
    __metadata("design:type", Number)
], Cronjob.prototype, "maxAttempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Cronjob.prototype, "error", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Cronjob.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Cronjob.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Cronjob.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Cronjob.prototype, "updatedAt", void 0);
exports.Cronjob = Cronjob = __decorate([
    (0, typeorm_1.Entity)('cronjobs')
], Cronjob);
//# sourceMappingURL=cronjob.entity.js.map