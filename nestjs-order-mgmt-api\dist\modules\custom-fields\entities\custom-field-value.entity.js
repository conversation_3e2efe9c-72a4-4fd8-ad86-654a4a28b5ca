"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomFieldValue = void 0;
const typeorm_1 = require("typeorm");
const custom_field_entity_1 = require("./custom-field.entity");
let CustomFieldValue = class CustomFieldValue {
};
exports.CustomFieldValue = CustomFieldValue;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CustomFieldValue.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CustomFieldValue.prototype, "customFieldId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], CustomFieldValue.prototype, "entityType", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CustomFieldValue.prototype, "entityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomFieldValue.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], CustomFieldValue.prototype, "jsonValue", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomFieldValue.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomFieldValue.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => custom_field_entity_1.CustomField, (field) => field.values),
    (0, typeorm_1.JoinColumn)({ name: 'customFieldId' }),
    __metadata("design:type", custom_field_entity_1.CustomField)
], CustomFieldValue.prototype, "customField", void 0);
exports.CustomFieldValue = CustomFieldValue = __decorate([
    (0, typeorm_1.Entity)('custom_field_values'),
    (0, typeorm_1.Index)(['entityType', 'entityId', 'customFieldId'], { unique: true })
], CustomFieldValue);
//# sourceMappingURL=custom-field-value.entity.js.map