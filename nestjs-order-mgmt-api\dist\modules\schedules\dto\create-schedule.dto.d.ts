declare class RecurringPatternDto {
    frequency?: 'daily' | 'weekly' | 'monthly';
    interval?: number;
    daysOfWeek?: number[];
    endDate?: string;
}
export declare class CreateScheduleDto {
    inspectorId: number;
    date: string;
    startTime: string;
    endTime: string;
    available?: boolean;
    inspectionOrderId?: number;
    notes?: string;
    isRecurring?: boolean;
    recurringPattern?: RecurringPatternDto;
}
export {};
