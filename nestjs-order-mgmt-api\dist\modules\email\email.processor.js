"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const email_service_1 = require("./email.service");
let EmailProcessor = EmailProcessor_1 = class EmailProcessor {
    constructor(emailService) {
        this.emailService = emailService;
        this.logger = new common_1.Logger(EmailProcessor_1.name);
    }
    async handleSendEmail(job) {
        this.logger.log(`Processing email job ${job.id} to ${job.data.to}`);
        try {
            await job.progress(10);
            const result = await this.emailService.processEmail(job.data);
            await job.progress(100);
            this.logger.log(`Email job ${job.id} completed successfully`);
            return result;
        }
        catch (error) {
            this.logger.error(`Email job ${job.id} failed:`, error.stack);
            throw error;
        }
    }
    async handleBulkEmail(job) {
        this.logger.log(`Processing bulk email job ${job.id} with ${job.data.emails.length} emails`);
        const results = [];
        const total = job.data.emails.length;
        for (let i = 0; i < total; i++) {
            try {
                const result = await this.emailService.processEmail(job.data.emails[i]);
                results.push({ success: true, result });
                const progress = Math.round(((i + 1) / total) * 100);
                await job.progress(progress);
            }
            catch (error) {
                this.logger.error(`Failed to send email ${i + 1} in bulk job ${job.id}:`, error.stack);
                results.push({ success: false, error: error.message });
            }
        }
        this.logger.log(`Bulk email job ${job.id} completed. Success: ${results.filter(r => r.success).length}/${total}`);
        return results;
    }
};
exports.EmailProcessor = EmailProcessor;
__decorate([
    (0, bull_1.Process)('send-email'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailProcessor.prototype, "handleSendEmail", null);
__decorate([
    (0, bull_1.Process)('send-bulk-email'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailProcessor.prototype, "handleBulkEmail", null);
exports.EmailProcessor = EmailProcessor = EmailProcessor_1 = __decorate([
    (0, bull_1.Processor)('email'),
    __metadata("design:paramtypes", [email_service_1.EmailService])
], EmailProcessor);
//# sourceMappingURL=email.processor.js.map