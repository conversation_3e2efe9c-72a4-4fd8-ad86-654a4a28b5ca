import { InspectorsService } from './inspectors.service';
import { CreateInspectorDto } from './dto/create-inspector.dto';
import { UpdateInspectorDto } from './dto/update-inspector.dto';
import { InspectorQueryDto } from './dto/inspector-query.dto';
export declare class InspectorsController {
    private readonly inspectorsService;
    constructor(inspectorsService: InspectorsService);
    create(createInspectorDto: CreateInspectorDto, user: any): Promise<{
        inspector: import("./entities/inspector.entity").Inspector;
        message: string;
    }>;
    findAll(query: InspectorQueryDto): Promise<{
        inspectors: import("./entities/inspector.entity").Inspector[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findAvailable(date: string, startTime: string, endTime: string): Promise<{
        date: string;
        timeSlot: {
            startTime: string;
            endTime: string;
        };
        availableInspectors: any[];
    }>;
    findOne(id: number): Promise<import("./entities/inspector.entity").Inspector>;
    update(id: number, updateInspectorDto: UpdateInspectorDto): Promise<{
        inspector: import("./entities/inspector.entity").Inspector;
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getSchedule(id: number, startDate?: string, endDate?: string): Promise<{
        inspector: {
            id: number;
            name: string;
            email: string;
        };
        schedules: import("../schedules/entities/schedule.entity").Schedule[];
        period: {
            startDate: string;
            endDate: string;
        };
    }>;
    getStats(id: number): Promise<{
        inspector: {
            id: number;
            name: string;
            rating: number;
            completedInspections: number;
        };
        statistics: {
            totalSchedules: number;
            completedInspections: number;
            upcomingSchedules: number;
            averageRating: number;
            monthlyStats: any[];
        };
    }>;
}
