import { InspectorsService } from './inspectors.service';
import { CreateInspectorDto } from './dto/create-inspector.dto';
import { UpdateInspectorDto } from './dto/update-inspector.dto';
import { InspectorQueryDto } from './dto/inspector-query.dto';
export declare class InspectorsController {
    private readonly inspectorsService;
    constructor(inspectorsService: InspectorsService);
    create(createInspectorDto: CreateInspectorDto, user: any): Promise<any>;
    findAll(query: InspectorQueryDto): Promise<any>;
    findAvailable(date: string, startTime: string, endTime: string): Promise<any>;
    findOne(id: number): Promise<any>;
    update(id: number, updateInspectorDto: UpdateInspectorDto): Promise<any>;
    remove(id: number): Promise<any>;
    getSchedule(id: number, startDate?: string, endDate?: string): Promise<any>;
    getStats(id: number): Promise<any>;
}
