import { CustomFieldType, CustomFieldEntity } from '../entities/custom-field.entity';
declare class CustomFieldValidationDto {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: string;
}
declare class CustomFieldDisplayDto {
    width?: string;
    columns?: number;
    rows?: number;
}
declare class CustomFieldOptionsDto {
    choices?: {
        value: string;
        label: string;
    }[];
    placeholder?: string;
    helpText?: string;
    validation?: CustomFieldValidationDto;
    display?: CustomFieldDisplayDto;
}
export declare class CreateCustomFieldDto {
    name: string;
    key: string;
    description?: string;
    type: CustomFieldType;
    entityType: CustomFieldEntity;
    options?: CustomFieldOptionsDto;
    defaultValue?: string;
    isRequired?: boolean;
    isActive?: boolean;
    isVisible?: boolean;
    isSearchable?: boolean;
    sortOrder?: number;
    group?: string;
    permissions?: string[];
}
export {};
