import { z } from "zod";

// Property type options
const PROPERTY_TYPES = [
  "Single Family",
  "Duplex",
  "Triplex",
  "Quad-plex",
  "Other"
] as const;

// Foundation type options
const FOUNDATION_TYPES = [
  "Slab-on-grade",
  "Crawl Space",
  "Pier-and-beam"
] as const;

// Order status options
const ORDER_STATUS = [
  "pending",
  "scheduled",
  "in-progress",
  "completed",
  "cancelled"
] as const;

// Services schema
const servicesSchema = z.object({
  flexfund: z.boolean().default(false),
  mold: z.boolean().default(false),
});

// Main order form schema
export const orderFormSchema = z.object({
  // General section
  clientName: z.string().min(1, "Client name is required"),
  clientEmail: z.string().email("Invalid email address"),
  clientPhone: z.string().min(10, "Phone number must be at least 10 digits"),
  status: z.enum(ORDER_STATUS).default("pending"),

  // Property section
  propertyAddress: z.string().min(1, "Property address is required"),
  propertyType: z.string().min(1, "Property type is required"),
  yearBuilt: z.string().min(1, "Year built is required"),
  propertyAge: z.string().min(1, "Property age is required"),
  foundationType: z.string().min(1, "Foundatio nType is required"),
  gateCode: z.string().min(1, "Gate Code is required"),
  lockboxCode: z.string().min(1, "Lockbox Code is required"),
  mlsNumber: z.string().min(1, "MLS Number is required"),
  isClientAttending: z.boolean().default(false),
  isOccupied: z.boolean().default(false),
  hasUtilities: z.boolean().default(false),
  hasAlarm: z.boolean().default(false),

  // Services section
  services: servicesSchema,

  // Agents section
  agentName: z.string().min(1, "Agent name is required"),
  agentEmail: z.string().email("Invalid email address"),
  agentPhone: z.string().min(1, "Agent phone is required"),
  isSeller: z.boolean().default(false),
  isBuyer: z.boolean().default(false),

  // Fees section
  inspectionFee: z.string().optional(),
  thirdPartyFee: z.string().optional(),
  discountFee: z.string().optional(),
  processingFee: z.string().optional(),
});

export type OrderFormData = z.infer<typeof orderFormSchema>; 