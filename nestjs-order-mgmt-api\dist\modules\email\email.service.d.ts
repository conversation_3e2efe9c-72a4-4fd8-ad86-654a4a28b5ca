import { Queue } from 'bull';
import { SendEmailDto } from './dto/send-email.dto';
import { BulkEmailDto } from './dto/bulk-email.dto';
export interface EmailJob {
    to: string | string[];
    subject: string;
    text?: string;
    html?: string;
    template?: string;
    variables?: Record<string, any>;
    attachments?: any[];
    priority?: 'low' | 'normal' | 'high';
}
export declare class EmailService {
    private emailQueue;
    private readonly logger;
    private transporter;
    constructor(emailQueue: Queue);
    private initializeTransporter;
    sendEmail(sendEmailDto: SendEmailDto): Promise<{
        jobId: import("bull").JobId;
        message: string;
    }>;
    sendBulkEmail(bulkEmailDto: BulkEmailDto): Promise<{
        jobIds: import("bull").JobId[];
        totalEmails: number;
        message: string;
    }>;
    sendTemplateEmail(templateName: string, to: string | string[], variables: Record<string, any>): Promise<{
        jobId: import("bull").JobId;
        message: string;
    }>;
    getQueueStatus(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        total: number;
    }>;
    getJobStatus(jobId: string): Promise<{
        status: string;
        id?: undefined;
        progress?: undefined;
        data?: undefined;
        processedOn?: undefined;
        finishedOn?: undefined;
        failedReason?: undefined;
    } | {
        id: import("bull").JobId;
        status: import("bull").JobStatus | "stuck";
        progress: any;
        data: any;
        processedOn: number;
        finishedOn: number;
        failedReason: string;
    }>;
    retryFailedJobs(): Promise<{
        retriedJobs: number;
        message: string;
    }>;
    clearQueue(): Promise<{
        message: string;
    }>;
    processEmail(emailData: EmailJob): Promise<any>;
    private renderTemplate;
    private getPriorityValue;
    private getEmailTemplates;
}
