"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_entity_1 = require("./entities/schedule.entity");
const inspector_entity_1 = require("../inspectors/entities/inspector.entity");
const order_entity_1 = require("../orders/entities/order.entity");
let SchedulesService = class SchedulesService {
    constructor(scheduleRepository, inspectorRepository, orderRepository) {
        this.scheduleRepository = scheduleRepository;
        this.inspectorRepository = inspectorRepository;
        this.orderRepository = orderRepository;
    }
    async create(createScheduleDto, user) {
        const inspector = await this.inspectorRepository.findOne({
            where: { id: createScheduleDto.inspectorId },
        });
        if (!inspector) {
            throw new common_1.NotFoundException('Inspector not found');
        }
        const conflicts = await this.checkTimeConflicts(createScheduleDto.inspectorId, createScheduleDto.date, createScheduleDto.startTime, createScheduleDto.endTime);
        if (conflicts.length > 0) {
            throw new common_1.ConflictException('Schedule conflicts with existing schedules');
        }
        const dailyCount = await this.getDailyAssignmentCount(createScheduleDto.inspectorId, createScheduleDto.date);
        if (dailyCount >= 3) {
            throw new common_1.ConflictException('Inspector already has maximum assignments for this day');
        }
        const schedule = this.scheduleRepository.create(createScheduleDto);
        const savedSchedule = await this.scheduleRepository.save(schedule);
        return {
            schedule: await this.findOne(savedSchedule.id, user),
            message: 'Schedule created successfully',
        };
    }
    async bulkCreate(bulkCreateDto, user) {
        const results = [];
        for (const scheduleData of bulkCreateDto.schedules) {
            try {
                const result = await this.create(scheduleData, user);
                results.push({ success: true, schedule: result.schedule });
            }
            catch (error) {
                results.push({ success: false, error: error.message, data: scheduleData });
            }
        }
        return {
            results,
            message: 'Bulk schedule creation completed',
        };
    }
    async findAll(query, user) {
        const { page = 1, limit = 10, inspectorId, date, available, startDate, endDate, sortBy = 'date', sortOrder = 'ASC', } = query;
        const queryBuilder = this.scheduleRepository.createQueryBuilder('schedule');
        queryBuilder.leftJoinAndSelect('schedule.inspector', 'inspector');
        queryBuilder.leftJoinAndSelect('schedule.order', 'order');
        if (inspectorId) {
            queryBuilder.andWhere('schedule.inspectorId = :inspectorId', { inspectorId });
        }
        if (date) {
            queryBuilder.andWhere('schedule.date = :date', { date });
        }
        if (startDate && endDate) {
            queryBuilder.andWhere('schedule.date BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        if (available !== undefined) {
            queryBuilder.andWhere('schedule.available = :available', { available });
        }
        if (user.role === 'inspector') {
            queryBuilder.andWhere('schedule.inspectorId = :userId', { userId: user.userId });
        }
        queryBuilder.orderBy(`schedule.${sortBy}`, sortOrder);
        queryBuilder.addOrderBy('schedule.startTime', 'ASC');
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [schedules, total] = await queryBuilder.getManyAndCount();
        return {
            schedules,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id, user) {
        const schedule = await this.scheduleRepository.findOne({
            where: { id },
            relations: ['inspector', 'order'],
        });
        if (!schedule) {
            throw new common_1.NotFoundException('Schedule not found');
        }
        if (user.role === 'inspector' && schedule.inspectorId !== user.userId) {
            throw new common_1.ForbiddenException('You can only view your own schedules');
        }
        return schedule;
    }
    async update(id, updateScheduleDto, user) {
        const schedule = await this.findOne(id, user);
        if (user.role === 'inspector' && schedule.inspectorId !== user.userId) {
            throw new common_1.ForbiddenException('You can only update your own schedules');
        }
        if (updateScheduleDto.date || updateScheduleDto.startTime || updateScheduleDto.endTime) {
            const conflicts = await this.checkTimeConflicts(updateScheduleDto.inspectorId || schedule.inspectorId, updateScheduleDto.date || schedule.date, updateScheduleDto.startTime || schedule.startTime, updateScheduleDto.endTime || schedule.endTime, id);
            if (conflicts.length > 0) {
                throw new common_1.ConflictException('Schedule conflicts with existing schedules');
            }
        }
        await this.scheduleRepository.update(id, updateScheduleDto);
        const updatedSchedule = await this.findOne(id, user);
        return {
            schedule: updatedSchedule,
            message: 'Schedule updated successfully',
        };
    }
    async remove(id, user) {
        const schedule = await this.findOne(id, user);
        if (user.role === 'inspector' && schedule.inspectorId !== user.userId) {
            throw new common_1.ForbiddenException('You can only delete your own schedules');
        }
        if (schedule.inspectionOrderId) {
            throw new common_1.BadRequestException('Cannot delete schedule with assigned order');
        }
        await this.scheduleRepository.remove(schedule);
        return {
            message: 'Schedule deleted successfully',
        };
    }
    async checkConflicts(checkConflictDto) {
        const conflicts = await this.checkTimeConflicts(checkConflictDto.inspectorId, checkConflictDto.date, checkConflictDto.startTime, checkConflictDto.endTime, checkConflictDto.excludeScheduleId);
        return {
            hasConflicts: conflicts.length > 0,
            conflicts,
        };
    }
    async assignOrder(scheduleId, orderId, user) {
        const schedule = await this.findOne(scheduleId, user);
        const order = await this.orderRepository.findOne({ where: { id: orderId } });
        if (!order) {
            throw new common_1.NotFoundException('Order not found');
        }
        if (!schedule.available) {
            throw new common_1.BadRequestException('Schedule is not available for assignment');
        }
        schedule.inspectionOrderId = orderId;
        schedule.available = false;
        await this.scheduleRepository.save(schedule);
        return {
            message: 'Order assigned to schedule successfully',
            schedule: await this.findOne(scheduleId, user),
        };
    }
    async unassignOrder(scheduleId, user) {
        const schedule = await this.findOne(scheduleId, user);
        schedule.inspectionOrderId = null;
        schedule.available = true;
        await this.scheduleRepository.save(schedule);
        return {
            message: 'Order unassigned from schedule successfully',
            schedule: await this.findOne(scheduleId, user),
        };
    }
    async getInspectorAvailability(inspectorId, startDate, endDate) {
        const schedules = await this.scheduleRepository.find({
            where: {
                inspectorId,
                date: (0, typeorm_2.Between)(startDate, endDate),
            },
            order: { date: 'ASC', startTime: 'ASC' },
        });
        const availability = this.processAvailabilityData(schedules, startDate, endDate);
        return {
            inspectorId,
            startDate,
            endDate,
            availability,
        };
    }
    async getCalendarView(inspectorId, month, year) {
        const currentDate = new Date();
        const targetMonth = month ? parseInt(month) - 1 : currentDate.getMonth();
        const targetYear = year ? parseInt(year) : currentDate.getFullYear();
        const startDate = new Date(targetYear, targetMonth, 1).toISOString().split('T')[0];
        const endDate = new Date(targetYear, targetMonth + 1, 0).toISOString().split('T')[0];
        const schedules = await this.scheduleRepository.find({
            where: {
                inspectorId,
                date: (0, typeorm_2.Between)(startDate, endDate),
            },
            relations: ['order'],
            order: { date: 'ASC', startTime: 'ASC' },
        });
        return {
            month: targetMonth + 1,
            year: targetYear,
            schedules: this.formatCalendarData(schedules),
        };
    }
    async getScheduleStats() {
        const totalSchedules = await this.scheduleRepository.count();
        const availableSchedules = await this.scheduleRepository.count({
            where: { available: true },
        });
        const assignedSchedules = await this.scheduleRepository.count({
            where: { available: false },
        });
        const schedulesByInspector = await this.scheduleRepository
            .createQueryBuilder('schedule')
            .leftJoin('schedule.inspector', 'inspector')
            .select('inspector.name', 'inspectorName')
            .addSelect('COUNT(*)', 'scheduleCount')
            .groupBy('inspector.id, inspector.name')
            .getRawMany();
        return {
            totalSchedules,
            availableSchedules,
            assignedSchedules,
            schedulesByInspector,
        };
    }
    async createRecurringSchedules(recurringData, user) {
        const { inspectorId, startDate, endDate, pattern, timeSlots } = recurringData;
        const schedules = [];
        return {
            schedules,
            message: 'Recurring schedules created successfully',
        };
    }
    async checkTimeConflicts(inspectorId, date, startTime, endTime, excludeScheduleId) {
        const queryBuilder = this.scheduleRepository.createQueryBuilder('schedule');
        queryBuilder.where('schedule.inspectorId = :inspectorId', { inspectorId });
        queryBuilder.andWhere('schedule.date = :date', { date });
        queryBuilder.andWhere('(schedule.startTime < :endTime AND schedule.endTime > :startTime)', { startTime, endTime });
        if (excludeScheduleId) {
            queryBuilder.andWhere('schedule.id != :excludeScheduleId', { excludeScheduleId });
        }
        return queryBuilder.getMany();
    }
    async getDailyAssignmentCount(inspectorId, date) {
        return this.scheduleRepository.count({
            where: {
                inspectorId,
                date,
                available: false,
            },
        });
    }
    processAvailabilityData(schedules, startDate, endDate) {
        const availability = {};
        schedules.forEach(schedule => {
            if (!availability[schedule.date]) {
                availability[schedule.date] = [];
            }
            availability[schedule.date].push({
                id: schedule.id,
                startTime: schedule.startTime,
                endTime: schedule.endTime,
                available: schedule.available,
                hasOrder: !!schedule.inspectionOrderId,
            });
        });
        return availability;
    }
    formatCalendarData(schedules) {
        return schedules.map(schedule => ({
            id: schedule.id,
            date: schedule.date,
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            available: schedule.available,
            order: schedule.order ? {
                id: schedule.order.id,
                orderNumber: schedule.order.orderNumber,
                clientName: schedule.order.clientName,
            } : null,
        }));
    }
};
exports.SchedulesService = SchedulesService;
exports.SchedulesService = SchedulesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(schedule_entity_1.Schedule)),
    __param(1, (0, typeorm_1.InjectRepository)(inspector_entity_1.Inspector)),
    __param(2, (0, typeorm_1.InjectRepository)(order_entity_1.Order)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], SchedulesService);
//# sourceMappingURL=schedules.service.js.map