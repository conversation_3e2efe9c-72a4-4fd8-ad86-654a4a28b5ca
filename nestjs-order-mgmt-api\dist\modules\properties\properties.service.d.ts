import { Repository } from 'typeorm';
import { Property } from './entities/property.entity';
import { PropertyTag } from './entities/property-tag.entity';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { PropertyQueryDto } from './dto/property-query.dto';
import { CreatePropertyTagDto } from './dto/create-property-tag.dto';
export declare class PropertiesService {
    private readonly propertyRepository;
    private readonly propertyTagRepository;
    constructor(propertyRepository: Repository<Property>, propertyTagRepository: Repository<PropertyTag>);
    create(createPropertyDto: CreatePropertyDto): Promise<{
        property: Property;
        message: string;
    }>;
    findAll(query: PropertyQueryDto): Promise<{
        properties: Property[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<Property>;
    update(id: number, updatePropertyDto: UpdatePropertyDto): Promise<{
        property: Property;
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    searchByAddress(searchTerm: string): Promise<Property[]>;
    findNearby(latitude: number, longitude: number, radius: number): Promise<{
        properties: Property[];
        center: {
            latitude: number;
            longitude: number;
        };
        radius: number;
    }>;
    getPropertyOrders(id: number): Promise<{
        propertyId: number;
        orders: import("../orders/entities/order.entity").Order[];
        totalOrders: number;
    }>;
    createTag(createTagDto: CreatePropertyTagDto): Promise<{
        tag: PropertyTag;
        message: string;
    }>;
    getTags(): Promise<PropertyTag[]>;
    updatePropertyTags(propertyId: number, tagIds: number[]): Promise<{
        message: string;
        tags: PropertyTag[];
    }>;
    getPropertyStats(): Promise<{
        totalProperties: number;
        propertiesByType: any[];
        propertiesByStatus: any[];
    }>;
}
