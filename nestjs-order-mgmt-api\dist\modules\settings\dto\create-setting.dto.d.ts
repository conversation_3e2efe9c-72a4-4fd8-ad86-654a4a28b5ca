import { SettingType, SettingCategory } from '../entities/setting.entity';
declare class SettingOptionsDto {
    min?: number;
    max?: number;
    choices?: {
        value: string;
        label: string;
    }[];
    placeholder?: string;
    helpText?: string;
    validation?: string;
    encrypted?: boolean;
}
export declare class CreateSettingDto {
    key: string;
    name: string;
    description?: string;
    type: SettingType;
    category: SettingCategory;
    value?: string;
    defaultValue?: string;
    options?: SettingOptionsDto;
    isRequired?: boolean;
    isSecret?: boolean;
    isEditable?: boolean;
    isVisible?: boolean;
    sortOrder?: number;
    group?: string;
}
export {};
