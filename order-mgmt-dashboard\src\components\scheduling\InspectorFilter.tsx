import React from 'react';
import { Check } from 'lucide-react';

interface Inspector {
  id: number;
  name: string;
  color: string;
}

interface InspectorFilterProps {
  inspectors: Inspector[];
  selectedInspectors: number[];
  onSelectionChange: (selectedIds: number[]) => void;
}

const InspectorFilter: React.FC<InspectorFilterProps> = ({
  inspectors,
  selectedInspectors,
  onSelectionChange,
}) => {
  const allSelected = selectedInspectors.length === inspectors.length;

  const handleInspectorClick = (inspectorId: number) => {
    if (selectedInspectors.includes(inspectorId)) {
      onSelectionChange(selectedInspectors.filter(id => id !== inspectorId));
    } else {
      onSelectionChange([...selectedInspectors, inspectorId]);
    }
  };

  const handleSelectAll = () => {
    if (allSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(inspectors.map(inspector => inspector.id));
    }
  };

  return (
    <div>
      <div className="flex flex-wrap gap-2">
        <button
          onClick={handleSelectAll}
          className={`
            flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium
            transition-all shadow-sm
            ${allSelected
              ? 'bg-blue-100/70 text-blue-700 border border-blue-200/70'
              : 'bg-gray-50 text-gray-600 border border-gray-200/70 hover:bg-gray-100/70'
            }
          `}
        >
          <div className={`
            w-4 h-4 flex items-center justify-center rounded-sm border transition-colors
            ${allSelected
              ? 'bg-blue-500 border-blue-500 text-white'
              : 'border-gray-400 bg-white'
            }
          `}>
            {allSelected && <Check className="h-3 w-3" />}
          </div>
          <span className="ml-1">All</span>
        </button>

        {inspectors.map(inspector => {
          const isSelected = selectedInspectors.includes(inspector.id);
          return (
            <button
              key={inspector.id}
              onClick={() => handleInspectorClick(inspector.id)}
              className={`
                flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium
                transition-all shadow-sm
                ${isSelected
                  ? 'bg-blue-100/70 text-blue-700 border border-blue-200/70'
                  : 'bg-gray-50 text-gray-600 border border-gray-200/70 hover:bg-gray-100/70'
                }
              `}
            >
              <div className={`
                w-4 h-4 flex items-center justify-center rounded-sm border transition-colors
                ${isSelected
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'border-gray-400 bg-white'
                }
              `}>
                {isSelected && <Check className="h-3 w-3" />}
              </div>
              <span className="flex items-center gap-1.5 ml-1">
                <span className={`w-4 h-4 rounded-full ${inspector.color} shadow-sm`}></span>
                {inspector.name}
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default InspectorFilter;
