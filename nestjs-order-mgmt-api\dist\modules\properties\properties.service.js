"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertiesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const property_entity_1 = require("./entities/property.entity");
const property_tag_entity_1 = require("./entities/property-tag.entity");
let PropertiesService = class PropertiesService {
    constructor(propertyRepository, propertyTagRepository) {
        this.propertyRepository = propertyRepository;
        this.propertyTagRepository = propertyTagRepository;
    }
    async create(createPropertyDto) {
        const property = this.propertyRepository.create(createPropertyDto);
        const savedProperty = await this.propertyRepository.save(property);
        return {
            property: savedProperty,
            message: 'Property created successfully',
        };
    }
    async findAll(query) {
        const { page = 1, limit = 10, propertyType, status, search, sortBy = 'createdAt', sortOrder = 'DESC', } = query;
        const queryBuilder = this.propertyRepository.createQueryBuilder('property');
        if (propertyType) {
            queryBuilder.andWhere('property.propertyType = :propertyType', {
                propertyType,
            });
        }
        if (status) {
            queryBuilder.andWhere('property.status = :status', { status });
        }
        if (search) {
            queryBuilder.andWhere('(property.addressLine1 ILIKE :search OR property.city ILIKE :search OR property.zipCode ILIKE :search)', { search: `%${search}%` });
        }
        queryBuilder.leftJoinAndSelect('property.tags', 'tags');
        queryBuilder.leftJoinAndSelect('property.orders', 'orders');
        queryBuilder.orderBy(`property.${sortBy}`, sortOrder);
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [properties, total] = await queryBuilder.getManyAndCount();
        return {
            properties,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const property = await this.propertyRepository.findOne({
            where: { id },
            relations: ['tags', 'orders'],
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        return property;
    }
    async update(id, updatePropertyDto) {
        const property = await this.propertyRepository.findOne({ where: { id } });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        await this.propertyRepository.update(id, updatePropertyDto);
        const updatedProperty = await this.findOne(id);
        return {
            property: updatedProperty,
            message: 'Property updated successfully',
        };
    }
    async remove(id) {
        const property = await this.propertyRepository.findOne({ where: { id } });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        await this.propertyRepository.remove(property);
        return {
            message: 'Property deleted successfully',
        };
    }
    async searchByAddress(searchTerm) {
        const properties = await this.propertyRepository.find({
            where: [
                { addressLine1: (0, typeorm_2.Like)(`%${searchTerm}%`) },
                { city: (0, typeorm_2.Like)(`%${searchTerm}%`) },
                { zipCode: (0, typeorm_2.Like)(`%${searchTerm}%`) },
                { state: (0, typeorm_2.Like)(`%${searchTerm}%`) },
            ],
            take: 10,
        });
        return properties;
    }
    async findNearby(latitude, longitude, radius) {
        const properties = await this.propertyRepository.find({
            take: 20,
        });
        return {
            properties,
            center: { latitude, longitude },
            radius,
        };
    }
    async getPropertyOrders(id) {
        const property = await this.propertyRepository.findOne({
            where: { id },
            relations: ['orders'],
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        return {
            propertyId: id,
            orders: property.orders,
            totalOrders: property.orders.length,
        };
    }
    async createTag(createTagDto) {
        const tag = this.propertyTagRepository.create(createTagDto);
        const savedTag = await this.propertyTagRepository.save(tag);
        return {
            tag: savedTag,
            message: 'Property tag created successfully',
        };
    }
    async getTags() {
        const tags = await this.propertyTagRepository.find({
            where: { isActive: true },
            order: { name: 'ASC' },
        });
        return tags;
    }
    async updatePropertyTags(propertyId, tagIds) {
        const property = await this.propertyRepository.findOne({
            where: { id: propertyId },
            relations: ['tags'],
        });
        if (!property) {
            throw new common_1.NotFoundException('Property not found');
        }
        const tags = await this.propertyTagRepository.find({
            where: { id: (0, typeorm_2.In)(tagIds) },
        });
        property.tags = tags;
        await this.propertyRepository.save(property);
        return {
            message: 'Property tags updated successfully',
            tags,
        };
    }
    async getPropertyStats() {
        const totalProperties = await this.propertyRepository.count();
        const propertiesByType = await this.propertyRepository
            .createQueryBuilder('property')
            .select('property.propertyType', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('property.propertyType')
            .getRawMany();
        const propertiesByStatus = await this.propertyRepository
            .createQueryBuilder('property')
            .select('property.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('property.status')
            .getRawMany();
        return {
            totalProperties,
            propertiesByType,
            propertiesByStatus,
        };
    }
};
exports.PropertiesService = PropertiesService;
exports.PropertiesService = PropertiesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(property_entity_1.Property)),
    __param(1, (0, typeorm_1.InjectRepository)(property_tag_entity_1.PropertyTag)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PropertiesService);
//# sourceMappingURL=properties.service.js.map