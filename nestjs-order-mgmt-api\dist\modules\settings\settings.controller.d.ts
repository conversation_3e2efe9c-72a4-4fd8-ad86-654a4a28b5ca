import { SettingsService } from './settings.service';
import { CreateSettingDto } from './dto/create-setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { UpdateSettingValueDto } from './dto/update-setting-value.dto';
import { SettingQueryDto } from './dto/setting-query.dto';
export declare class SettingsController {
    private readonly settingsService;
    constructor(settingsService: SettingsService);
    create(createSettingDto: CreateSettingDto): Promise<{
        setting: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: import("./entities/setting.entity").SettingType;
            category: import("./entities/setting.entity").SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        };
        message: string;
    }>;
    findAll(query: SettingQueryDto): Promise<{
        settings: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: import("./entities/setting.entity").SettingType;
            category: import("./entities/setting.entity").SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    }>;
    getPublicSettings(): Promise<{}>;
    getCategories(): Promise<{
        value: import("./entities/setting.entity").SettingCategory;
        label: string;
    }[]>;
    getByCategory(category: string): Promise<{
        category: string;
        settings: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: import("./entities/setting.entity").SettingType;
            category: import("./entities/setting.entity").SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    }>;
    getByKey(key: string): Promise<{
        value: any;
        id: number;
        key: string;
        name: string;
        description: string;
        type: import("./entities/setting.entity").SettingType;
        category: import("./entities/setting.entity").SettingCategory;
        defaultValue: string;
        options: {
            min?: number;
            max?: number;
            choices?: {
                value: string;
                label: string;
            }[];
            placeholder?: string;
            helpText?: string;
            validation?: string;
            encrypted?: boolean;
        };
        isRequired: boolean;
        isSecret: boolean;
        isEditable: boolean;
        isVisible: boolean;
        sortOrder: number;
        group: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getValue(key: string): Promise<any>;
    findOne(id: number): Promise<{
        value: any;
        id: number;
        key: string;
        name: string;
        description: string;
        type: import("./entities/setting.entity").SettingType;
        category: import("./entities/setting.entity").SettingCategory;
        defaultValue: string;
        options: {
            min?: number;
            max?: number;
            choices?: {
                value: string;
                label: string;
            }[];
            placeholder?: string;
            helpText?: string;
            validation?: string;
            encrypted?: boolean;
        };
        isRequired: boolean;
        isSecret: boolean;
        isEditable: boolean;
        isVisible: boolean;
        sortOrder: number;
        group: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    update(id: number, updateSettingDto: UpdateSettingDto): Promise<{
        setting: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: import("./entities/setting.entity").SettingType;
            category: import("./entities/setting.entity").SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        };
        message: string;
    }>;
    updateValue(key: string, updateValueDto: UpdateSettingValueDto): Promise<{
        message: string;
        value: any;
    }>;
    bulkUpdate(settings: {
        key: string;
        value: any;
    }[]): Promise<{
        results: any[];
        message: string;
    }>;
    resetToDefault(key: string): Promise<{
        message: string;
        value: any;
    }>;
    createBackup(): Promise<{
        backup: {
            timestamp: string;
            version: string;
            settings: {
                key: string;
                value: string;
                type: import("./entities/setting.entity").SettingType;
                category: import("./entities/setting.entity").SettingCategory;
            }[];
        };
        message: string;
    }>;
    restoreBackup(backupData: any): Promise<{
        results: any[];
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    validateSetting(data: {
        key: string;
        value: any;
    }): Promise<{
        isValid: boolean;
        errors: string[];
    }>;
}
