import { PropertyType, PropertyStatus } from '../entities/property.entity';
export declare class CreatePropertyDto {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    zipCode: string;
    country?: string;
    propertyType: PropertyType;
    status?: PropertyStatus;
    yearBuilt?: number;
    squareFootage?: number;
    lotSize?: number;
    bedrooms?: number;
    bathrooms?: number;
    floors?: number;
    foundationType?: string;
    hasGarage?: boolean;
    hasPool?: boolean;
    hasFireplace?: boolean;
    hasBasement?: boolean;
    hasAttic?: boolean;
    hasDeck?: boolean;
    hasPatio?: boolean;
    hasElectricity?: boolean;
    hasWater?: boolean;
    hasGas?: boolean;
    hasSewer?: boolean;
    hasInternet?: boolean;
    heatingType?: string;
    coolingType?: string;
    gateCode?: string;
    lockboxCode?: string;
    alarmCode?: string;
    mlsNumber?: string;
    description?: string;
    notes?: string;
    images?: string[];
    customFields?: Record<string, any>;
}
