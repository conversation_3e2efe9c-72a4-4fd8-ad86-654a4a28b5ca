import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsArray,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CustomFieldType, CustomFieldEntity } from '../entities/custom-field.entity';

class CustomFieldValidationDto {
  @ApiProperty({ description: 'Is field required', default: false })
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiProperty({ description: 'Minimum length for text fields', required: false })
  @IsNumber()
  @IsOptional()
  minLength?: number;

  @ApiProperty({ description: 'Maximum length for text fields', required: false })
  @IsNumber()
  @IsOptional()
  maxLength?: number;

  @ApiProperty({ description: 'Minimum value for number fields', required: false })
  @IsNumber()
  @IsOptional()
  min?: number;

  @ApiProperty({ description: 'Maximum value for number fields', required: false })
  @IsNumber()
  @IsOptional()
  max?: number;

  @ApiProperty({ description: 'Validation pattern (regex)', required: false })
  @IsString()
  @IsOptional()
  pattern?: string;
}

class CustomFieldDisplayDto {
  @ApiProperty({ description: 'Field width', required: false })
  @IsString()
  @IsOptional()
  width?: string;

  @ApiProperty({ description: 'Number of columns for layout', required: false })
  @IsNumber()
  @IsOptional()
  columns?: number;

  @ApiProperty({ description: 'Number of rows for textarea', required: false })
  @IsNumber()
  @IsOptional()
  rows?: number;
}

class CustomFieldOptionsDto {
  @ApiProperty({
    description: 'Available choices for select/radio fields',
    type: [Object],
    required: false,
  })
  @IsOptional()
  choices?: { value: string; label: string }[];

  @ApiProperty({ description: 'Placeholder text', required: false })
  @IsString()
  @IsOptional()
  placeholder?: string;

  @ApiProperty({ description: 'Help text', required: false })
  @IsString()
  @IsOptional()
  helpText?: string;

  @ApiProperty({
    description: 'Validation rules',
    type: CustomFieldValidationDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldValidationDto)
  @IsOptional()
  validation?: CustomFieldValidationDto;

  @ApiProperty({
    description: 'Display options',
    type: CustomFieldDisplayDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldDisplayDto)
  @IsOptional()
  display?: CustomFieldDisplayDto;
}

export class CreateCustomFieldDto {
  @ApiProperty({
    description: 'Field display name',
    example: 'Property Features',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Field unique key',
    example: 'property_features',
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'Field description',
    example: 'Additional features of the property',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Field type',
    enum: CustomFieldType,
    example: CustomFieldType.MULTISELECT,
  })
  @IsEnum(CustomFieldType)
  type: CustomFieldType;

  @ApiProperty({
    description: 'Entity type this field applies to',
    enum: CustomFieldEntity,
    example: CustomFieldEntity.PROPERTY,
  })
  @IsEnum(CustomFieldEntity)
  entityType: CustomFieldEntity;

  @ApiProperty({
    description: 'Field options and configuration',
    type: CustomFieldOptionsDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CustomFieldOptionsDto)
  @IsOptional()
  options?: CustomFieldOptionsDto;

  @ApiProperty({
    description: 'Default value',
    example: 'Default text',
    required: false,
  })
  @IsString()
  @IsOptional()
  defaultValue?: string;

  @ApiProperty({
    description: 'Is field required',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isRequired?: boolean = false;

  @ApiProperty({
    description: 'Is field active',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;

  @ApiProperty({
    description: 'Is field visible in UI',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isVisible?: boolean = true;

  @ApiProperty({
    description: 'Is field searchable',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isSearchable?: boolean = false;

  @ApiProperty({
    description: 'Sort order',
    default: 0,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  sortOrder?: number = 0;

  @ApiProperty({
    description: 'Field group',
    example: 'Property Details',
    required: false,
  })
  @IsString()
  @IsOptional()
  group?: string;

  @ApiProperty({
    description: 'Roles that can view/edit this field',
    type: [String],
    example: ['admin', 'inspector'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  permissions?: string[];
}
