import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { CustomField } from './custom-field.entity';

@Entity('custom_field_values')
@Index(['entityType', 'entityId', 'customFieldId'], { unique: true })
export class CustomFieldValue {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  customFieldId: number;

  @Column({ length: 50 })
  entityType: string; // 'order', 'property', 'inspector', etc.

  @Column()
  entityId: number; // ID of the related entity

  @Column({ type: 'text', nullable: true })
  value: string;

  @Column({ type: 'jsonb', nullable: true })
  jsonValue: any; // For complex data types

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => CustomField, (field) => field.values)
  @JoinColumn({ name: 'customFieldId' })
  customField: CustomField;
}
