import { Order, OrderStatus, PropertyType } from './order.entity';

describe('Order Entity', () => {
  let order: Order;

  beforeEach(() => {
    order = new Order();
  });

  describe('Order Creation', () => {
    it('should create an order with default values', () => {
      expect(order).toBeDefined();
      expect(order.id).toBeUndefined();
      expect(order.status).toBeUndefined();
    });

    it('should set order properties correctly', () => {
      order.orderNumber = 'ORD-2024-0001';
      order.status = OrderStatus.PENDING;
      order.propertyType = PropertyType.RESIDENTIAL;
      order.addressLine1 = '123 Main St';
      order.city = 'New York';
      order.state = 'NY';
      order.zipCode = '10001';
      order.clientIds = [1, 2];
      order.assignedInspectorIds = [1];

      expect(order.orderNumber).toBe('ORD-2024-0001');
      expect(order.status).toBe(OrderStatus.PENDING);
      expect(order.propertyType).toBe(PropertyType.RESIDENTIAL);
      expect(order.addressLine1).toBe('123 Main St');
      expect(order.city).toBe('New York');
      expect(order.state).toBe('NY');
      expect(order.zipCode).toBe('10001');
      expect(order.clientIds).toEqual([1, 2]);
      expect(order.assignedInspectorIds).toEqual([1]);
    });

    it('should handle multiple clients', () => {
      order.clientIds = [1, 2, 3, 4];

      expect(order.clientIds).toHaveLength(4);
      expect(order.clientIds).toContain(1);
      expect(order.clientIds).toContain(2);
      expect(order.clientIds).toContain(3);
      expect(order.clientIds).toContain(4);
    });

    it('should handle multiple inspectors', () => {
      order.assignedInspectorIds = [1, 2, 3];

      expect(order.assignedInspectorIds).toHaveLength(3);
      expect(order.assignedInspectorIds).toContain(1);
      expect(order.assignedInspectorIds).toContain(2);
      expect(order.assignedInspectorIds).toContain(3);
    });

    it('should handle empty client and inspector arrays', () => {
      order.clientIds = [];
      order.assignedInspectorIds = [];

      expect(order.clientIds).toEqual([]);
      expect(order.assignedInspectorIds).toEqual([]);
    });

    it('should handle null client and inspector arrays', () => {
      order.clientIds = null;
      order.assignedInspectorIds = null;

      expect(order.clientIds).toBeNull();
      expect(order.assignedInspectorIds).toBeNull();
    });
  });

  describe('Order Status', () => {
    it('should have all required order statuses', () => {
      expect(OrderStatus.PENDING).toBe('pending');
      expect(OrderStatus.ASSIGNED).toBe('assigned');
      expect(OrderStatus.SCHEDULED).toBe('scheduled');
      expect(OrderStatus.IN_PROGRESS).toBe('in_progress');
      expect(OrderStatus.COMPLETED).toBe('completed');
      expect(OrderStatus.CANCELLED).toBe('cancelled');
    });

    it('should allow status transitions', () => {
      order.status = OrderStatus.PENDING;
      expect(order.status).toBe(OrderStatus.PENDING);

      order.status = OrderStatus.ASSIGNED;
      expect(order.status).toBe(OrderStatus.ASSIGNED);

      order.status = OrderStatus.SCHEDULED;
      expect(order.status).toBe(OrderStatus.SCHEDULED);

      order.status = OrderStatus.IN_PROGRESS;
      expect(order.status).toBe(OrderStatus.IN_PROGRESS);

      order.status = OrderStatus.COMPLETED;
      expect(order.status).toBe(OrderStatus.COMPLETED);
    });
  });

  describe('Property Types', () => {
    it('should have all required property types', () => {
      expect(PropertyType.RESIDENTIAL).toBe('residential');
      expect(PropertyType.COMMERCIAL).toBe('commercial');
      expect(PropertyType.INDUSTRIAL).toBe('industrial');
      expect(PropertyType.MULTI_FAMILY).toBe('multi_family');
      expect(PropertyType.CONDO).toBe('condo');
      expect(PropertyType.TOWNHOUSE).toBe('townhouse');
      expect(PropertyType.MOBILE_HOME).toBe('mobile_home');
      expect(PropertyType.VACANT_LAND).toBe('vacant_land');
    });

    it('should set property type correctly', () => {
      order.propertyType = PropertyType.RESIDENTIAL;
      expect(order.propertyType).toBe(PropertyType.RESIDENTIAL);

      order.propertyType = PropertyType.COMMERCIAL;
      expect(order.propertyType).toBe(PropertyType.COMMERCIAL);

      order.propertyType = PropertyType.INDUSTRIAL;
      expect(order.propertyType).toBe(PropertyType.INDUSTRIAL);
    });
  });

  describe('Address Information', () => {
    it('should store complete address information', () => {
      order.addressLine1 = '123 Main Street';
      order.addressLine2 = 'Apt 4B';
      order.city = 'New York';
      order.state = 'NY';
      order.zipCode = '10001';
      order.country = 'USA';

      expect(order.addressLine1).toBe('123 Main Street');
      expect(order.addressLine2).toBe('Apt 4B');
      expect(order.city).toBe('New York');
      expect(order.state).toBe('NY');
      expect(order.zipCode).toBe('10001');
      expect(order.country).toBe('USA');
    });

    it('should handle optional address fields', () => {
      order.addressLine1 = '123 Main Street';
      order.city = 'New York';
      order.state = 'NY';
      order.zipCode = '10001';
      // addressLine2 and country are optional

      expect(order.addressLine1).toBe('123 Main Street');
      expect(order.addressLine2).toBeUndefined();
      expect(order.city).toBe('New York');
      expect(order.state).toBe('NY');
      expect(order.zipCode).toBe('10001');
      expect(order.country).toBeUndefined();
    });
  });

  describe('Scheduling Information', () => {
    it('should store scheduling information', () => {
      const inspectionDate = new Date('2024-01-15');
      order.inspectionDate = inspectionDate;
      order.scheduledDate = '2024-01-15';
      order.scheduledTime = '10:00';

      expect(order.inspectionDate).toEqual(inspectionDate);
      expect(order.scheduledDate).toBe('2024-01-15');
      expect(order.scheduledTime).toBe('10:00');
    });
  });

  describe('Completion and Cancellation', () => {
    it('should store completion information', () => {
      const completedAt = new Date();
      order.completedAt = completedAt;
      order.inspectionReport = 'Inspection completed successfully';
      order.inspectionNotes = 'All systems checked';

      expect(order.completedAt).toEqual(completedAt);
      expect(order.inspectionReport).toBe('Inspection completed successfully');
      expect(order.inspectionNotes).toBe('All systems checked');
    });

    it('should store cancellation information', () => {
      const cancelledAt = new Date();
      order.cancelledAt = cancelledAt;
      order.cancellationReason = 'Client requested cancellation';

      expect(order.cancelledAt).toEqual(cancelledAt);
      expect(order.cancellationReason).toBe('Client requested cancellation');
    });
  });

  describe('Pricing Information', () => {
    it('should store pricing information', () => {
      order.inspectionFee = 500.00;
      order.travelFee = 50.00;
      order.additionalFees = 25.00;
      order.totalAmount = 575.00;
      order.processingFee = 15.00;

      expect(order.inspectionFee).toBe(500.00);
      expect(order.travelFee).toBe(50.00);
      expect(order.additionalFees).toBe(25.00);
      expect(order.totalAmount).toBe(575.00);
      expect(order.processingFee).toBe(15.00);
    });

    it('should handle zero fees', () => {
      order.inspectionFee = 500.00;
      order.travelFee = 0;
      order.additionalFees = 0;
      order.totalAmount = 500.00;
      order.processingFee = 0;

      expect(order.inspectionFee).toBe(500.00);
      expect(order.travelFee).toBe(0);
      expect(order.additionalFees).toBe(0);
      expect(order.totalAmount).toBe(500.00);
      expect(order.processingFee).toBe(0);
    });
  });

  describe('Property Features', () => {
    it('should store property features', () => {
      order.squareFootage = 2500;
      order.yearBuilt = 1995;
      order.bedrooms = 3;
      order.bathrooms = 2.5;
      order.stories = 2;
      order.garageSpaces = 2;

      expect(order.squareFootage).toBe(2500);
      expect(order.yearBuilt).toBe(1995);
      expect(order.bedrooms).toBe(3);
      expect(order.bathrooms).toBe(2.5);
      expect(order.stories).toBe(2);
      expect(order.garageSpaces).toBe(2);
    });

    it('should handle optional property features', () => {
      order.squareFootage = 1800;
      // Other features are optional

      expect(order.squareFootage).toBe(1800);
      expect(order.yearBuilt).toBeUndefined();
      expect(order.bedrooms).toBeUndefined();
      expect(order.bathrooms).toBeUndefined();
      expect(order.stories).toBeUndefined();
      expect(order.garageSpaces).toBeUndefined();
    });
  });

  describe('Special Instructions', () => {
    it('should store special instructions and notes', () => {
      order.specialInstructions = 'Please call before arrival';
      order.accessInstructions = 'Key is under the mat';
      order.alarmCode = '1234';
      order.note = 'Rush order';

      expect(order.specialInstructions).toBe('Please call before arrival');
      expect(order.accessInstructions).toBe('Key is under the mat');
      expect(order.alarmCode).toBe('1234');
      expect(order.note).toBe('Rush order');
    });
  });

  describe('Timestamps', () => {
    it('should have creation and update timestamps', () => {
      const now = new Date();
      order.createdAt = now;
      order.updatedAt = now;

      expect(order.createdAt).toEqual(now);
      expect(order.updatedAt).toEqual(now);
    });
  });
});
