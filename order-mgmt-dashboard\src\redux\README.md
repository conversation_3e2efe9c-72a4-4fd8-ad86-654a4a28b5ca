# Redux Implementation

This directory contains the Redux implementation for the Order Management Dashboard application.

## Structure

- `store.ts`: The main Redux store configuration
- `hooks.ts`: Custom hooks for using Redux with TypeScript
- `slices/`: Directory containing Redux slices
  - `ordersSlice.ts`: Reducer and actions for orders management
  - `userSlice.ts`: Reducer and actions for user management

## Usage

### Store Setup

The store is configured in `store.ts` and provided to the application in `main.tsx`:

```tsx
// main.tsx
import { Provider } from 'react-redux'
import { store } from './redux/store'

createRoot(document.getElementById("root") as HTMLElement).render(
  <Provider store={store}>
    <App />
  </Provider>
);
```

### Using Redux in Components

Use the custom hooks from `hooks.ts` to access the store and dispatch actions:

```tsx
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { fetchOrders } from '@/redux/slices/ordersSlice';

function MyComponent() {
  const dispatch = useAppDispatch();
  const { items, loading, error } = useAppSelector(state => state.orders);

  useEffect(() => {
    dispatch(fetchOrders({ page: 1, pageSize: 10 }));
  }, [dispatch]);

  // Rest of your component
}
```

## Pagination Support

The orders reducer includes built-in support for pagination with the following state structure:

```typescript
{
  items: [],
  currentPage: 1,
  pageSize: 10,
  totalItems: 0,
  totalPages: 0,
  loading: false,
  error: null,
}
```

To change the page or page size, dispatch the appropriate actions:

```tsx
import { setPage, setPageSize } from '@/redux/slices/ordersSlice';

// Change page
dispatch(setPage(2));

// Change page size
dispatch(setPageSize(20));
```

## Async Operations

Each slice includes async thunks for common operations:

### Orders Slice

- `fetchOrders`: Fetch orders with pagination
- `addOrder`: Add a new order
- `updateOrder`: Update an existing order
- `deleteOrder`: Delete an order

### User Slice

- `fetchCurrentUser`: Fetch the current user's profile
- `loginUser`: Log in a user
- `registerUser`: Register a new user
- `updateUserProfile`: Update the user's profile
- `logoutUser`: Log out the current user
