"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Auth = Auth;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../modules/auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../modules/auth/guards/roles.guard");
const roles_decorator_1 = require("./roles.decorator");
function Auth(...roles) {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard), (0, roles_decorator_1.Roles)(...roles), (0, swagger_1.ApiBearerAuth)('JWT-auth'), (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized' }));
}
//# sourceMappingURL=auth.decorator.js.map