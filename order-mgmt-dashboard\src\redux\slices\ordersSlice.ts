import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Order, OrdersState, OrdersApiResponse } from '@/types/order';
import { apiClient } from '@/lib/api-client';
import { mockApi } from '@/redux/mockApi';

// Use mock API for development
const USE_MOCK_API = false;

const initialState: OrdersState = {
  items: [],
  currentPage: 1,
  pageSize: 10,
  totalItems: 0,
  totalPages: 0,
  loading: false,
  error: null,
};

// Async thunks
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async ({ page = 1, pageSize = 10 }: { page?: number; pageSize?: number }, { rejectWithValue }) => {
    try {
      if (USE_MOCK_API) {
        // Use mock API for development
        const response = await mockApi.getOrders(page, pageSize);

        return {
          orders: response.orders,
          totalItems: response.pagination.total,
          totalPages: response.pagination.totalPages,
          currentPage: page,
          pageSize,
        };
      } else {
        // Use real API for production
        const response = await apiClient<OrdersApiResponse>(`orders?page=${page}&pageSize=${pageSize}`);

        if (!response) {
          return rejectWithValue('Failed to fetch orders');
        }

        return {
          orders: response.orders,
          totalItems: response.pagination.total,
          totalPages: response.pagination.totalPages,
          currentPage: page,
          pageSize,
        };
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch orders');
    }
  }
);

export const addOrder = createAsyncThunk(
  'orders/addOrder',
  async (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      if (USE_MOCK_API) {
        // Use mock API for development
        const response = await mockApi.addOrder(order);
        return response.data.order;
      } else {
        // API endpoint for adding an order
        const response = await apiClient<{ success: boolean; data: { order: Order } }>('orders', {
          method: 'POST',
          data: order,
        });

        if (!response) {
          return rejectWithValue('Failed to add order');
        }

        return response.data.order;
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to add order');
    }
  }
);

export const updateOrder = createAsyncThunk(
  'orders/updateOrder',
  async ({ id, order }: { id: number; order: Partial<Order> }, { rejectWithValue }) => {
    try {
      if (USE_MOCK_API) {
        // Use mock API for development
        const response = await mockApi.updateOrder(id, order);
        return response.data.order;
      } else {
        // API endpoint for updating an order
        const response = await apiClient<{ success: boolean; data: { order: Order } }>(`orders/${id}`, {
          method: 'PUT',
          data: order,
        });

        if (!response) {
          return rejectWithValue('Failed to update order');
        }

        return response.data.order;
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update order');
    }
  }
);

export const deleteOrder = createAsyncThunk(
  'orders/deleteOrder',
  async (id: number, { rejectWithValue }) => {
    try {
      if (USE_MOCK_API) {
        // Use mock API for development
        await mockApi.deleteOrder(id);
        return id;
      } else {
        // API endpoint for deleting an order
        const response = await apiClient<{ success: boolean }>(`orders/${id}`, {
          method: 'DELETE',
        });

        if (!response) {
          return rejectWithValue('Failed to delete order');
        }

        return id;
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete order');
    }
  }
);

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    clearOrdersError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch orders
      .addCase(fetchOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.orders;
        state.totalItems = action.payload.totalItems;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
        state.pageSize = action.payload.pageSize;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Add order
      .addCase(addOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.items.push(action.payload);
        state.totalItems += 1;
        state.totalPages = Math.ceil(state.totalItems / state.pageSize);
      })
      .addCase(addOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update order
      .addCase(updateOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOrder.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.items.findIndex(order => order.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
      })
      .addCase(updateOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete order
      .addCase(deleteOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.items = state.items.filter(order => order.id !== action.payload);
        state.totalItems -= 1;
        state.totalPages = Math.ceil(state.totalItems / state.pageSize);
      })
      .addCase(deleteOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setPage, setPageSize, clearOrdersError } = ordersSlice.actions;
export default ordersSlice.reducer;
