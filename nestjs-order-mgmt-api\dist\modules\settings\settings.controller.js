"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const settings_service_1 = require("./settings.service");
const create_setting_dto_1 = require("./dto/create-setting.dto");
const update_setting_dto_1 = require("./dto/update-setting.dto");
const update_setting_value_dto_1 = require("./dto/update-setting-value.dto");
const setting_query_dto_1 = require("./dto/setting-query.dto");
const auth_decorator_1 = require("../../common/decorators/auth.decorator");
let SettingsController = class SettingsController {
    constructor(settingsService) {
        this.settingsService = settingsService;
    }
    async create(createSettingDto) {
        return this.settingsService.create(createSettingDto);
    }
    async findAll(query) {
        return this.settingsService.findAll(query);
    }
    async getPublicSettings() {
        return this.settingsService.getPublicSettings();
    }
    async getCategories() {
        return this.settingsService.getCategories();
    }
    async getByCategory(category) {
        return this.settingsService.getByCategory(category);
    }
    async getByKey(key) {
        return this.settingsService.getByKey(key);
    }
    async getValue(key) {
        return this.settingsService.getValue(key);
    }
    async findOne(id) {
        return this.settingsService.findOne(id);
    }
    async update(id, updateSettingDto) {
        return this.settingsService.update(id, updateSettingDto);
    }
    async updateValue(key, updateValueDto) {
        return this.settingsService.updateValue(key, updateValueDto.value);
    }
    async bulkUpdate(settings) {
        return this.settingsService.bulkUpdate(settings);
    }
    async resetToDefault(key) {
        return this.settingsService.resetToDefault(key);
    }
    async createBackup() {
        return this.settingsService.createBackup();
    }
    async restoreBackup(backupData) {
        return this.settingsService.restoreBackup(backupData);
    }
    async remove(id) {
        return this.settingsService.remove(id);
    }
    async validateSetting(data) {
        return this.settingsService.validateSetting(data.key, data.value);
    }
};
exports.SettingsController = SettingsController;
__decorate([
    (0, common_1.Post)(),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new setting' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Setting successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_setting_dto_1.CreateSettingDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all settings with filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Settings retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'group', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'isVisible', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [setting_query_dto_1.SettingQueryDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('public'),
    (0, swagger_1.ApiOperation)({ summary: 'Get public settings (no authentication required)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Public settings retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getPublicSettings", null);
__decorate([
    (0, common_1.Get)('categories'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all setting categories' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Categories retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getCategories", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get settings by category' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Settings retrieved successfully' }),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getByCategory", null);
__decorate([
    (0, common_1.Get)('key/:key'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get setting by key' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Setting not found' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getByKey", null);
__decorate([
    (0, common_1.Get)('value/:key'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get setting value by key' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting value retrieved successfully' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "getValue", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get setting by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Setting not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update setting' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Setting not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_setting_dto_1.UpdateSettingDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)('key/:key/value'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update setting value by key' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting value updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Setting not found' }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_setting_value_dto_1.UpdateSettingValueDto]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "updateValue", null);
__decorate([
    (0, common_1.Post)('bulk-update'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Bulk update multiple settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Settings updated successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "bulkUpdate", null);
__decorate([
    (0, common_1.Post)('reset/:key'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Reset setting to default value' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting reset successfully' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "resetToDefault", null);
__decorate([
    (0, common_1.Post)('backup'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create settings backup' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Settings backup created' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "createBackup", null);
__decorate([
    (0, common_1.Post)('restore'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Restore settings from backup' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Settings restored successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "restoreBackup", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete setting' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Setting deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Setting not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Validate setting value' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Validation result' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SettingsController.prototype, "validateSetting", null);
exports.SettingsController = SettingsController = __decorate([
    (0, swagger_1.ApiTags)('Settings'),
    (0, common_1.Controller)('settings'),
    __metadata("design:paramtypes", [settings_service_1.SettingsService])
], SettingsController);
//# sourceMappingURL=settings.controller.js.map