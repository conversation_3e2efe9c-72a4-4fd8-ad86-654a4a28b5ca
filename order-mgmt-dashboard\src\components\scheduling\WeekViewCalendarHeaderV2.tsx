import React from "react";
import { format } from "date-fns";

interface WeekViewCalendarHeaderV2Props {
  days: Date[];
}

const WeekViewCalendarHeaderV2: React.FC<WeekViewCalendarHeaderV2Props> = ({
  days,
}) => {
  const today = new Date();
  const isToday = (date: Date) => {
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const showMonth = (date: Date, index: number) => {
    if (index === 0) return true;
    const prevDate = days[index - 1];
    return date.getMonth() !== prevDate.getMonth();
  };

  return (
    <div className="grid grid-cols-[100px_repeat(7,1fr)] border-b border-gray-200 sticky top-0 bg-white z-10 shadow-sm">
      <div className="p-3 text-gray-500 flex items-center justify-center border-r border-gray-100 bg-gray-50/50">
        <span className="text-xs font-semibold uppercase tracking-wide">
          Time
        </span>
      </div>
      {days.map((day, index) => {
        const dayIsToday = isToday(day);
        const isWeekend = day.getDay() === 0 || day.getDay() === 6;

        return (
          <div
            key={day.toISOString()}
            className={`px-2 py-3 text-center border-r border-gray-100 ${
              dayIsToday ? "bg-blue-50/50" : isWeekend ? "bg-gray-50/30" : ""
            }`}
          >
            <div className="flex flex-col items-center">
              <div
                className={`text-xs uppercase tracking-wide font-semibold ${
                  dayIsToday
                    ? "text-blue-600"
                    : isWeekend
                    ? "text-gray-500"
                    : "text-gray-400"
                }`}
              >
                {format(day, "EEE")}
              </div>

              <div className="mt-1 flex items-center justify-center">
                <div
                  className={`
                    ${
                      dayIsToday
                        ? "bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium shadow-sm"
                        : "text-gray-800 text-sm font-medium"
                    }
                  `}
                >
                  {format(day, "d")}
                </div>
              </div>

              {showMonth(day, index) && (
                <div className="text-xs font-normal text-gray-500 mt-1">
                  {format(day, "MMM")}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default WeekViewCalendarHeaderV2;
