"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TemplatesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const Handlebars = require("handlebars");
const template_entity_1 = require("./entities/template.entity");
let TemplatesService = class TemplatesService {
    constructor(templateRepository) {
        this.templateRepository = templateRepository;
        this.registerHandlebarsHelpers();
    }
    registerHandlebarsHelpers() {
        Handlebars.registerHelper('formatDate', (date, format) => {
            if (!date)
                return '';
            return new Date(date).toLocaleDateString();
        });
        Handlebars.registerHelper('formatCurrency', (amount) => {
            if (!amount)
                return '$0.00';
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
            }).format(amount);
        });
        Handlebars.registerHelper('uppercase', (str) => {
            return str ? str.toUpperCase() : '';
        });
        Handlebars.registerHelper('lowercase', (str) => {
            return str ? str.toLowerCase() : '';
        });
    }
    async create(createTemplateDto) {
        const validation = await this.validateTemplate(createTemplateDto.content, createTemplateDto.htmlContent);
        if (!validation.isValid) {
            throw new common_1.BadRequestException(`Template validation failed: ${validation.errors.join(', ')}`);
        }
        const template = this.templateRepository.create(createTemplateDto);
        const savedTemplate = await this.templateRepository.save(template);
        return {
            template: savedTemplate,
            message: 'Template created successfully',
        };
    }
    async findAll(query) {
        const { page = 1, limit = 10, type, category, isActive, search, sortBy = 'createdAt', sortOrder = 'DESC', } = query;
        const queryBuilder = this.templateRepository.createQueryBuilder('template');
        if (type) {
            queryBuilder.andWhere('template.type = :type', { type });
        }
        if (category) {
            queryBuilder.andWhere('template.category = :category', { category });
        }
        if (isActive !== undefined) {
            queryBuilder.andWhere('template.isActive = :isActive', { isActive });
        }
        if (search) {
            queryBuilder.andWhere('(template.name ILIKE :search OR template.subject ILIKE :search)', { search: `%${search}%` });
        }
        queryBuilder.orderBy(`template.${sortBy}`, sortOrder);
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [templates, total] = await queryBuilder.getManyAndCount();
        return {
            templates,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const template = await this.templateRepository.findOne({ where: { id } });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        return template;
    }
    async findByCategory(category) {
        const templates = await this.templateRepository.find({
            where: { category: category, isActive: true },
            order: { name: 'ASC' },
        });
        return templates;
    }
    async update(id, updateTemplateDto) {
        const template = await this.templateRepository.findOne({ where: { id } });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        if (updateTemplateDto.content || updateTemplateDto.htmlContent) {
            const validation = await this.validateTemplate(updateTemplateDto.content || template.content, updateTemplateDto.htmlContent || template.htmlContent);
            if (!validation.isValid) {
                throw new common_1.BadRequestException(`Template validation failed: ${validation.errors.join(', ')}`);
            }
        }
        await this.templateRepository.update(id, updateTemplateDto);
        const updatedTemplate = await this.findOne(id);
        return {
            template: updatedTemplate,
            message: 'Template updated successfully',
        };
    }
    async updateStatus(id, isActive) {
        const template = await this.templateRepository.findOne({ where: { id } });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        await this.templateRepository.update(id, { isActive });
        return {
            message: `Template ${isActive ? 'activated' : 'deactivated'} successfully`,
        };
    }
    async remove(id) {
        const template = await this.templateRepository.findOne({ where: { id } });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        await this.templateRepository.remove(template);
        return {
            message: 'Template deleted successfully',
        };
    }
    async renderTemplate(id, variables) {
        const template = await this.findOne(id);
        try {
            const textTemplate = Handlebars.compile(template.content);
            const renderedContent = textTemplate(variables);
            let renderedHtmlContent = null;
            if (template.htmlContent) {
                const htmlTemplate = Handlebars.compile(template.htmlContent);
                renderedHtmlContent = htmlTemplate(variables);
            }
            const subjectTemplate = Handlebars.compile(template.subject);
            const renderedSubject = subjectTemplate(variables);
            return {
                subject: renderedSubject,
                content: renderedContent,
                htmlContent: renderedHtmlContent,
                type: template.type,
                category: template.category,
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Template rendering failed: ${error.message}`);
        }
    }
    async previewTemplate(id) {
        const template = await this.findOne(id);
        const sampleData = this.generateSampleData(template.variables || []);
        return this.renderTemplate(id, sampleData);
    }
    async duplicateTemplate(id) {
        const template = await this.findOne(id);
        const duplicatedTemplate = this.templateRepository.create({
            ...template,
            id: undefined,
            name: `${template.name} (Copy)`,
            isDefault: false,
        });
        const savedTemplate = await this.templateRepository.save(duplicatedTemplate);
        return {
            template: savedTemplate,
            message: 'Template duplicated successfully',
        };
    }
    async validateTemplate(content, htmlContent) {
        const errors = [];
        try {
            Handlebars.compile(content);
        }
        catch (error) {
            errors.push(`Text content error: ${error.message}`);
        }
        if (htmlContent) {
            try {
                Handlebars.compile(htmlContent);
            }
            catch (error) {
                errors.push(`HTML content error: ${error.message}`);
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    async getCategories() {
        return Object.values(template_entity_1.TemplateCategory).map((category) => ({
            value: category,
            label: category.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
        }));
    }
    async getAvailableVariables() {
        return [
            { name: 'clientName', description: 'Client full name', type: 'string' },
            { name: 'clientEmail', description: 'Client email address', type: 'string' },
            { name: 'orderNumber', description: 'Order number', type: 'string' },
            { name: 'propertyAddress', description: 'Property address', type: 'string' },
            { name: 'inspectionDate', description: 'Inspection date', type: 'date' },
            { name: 'inspectorName', description: 'Inspector name', type: 'string' },
            { name: 'inspectionFee', description: 'Inspection fee', type: 'currency' },
            { name: 'companyName', description: 'Company name', type: 'string' },
            { name: 'companyPhone', description: 'Company phone', type: 'string' },
            { name: 'companyEmail', description: 'Company email', type: 'string' },
        ];
    }
    generateSampleData(variables) {
        const sampleData = {
            clientName: 'John Doe',
            clientEmail: '<EMAIL>',
            orderNumber: 'ORD-2024-001',
            propertyAddress: '123 Main St, New York, NY 10001',
            inspectionDate: new Date(),
            inspectorName: 'Jane Smith',
            inspectionFee: 500.00,
            companyName: 'ABC Inspection Services',
            companyPhone: '(*************',
            companyEmail: '<EMAIL>',
        };
        variables.forEach((variable) => {
            if (!sampleData[variable.name]) {
                sampleData[variable.name] = variable.defaultValue || 'Sample Value';
            }
        });
        return sampleData;
    }
};
exports.TemplatesService = TemplatesService;
exports.TemplatesService = TemplatesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(template_entity_1.Template)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], TemplatesService);
//# sourceMappingURL=templates.service.js.map