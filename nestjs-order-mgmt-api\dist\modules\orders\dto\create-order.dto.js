"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const order_entity_1 = require("../entities/order.entity");
class CreateOrderDto {
    constructor() {
        this.isClientAttending = false;
        this.isOccupied = false;
        this.hasUtilities = false;
        this.hasAlarm = false;
        this.isSeller = false;
        this.isBuyer = false;
        this.thirdPartyFee = 0;
        this.discountFee = 0;
        this.processingFee = 0;
    }
}
exports.CreateOrderDto = CreateOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Property address line 1', example: '123 Main St' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "addressLine1", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'City', example: 'New York' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Zip code', example: '10001' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "zipCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'State', example: 'NY' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Property type',
        enum: order_entity_1.PropertyType,
        required: false
    }),
    (0, class_validator_1.IsEnum)(order_entity_1.PropertyType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "propertyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Year built', example: 2020, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Min)(1800),
    (0, class_validator_1.Max)(new Date().getFullYear() + 1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "yearBuilt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Foundation type', example: 'Concrete', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "foundationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Gate code', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "gateCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Lockbox code', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "lockboxCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Alarm code', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "alarmCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'MLS number', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "mlsNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Additional notes', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "note", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is client attending', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "isClientAttending", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is property occupied', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "isOccupied", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Has utilities', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "hasUtilities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Has alarm system', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "hasAlarm", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional services',
        example: { flexfund: true, mold: false },
        required: false
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateOrderDto.prototype, "services", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Agent name', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "agentName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Agent email', required: false }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "agentEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Agent phone', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "agentPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is seller agent', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "isSeller", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is buyer agent', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateOrderDto.prototype, "isBuyer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Inspection fee', example: 500.00 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "inspectionFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Third party fee', default: 0, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "thirdPartyFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Discount fee', default: 0, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "discountFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Processing fee', default: 0, required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.Min)(0),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "processingFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Assigned inspector IDs',
        type: [Number],
        example: [1, 2],
        required: false
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateOrderDto.prototype, "assignedInspectorIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Property ID if linking to existing property', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "propertyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Client IDs if admin is creating order for multiple clients',
        type: [Number],
        example: [1, 2],
        required: false
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateOrderDto.prototype, "clientIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Single client ID (legacy)', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "clientId", void 0);
//# sourceMappingURL=create-order.dto.js.map