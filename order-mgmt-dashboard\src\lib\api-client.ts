
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from "sonner";

export interface ApiResponse<T = unknown> {
  success: boolean;
  error?: string;
  errorCode?: string;
  data: T;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    name: string;
    email: string;
    id: string;
    role: string;
  };
}

const BASE_URL = 'https://v0-building-restful-api.vercel.app';

// Create axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: `${BASE_URL}/api`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: string) => void;
  reject: (error: unknown) => void;
}> = [];

const processQueue = (error: unknown, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token!);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return axiosInstance(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = localStorage.getItem('refresh_token');

      if (!refreshToken) {
        // No refresh token, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/signin';
        return Promise.reject(error);
      }

      try {
        // Attempt to refresh the token
        const response = await axios.post(`${BASE_URL}/api/auth/refresh`, {
          refreshToken: refreshToken,
        });

        console.log('response', response)

        const { accessToken, refreshToken: newRefreshToken } = response.data.data;

        // Update tokens in localStorage
        localStorage.setItem('access_token', accessToken);
        if (newRefreshToken) {
          localStorage.setItem('refresh_token', newRefreshToken);
        }

        // Update the authorization header
        axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;

        // Process the queue
        processQueue(null, accessToken);

        // Retry the original request
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // Refresh failed, clear tokens and redirect to login
        processQueue(refreshError, null);
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');

        toast.error('Session expired. Please login again.');
        window.location.href = '/signin';

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

// API client function
export async function apiClient<T>(endpoint: string, options: AxiosRequestConfig = {}): Promise<T | null> {
  try {
    // Remove leading slash if present since baseURL already includes the path
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;

    const response: AxiosResponse<ApiResponse<T>> = await axiosInstance({
      url: cleanEndpoint,
      ...options,
    });

    const result = response.data;

    if (!result.success) {
      toast.error(result.error || 'An error occurred');
      return null;
    }

    return result.data;
  } catch (error: unknown) {
    // Don't show error toast for 401 errors as they're handled by interceptor
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      return null;
    }

    const errorMessage = axios.isAxiosError(error)
      ? error.response?.data?.error || error.message
      : 'Network error occurred';

    toast.error(errorMessage);
    return null;
  }
}

// Export the axios instance for direct use if needed
export { axiosInstance as jwtAxios };
