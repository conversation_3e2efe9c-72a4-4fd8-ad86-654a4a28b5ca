"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCustomFieldDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const custom_field_entity_1 = require("../entities/custom-field.entity");
class CustomFieldValidationDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Is field required', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CustomFieldValidationDto.prototype, "required", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Minimum length for text fields', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CustomFieldValidationDto.prototype, "minLength", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Maximum length for text fields', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CustomFieldValidationDto.prototype, "maxLength", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Minimum value for number fields', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CustomFieldValidationDto.prototype, "min", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Maximum value for number fields', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CustomFieldValidationDto.prototype, "max", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Validation pattern (regex)', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CustomFieldValidationDto.prototype, "pattern", void 0);
class CustomFieldDisplayDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Field width', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CustomFieldDisplayDto.prototype, "width", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of columns for layout', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CustomFieldDisplayDto.prototype, "columns", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of rows for textarea', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CustomFieldDisplayDto.prototype, "rows", void 0);
class CustomFieldOptionsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available choices for select/radio fields',
        type: [Object],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CustomFieldOptionsDto.prototype, "choices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Placeholder text', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CustomFieldOptionsDto.prototype, "placeholder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Help text', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CustomFieldOptionsDto.prototype, "helpText", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Validation rules',
        type: CustomFieldValidationDto,
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CustomFieldValidationDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", CustomFieldValidationDto)
], CustomFieldOptionsDto.prototype, "validation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Display options',
        type: CustomFieldDisplayDto,
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CustomFieldDisplayDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", CustomFieldDisplayDto)
], CustomFieldOptionsDto.prototype, "display", void 0);
class CreateCustomFieldDto {
    constructor() {
        this.isRequired = false;
        this.isActive = true;
        this.isVisible = true;
        this.isSearchable = false;
        this.sortOrder = 0;
    }
}
exports.CreateCustomFieldDto = CreateCustomFieldDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field display name',
        example: 'Property Features',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field unique key',
        example: 'property_features',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field description',
        example: 'Additional features of the property',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field type',
        enum: custom_field_entity_1.CustomFieldType,
        example: custom_field_entity_1.CustomFieldType.MULTISELECT,
    }),
    (0, class_validator_1.IsEnum)(custom_field_entity_1.CustomFieldType),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity type this field applies to',
        enum: custom_field_entity_1.CustomFieldEntity,
        example: custom_field_entity_1.CustomFieldEntity.PROPERTY,
    }),
    (0, class_validator_1.IsEnum)(custom_field_entity_1.CustomFieldEntity),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "entityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field options and configuration',
        type: CustomFieldOptionsDto,
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CustomFieldOptionsDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", CustomFieldOptionsDto)
], CreateCustomFieldDto.prototype, "options", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Default value',
        example: 'Default text',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "defaultValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is field required',
        default: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCustomFieldDto.prototype, "isRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is field active',
        default: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCustomFieldDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is field visible in UI',
        default: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCustomFieldDto.prototype, "isVisible", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is field searchable',
        default: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCustomFieldDto.prototype, "isSearchable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sort order',
        default: 0,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCustomFieldDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field group',
        example: 'Property Details',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCustomFieldDto.prototype, "group", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Roles that can view/edit this field',
        type: [String],
        example: ['admin', 'inspector'],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateCustomFieldDto.prototype, "permissions", void 0);
//# sourceMappingURL=create-custom-field.dto.js.map