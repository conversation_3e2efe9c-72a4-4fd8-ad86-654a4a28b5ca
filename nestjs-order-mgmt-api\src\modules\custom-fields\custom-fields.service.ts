import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CustomField, CustomFieldType, CustomFieldEntity } from './entities/custom-field.entity';
import { CustomFieldValue } from './entities/custom-field-value.entity';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { CustomFieldQueryDto } from './dto/custom-field-query.dto';
import { SetCustomFieldValueDto } from './dto/set-custom-field-value.dto';

@Injectable()
export class CustomFieldsService {
  constructor(
    @InjectRepository(CustomField)
    private readonly customFieldRepository: Repository<CustomField>,
    @InjectRepository(CustomFieldValue)
    private readonly customFieldValueRepository: Repository<CustomFieldValue>,
  ) {}

  async create(createCustomFieldDto: CreateCustomFieldDto) {
    // Check if field with key already exists for this entity type
    const existingField = await this.customFieldRepository.findOne({
      where: {
        key: createCustomFieldDto.key,
        entityType: createCustomFieldDto.entityType,
      },
    });

    if (existingField) {
      throw new BadRequestException('Custom field with this key already exists for this entity type');
    }

    const customField = this.customFieldRepository.create(createCustomFieldDto);
    const savedField = await this.customFieldRepository.save(customField);

    return {
      customField: savedField,
      message: 'Custom field created successfully',
    };
  }

  async findAll(query: CustomFieldQueryDto, user: any) {
    const { entityType, type, isActive, search } = query;

    const queryBuilder = this.customFieldRepository.createQueryBuilder('field');

    // Apply filters
    if (entityType) {
      queryBuilder.andWhere('field.entityType = :entityType', { entityType });
    }

    if (type) {
      queryBuilder.andWhere('field.type = :type', { type });
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('field.isActive = :isActive', { isActive });
    }

    if (search) {
      queryBuilder.andWhere(
        '(field.name ILIKE :search OR field.key ILIKE :search OR field.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply permission filtering based on user role
    if (user.role !== 'admin') {
      queryBuilder.andWhere(
        '(field.permissions IS NULL OR :userRole = ANY(field.permissions))',
        { userRole: user.role },
      );
    }

    // Apply sorting
    queryBuilder.orderBy('field.entityType', 'ASC');
    queryBuilder.addOrderBy('field.sortOrder', 'ASC');
    queryBuilder.addOrderBy('field.name', 'ASC');

    const customFields = await queryBuilder.getMany();

    return {
      customFields,
    };
  }

  async findOne(id: number) {
    const customField = await this.customFieldRepository.findOne({
      where: { id },
      relations: ['values'],
    });

    if (!customField) {
      throw new NotFoundException('Custom field not found');
    }

    return customField;
  }

  async getByEntityType(entityType: string, user: any) {
    const queryBuilder = this.customFieldRepository.createQueryBuilder('field');

    queryBuilder.where('field.entityType = :entityType', { entityType: entityType as any });
    queryBuilder.andWhere('field.isActive = :isActive', { isActive: true });

    // Apply permission filtering
    if (user.role !== 'admin') {
      queryBuilder.andWhere(
        '(field.permissions IS NULL OR :userRole = ANY(field.permissions))',
        { userRole: user.role },
      );
    }

    queryBuilder.orderBy('field.sortOrder', 'ASC');
    queryBuilder.addOrderBy('field.name', 'ASC');

    const customFields = await queryBuilder.getMany();

    return {
      entityType,
      customFields,
    };
  }

  async getEntityValues(entityType: string, entityId: number, user: any) {
    // Get all custom fields for this entity type
    const fields = await this.getByEntityType(entityType, user);

    // Get all values for this entity
    const values = await this.customFieldValueRepository.find({
      where: { entityType, entityId },
      relations: ['customField'],
    });

    // Combine fields with their values
    const fieldsWithValues = fields.customFields.map(field => {
      const value = values.find(v => v.customFieldId === field.id);
      return {
        ...field,
        value: value ? this.parseValue(value, field.type) : field.defaultValue,
        hasValue: !!value,
      };
    });

    return {
      entityType,
      entityId,
      fields: fieldsWithValues,
    };
  }

  async update(id: number, updateCustomFieldDto: UpdateCustomFieldDto) {
    const customField = await this.customFieldRepository.findOne({ where: { id } });

    if (!customField) {
      throw new NotFoundException('Custom field not found');
    }

    await this.customFieldRepository.update(id, updateCustomFieldDto);

    const updatedField = await this.findOne(id);
    return {
      customField: updatedField,
      message: 'Custom field updated successfully',
    };
  }

  async updateStatus(id: number, isActive: boolean) {
    const customField = await this.customFieldRepository.findOne({ where: { id } });

    if (!customField) {
      throw new NotFoundException('Custom field not found');
    }

    await this.customFieldRepository.update(id, { isActive });

    return {
      message: `Custom field ${isActive ? 'activated' : 'deactivated'} successfully`,
    };
  }

  async remove(id: number) {
    const customField = await this.customFieldRepository.findOne({ where: { id } });

    if (!customField) {
      throw new NotFoundException('Custom field not found');
    }

    // Delete all associated values first
    await this.customFieldValueRepository.delete({ customFieldId: id });

    // Delete the custom field
    await this.customFieldRepository.remove(customField);

    return {
      message: 'Custom field deleted successfully',
    };
  }

  async setValue(setValueDto: SetCustomFieldValueDto, user: any) {
    const { customFieldId, entityType, entityId, value } = setValueDto;

    // Get the custom field
    const customField = await this.customFieldRepository.findOne({
      where: { id: customFieldId },
    });

    if (!customField) {
      throw new NotFoundException('Custom field not found');
    }

    // Check permissions
    if (!this.hasPermission(customField, user)) {
      throw new ForbiddenException('You do not have permission to modify this field');
    }

    // Validate the value
    const validation = this.validateValue(value, customField);
    if (!validation.isValid) {
      throw new BadRequestException(`Invalid value: ${validation.errors.join(', ')}`);
    }

    // Find existing value or create new one
    let fieldValue = await this.customFieldValueRepository.findOne({
      where: { customFieldId, entityType, entityId },
    });

    if (!fieldValue) {
      fieldValue = this.customFieldValueRepository.create({
        customFieldId,
        entityType,
        entityId,
      });
    }

    // Set the value based on field type
    if (this.isComplexType(customField.type)) {
      fieldValue.jsonValue = value;
      fieldValue.value = JSON.stringify(value);
    } else {
      fieldValue.value = String(value);
      fieldValue.jsonValue = null;
    }

    await this.customFieldValueRepository.save(fieldValue);

    return {
      message: 'Custom field value set successfully',
      value: this.parseValue(fieldValue, customField.type),
    };
  }

  async setEntityValues(entityType: string, entityId: number, values: { [fieldKey: string]: any }, user: any) {
    const results = [];

    // Get all custom fields for this entity type
    const fields = await this.customFieldRepository.find({
      where: { entityType: entityType as any, isActive: true },
    });

    for (const [fieldKey, value] of Object.entries(values)) {
      const field = fields.find(f => f.key === fieldKey);
      
      if (!field) {
        results.push({ fieldKey, success: false, error: 'Field not found' });
        continue;
      }

      try {
        await this.setValue({
          customFieldId: field.id,
          entityType,
          entityId,
          value,
        }, user);
        results.push({ fieldKey, success: true });
      } catch (error) {
        results.push({ fieldKey, success: false, error: error.message });
      }
    }

    return {
      results,
      message: 'Custom field values update completed',
    };
  }

  async deleteValue(entityType: string, entityId: number, fieldId: number, user: any) {
    const customField = await this.customFieldRepository.findOne({
      where: { id: fieldId },
    });

    if (!customField) {
      throw new NotFoundException('Custom field not found');
    }

    if (!this.hasPermission(customField, user)) {
      throw new ForbiddenException('You do not have permission to modify this field');
    }

    const result = await this.customFieldValueRepository.delete({
      customFieldId: fieldId,
      entityType,
      entityId,
    });

    if (result.affected === 0) {
      throw new NotFoundException('Custom field value not found');
    }

    return {
      message: 'Custom field value deleted successfully',
    };
  }

  async duplicate(id: number) {
    const customField = await this.findOne(id);

    const duplicatedField = this.customFieldRepository.create({
      ...customField,
      id: undefined,
      name: `${customField.name} (Copy)`,
      key: `${customField.key}_copy_${Date.now()}`,
      values: undefined,
    });

    const savedField = await this.customFieldRepository.save(duplicatedField);

    return {
      customField: savedField,
      message: 'Custom field duplicated successfully',
    };
  }

  async getAvailableTypes() {
    return Object.values(CustomFieldType).map(type => ({
      value: type,
      label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    }));
  }

  async getAvailableEntities() {
    return Object.values(CustomFieldEntity).map(entity => ({
      value: entity,
      label: entity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    }));
  }

  private hasPermission(customField: CustomField, user: any): boolean {
    if (user.role === 'admin') {
      return true;
    }

    if (!customField.permissions || customField.permissions.length === 0) {
      return true;
    }

    return customField.permissions.includes(user.role);
  }

  private validateValue(value: any, customField: CustomField) {
    const errors: string[] = [];

    // Check if required
    if (customField.isRequired && (value === null || value === undefined || value === '')) {
      errors.push('This field is required');
    }

    // Type-specific validation
    if (value !== null && value !== undefined && value !== '') {
      switch (customField.type) {
        case CustomFieldType.NUMBER:
          if (isNaN(Number(value))) {
            errors.push('Value must be a number');
          }
          break;
        case CustomFieldType.EMAIL:
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(String(value))) {
            errors.push('Value must be a valid email address');
          }
          break;
        case CustomFieldType.URL:
          try {
            new URL(String(value));
          } catch {
            errors.push('Value must be a valid URL');
          }
          break;
      }
    }

    // Custom validation from options
    if (customField.options?.validation) {
      const validation = customField.options.validation;
      
      if (validation.minLength && String(value).length < validation.minLength) {
        errors.push(`Value must be at least ${validation.minLength} characters long`);
      }
      
      if (validation.maxLength && String(value).length > validation.maxLength) {
        errors.push(`Value must be no more than ${validation.maxLength} characters long`);
      }
      
      if (validation.pattern) {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(String(value))) {
          errors.push('Value does not match the required pattern');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private parseValue(fieldValue: CustomFieldValue, fieldType: CustomFieldType) {
    if (this.isComplexType(fieldType)) {
      return fieldValue.jsonValue;
    }

    const value = fieldValue.value;
    if (!value) return null;

    switch (fieldType) {
      case CustomFieldType.NUMBER:
        return parseFloat(value);
      case CustomFieldType.BOOLEAN:
        return value === 'true';
      case CustomFieldType.DATE:
      case CustomFieldType.DATETIME:
        return new Date(value);
      default:
        return value;
    }
  }

  private isComplexType(fieldType: CustomFieldType): boolean {
    return [
      CustomFieldType.MULTISELECT,
      CustomFieldType.CHECKBOX,
      CustomFieldType.JSON,
    ].includes(fieldType);
  }
}
