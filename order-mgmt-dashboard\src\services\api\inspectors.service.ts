import { apiClient } from "@/lib/api-client";
import { Inspector } from "@/types/inspector";

export const inspectorsService = {
  getInspectors: async (page: number = 1, pageSize: number = 10) => {
    return await apiClient<{ inspectors: Inspector[] }>(
      `inspectors?page=${page}&pageSize=${pageSize}`
    );
  },

  createInspector: async (
    inspectorData: Omit<Inspector, "id" | "createdAt" | "updatedAt">
  ) => {
    return await apiClient<{ success: boolean; data: Inspector }>(
      "inspectors",
      {
        method: "POST",
        body: JSON.stringify(inspectorData),
      }
    );
  },
};
