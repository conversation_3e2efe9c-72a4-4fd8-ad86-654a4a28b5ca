import { Schedule } from '../../schedules/entities/schedule.entity';
export declare class Inspector {
    id: number;
    name: string;
    email: string;
    phone: string;
    licenseNumber: string;
    licenseExpiry: Date;
    specializations: string[];
    rating: number;
    completedInspections: number;
    isActive: boolean;
    isAvailable: boolean;
    workingHours: {
        monday?: {
            start: string;
            end: string;
        };
        tuesday?: {
            start: string;
            end: string;
        };
        wednesday?: {
            start: string;
            end: string;
        };
        thursday?: {
            start: string;
            end: string;
        };
        friday?: {
            start: string;
            end: string;
        };
        saturday?: {
            start: string;
            end: string;
        };
        sunday?: {
            start: string;
            end: string;
        };
    };
    notes: string;
    createdAt: Date;
    updatedAt: Date;
    schedules: Schedule[];
}
