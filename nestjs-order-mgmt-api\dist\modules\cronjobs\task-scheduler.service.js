"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TaskSchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskSchedulerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cronjob_entity_1 = require("./entities/cronjob.entity");
const order_entity_1 = require("../orders/entities/order.entity");
const schedule_entity_1 = require("../schedules/entities/schedule.entity");
const email_service_1 = require("../email/email.service");
let TaskSchedulerService = TaskSchedulerService_1 = class TaskSchedulerService {
    constructor(orderRepository, scheduleRepository, emailService) {
        this.orderRepository = orderRepository;
        this.scheduleRepository = scheduleRepository;
        this.emailService = emailService;
        this.logger = new common_1.Logger(TaskSchedulerService_1.name);
    }
    async executeJob(cronjob) {
        this.logger.log(`Executing job: ${cronjob.name} (${cronjob.jobType})`);
        switch (cronjob.jobType) {
            case cronjob_entity_1.JobType.EMAIL_REMINDER:
                return this.sendEmailReminders();
            case cronjob_entity_1.JobType.CLEANUP_LOGS:
                return this.cleanupOldLogs();
            case cronjob_entity_1.JobType.GENERATE_REPORTS:
                return this.generateReports();
            case cronjob_entity_1.JobType.SYNC_DATA:
                return this.syncExternalData();
            case cronjob_entity_1.JobType.BACKUP_DATABASE:
                return this.backupDatabase();
            case cronjob_entity_1.JobType.UPDATE_SCHEDULES:
                return this.updateSchedules();
            case cronjob_entity_1.JobType.SEND_NOTIFICATIONS:
                return this.sendNotifications();
            case cronjob_entity_1.JobType.CUSTOM:
                return this.executeCustomJob(cronjob);
            default:
                throw new Error(`Unknown job type: ${cronjob.jobType}`);
        }
    }
    async sendEmailReminders() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowStr = tomorrow.toISOString().split('T')[0];
        const ordersForTomorrow = await this.orderRepository.find({
            where: {
                status: order_entity_1.OrderStatus.SCHEDULED,
                scheduledDate: tomorrowStr,
            },
            relations: ['property', 'inspector', 'client'],
        });
        const results = [];
        for (const order of ordersForTomorrow) {
            try {
                await this.emailService.sendTemplateEmail('inspection-reminder', order.clientEmail, {
                    clientName: order.clientName,
                    orderNumber: order.orderNumber,
                    propertyAddress: order.property?.addressLine1 || 'N/A',
                    inspectionDate: order.scheduledDate,
                    inspectionTime: order.scheduledTime,
                    inspectorName: order.inspector?.name || 'TBD',
                });
                results.push({ orderId: order.id, status: 'sent' });
            }
            catch (error) {
                this.logger.error(`Failed to send reminder for order ${order.id}:`, error.stack);
                results.push({ orderId: order.id, status: 'failed', error: error.message });
            }
        }
        return {
            jobType: 'email_reminder',
            processed: ordersForTomorrow.length,
            results,
        };
    }
    async cleanupOldLogs() {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const oldCompletedOrders = await this.orderRepository.count({
            where: {
                status: order_entity_1.OrderStatus.COMPLETED,
                completedAt: (0, typeorm_2.LessThan)(thirtyDaysAgo),
            },
        });
        this.logger.log(`Found ${oldCompletedOrders} old completed orders`);
        return {
            jobType: 'cleanup_logs',
            oldRecordsFound: oldCompletedOrders,
            message: 'Log cleanup completed',
        };
    }
    async generateReports() {
        const today = new Date();
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        const monthlyOrders = await this.orderRepository.count({
            where: {
                createdAt: (0, typeorm_2.Between)(startOfMonth, endOfMonth),
            },
        });
        const completedOrders = await this.orderRepository.count({
            where: {
                status: order_entity_1.OrderStatus.COMPLETED,
                completedAt: (0, typeorm_2.Between)(startOfMonth, endOfMonth),
            },
        });
        const report = {
            period: `${startOfMonth.toISOString().split('T')[0]} to ${endOfMonth.toISOString().split('T')[0]}`,
            totalOrders: monthlyOrders,
            completedOrders,
            completionRate: monthlyOrders > 0 ? (completedOrders / monthlyOrders * 100).toFixed(2) + '%' : '0%',
            generatedAt: new Date().toISOString(),
        };
        this.logger.log('Monthly report generated:', report);
        return {
            jobType: 'generate_reports',
            report,
        };
    }
    async syncExternalData() {
        this.logger.log('Syncing external data...');
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
            jobType: 'sync_data',
            syncedRecords: 0,
            message: 'External data sync completed',
        };
    }
    async backupDatabase() {
        this.logger.log('Creating database backup...');
        const backupInfo = {
            timestamp: new Date().toISOString(),
            tables: ['orders', 'schedules', 'inspectors', 'properties'],
            size: '0 MB',
        };
        return {
            jobType: 'backup_database',
            backup: backupInfo,
            message: 'Database backup completed',
        };
    }
    async updateSchedules() {
        const today = new Date().toISOString().split('T')[0];
        const pastSchedules = await this.scheduleRepository.update({
            date: (0, typeorm_2.LessThan)(today),
            available: true,
        }, {
            available: false,
        });
        return {
            jobType: 'update_schedules',
            updatedSchedules: pastSchedules.affected,
            message: 'Schedule updates completed',
        };
    }
    async sendNotifications() {
        const overdueOrders = await this.orderRepository.count({
            where: {
                status: order_entity_1.OrderStatus.SCHEDULED,
                scheduledDate: (0, typeorm_2.LessThan)(new Date().toISOString().split('T')[0]),
            },
        });
        if (overdueOrders > 0) {
            this.logger.warn(`Found ${overdueOrders} overdue orders`);
        }
        return {
            jobType: 'send_notifications',
            overdueOrders,
            notificationsSent: overdueOrders > 0 ? 1 : 0,
        };
    }
    async executeCustomJob(cronjob) {
        const config = cronjob.configuration || {};
        this.logger.log(`Executing custom job with config:`, config);
        return {
            jobType: 'custom',
            configuration: config,
            message: 'Custom job executed',
        };
    }
};
exports.TaskSchedulerService = TaskSchedulerService;
exports.TaskSchedulerService = TaskSchedulerService = TaskSchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_entity_1.Order)),
    __param(1, (0, typeorm_1.InjectRepository)(schedule_entity_1.Schedule)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        email_service_1.EmailService])
], TaskSchedulerService);
//# sourceMappingURL=task-scheduler.service.js.map