import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { fetchOrders, setPage } from '@/redux/slices/ordersSlice';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Order } from '@/types/order';

export default function OrdersExample() {
  const dispatch = useAppDispatch();
  const { items, currentPage, pageSize, totalPages, loading, error } = useAppSelector(state => state.orders);

  useEffect(() => {
    dispatch(fetchOrders({ page: currentPage, pageSize }));
  }, [dispatch, currentPage, pageSize]);

  const handlePageChange = (newPage: number) => {
    dispatch(setPage(newPage));
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy - h:mm a');
    } catch (e) {
      return 'Invalid date';
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'in progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'inspected':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return <div>Loading orders...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Orders (Redux Example)</h2>
      
      <div className="space-y-4">
        {items.length === 0 ? (
          <p>No orders found.</p>
        ) : (
          <ul className="space-y-4">
            {items.map((order: Order) => (
              <li key={order.id} className="p-4 border rounded-md bg-white shadow-sm">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-bold text-lg">{order.inspectionOrderId}</h3>
                    <p className="text-gray-600">{order.propertyAddress}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(order.status)}`}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </span>
                </div>
                
                <div className="mt-3 grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm text-gray-500">Client</p>
                    <p className="text-sm">{order.clientName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Property Type</p>
                    <p className="text-sm capitalize">{order.propertyType}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Inspection Date</p>
                    <p className="text-sm">{formatDate(order.inspectionDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Inspection Fee</p>
                    <p className="text-sm">${order.inspectionFees.toFixed(2)}</p>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
        
        {totalPages > 1 && (
          <div className="flex justify-between items-center mt-4">
            <Button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              variant="outline"
            >
              Previous
            </Button>
            <span>
              Page {currentPage} of {totalPages}
            </span>
            <Button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              variant="outline"
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
