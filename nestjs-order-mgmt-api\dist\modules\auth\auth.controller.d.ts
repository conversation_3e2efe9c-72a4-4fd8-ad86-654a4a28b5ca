import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { VerifyTokenDto } from './dto/verify-token.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: import("../users/entities/user.entity").UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    login(loginDto: LoginDto, user: any): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            role: any;
        };
    }>;
    refresh(refreshTokenDto: RefreshTokenDto): Promise<{
        access_token: string;
        refresh_token: string;
    }>;
    verify(verifyTokenDto: VerifyTokenDto): Promise<{
        valid: boolean;
        user: {
            id: number;
            email: string;
            name: string;
            role: import("../users/entities/user.entity").UserRole;
        };
    }>;
    getProfile(user: any): Promise<{
        id: number;
        name: string;
        email: string;
        phone: string;
        role: import("../users/entities/user.entity").UserRole;
        isActive: boolean;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
        orders: import("../orders/entities/order.entity").Order[];
    }>;
    logout(user: any): Promise<{
        message: string;
    }>;
}
