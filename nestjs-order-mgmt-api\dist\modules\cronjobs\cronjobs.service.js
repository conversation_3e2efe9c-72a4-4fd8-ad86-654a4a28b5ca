"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CronjobsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronjobsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const cron_1 = require("cron");
const cronjob_entity_1 = require("./entities/cronjob.entity");
const task_scheduler_service_1 = require("./task-scheduler.service");
let CronjobsService = CronjobsService_1 = class CronjobsService {
    constructor(cronjobRepository, schedulerRegistry, taskSchedulerService) {
        this.cronjobRepository = cronjobRepository;
        this.schedulerRegistry = schedulerRegistry;
        this.taskSchedulerService = taskSchedulerService;
        this.logger = new common_1.Logger(CronjobsService_1.name);
        this.initializeActiveCronjobs();
    }
    async create(createCronjobDto) {
        const cronjob = this.cronjobRepository.create(createCronjobDto);
        const savedCronjob = await this.cronjobRepository.save(cronjob);
        if (savedCronjob.isActive) {
            await this.scheduleJob(savedCronjob);
        }
        return {
            cronjob: savedCronjob,
            message: 'Cronjob created successfully',
        };
    }
    async findAll(query) {
        const { page = 1, limit = 10, isActive, jobType, search, sortBy = 'createdAt', sortOrder = 'DESC', } = query;
        const queryBuilder = this.cronjobRepository.createQueryBuilder('cronjob');
        if (isActive !== undefined) {
            queryBuilder.andWhere('cronjob.isActive = :isActive', { isActive });
        }
        if (jobType) {
            queryBuilder.andWhere('cronjob.jobType = :jobType', { jobType });
        }
        if (search) {
            queryBuilder.andWhere('(cronjob.name ILIKE :search OR cronjob.description ILIKE :search)', { search: `%${search}%` });
        }
        queryBuilder.orderBy(`cronjob.${sortBy}`, sortOrder);
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [cronjobs, total] = await queryBuilder.getManyAndCount();
        return {
            cronjobs,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const cronjob = await this.cronjobRepository.findOne({ where: { id } });
        if (!cronjob) {
            throw new common_1.NotFoundException('Cronjob not found');
        }
        return cronjob;
    }
    async update(id, updateCronjobDto) {
        const cronjob = await this.findOne(id);
        await this.cronjobRepository.update(id, updateCronjobDto);
        if (updateCronjobDto.schedule || updateCronjobDto.isActive !== undefined) {
            await this.unscheduleJob(cronjob.name);
            const updatedCronjob = await this.findOne(id);
            if (updatedCronjob.isActive) {
                await this.scheduleJob(updatedCronjob);
            }
        }
        const updatedCronjob = await this.findOne(id);
        return {
            cronjob: updatedCronjob,
            message: 'Cronjob updated successfully',
        };
    }
    async updateStatus(id, isActive) {
        const cronjob = await this.findOne(id);
        await this.cronjobRepository.update(id, { isActive });
        if (isActive) {
            await this.scheduleJob({ ...cronjob, isActive });
        }
        else {
            await this.unscheduleJob(cronjob.name);
        }
        return {
            message: `Cronjob ${isActive ? 'enabled' : 'disabled'} successfully`,
        };
    }
    async remove(id) {
        const cronjob = await this.findOne(id);
        await this.unscheduleJob(cronjob.name);
        await this.cronjobRepository.remove(cronjob);
        return {
            message: 'Cronjob deleted successfully',
        };
    }
    async runJob(id) {
        const cronjob = await this.findOne(id);
        try {
            await this.cronjobRepository.update(id, {
                status: cronjob_entity_1.JobStatus.RUNNING,
                lastRunAt: new Date(),
            });
            const result = await this.taskSchedulerService.executeJob(cronjob);
            await this.cronjobRepository.update(id, {
                status: cronjob_entity_1.JobStatus.COMPLETED,
                lastCompletedAt: new Date(),
                runCount: cronjob.runCount + 1,
            });
            this.logger.log(`Cronjob ${cronjob.name} executed successfully`);
            return {
                message: 'Cronjob executed successfully',
                result,
            };
        }
        catch (error) {
            await this.cronjobRepository.update(id, {
                status: cronjob_entity_1.JobStatus.FAILED,
                lastError: error.message,
                failureCount: cronjob.failureCount + 1,
            });
            this.logger.error(`Cronjob ${cronjob.name} failed:`, error.stack);
            throw error;
        }
    }
    async getSystemStatus() {
        const totalJobs = await this.cronjobRepository.count();
        const activeJobs = await this.cronjobRepository.count({
            where: { isActive: true },
        });
        const runningJobs = await this.cronjobRepository.count({
            where: { status: cronjob_entity_1.JobStatus.RUNNING },
        });
        const failedJobs = await this.cronjobRepository.count({
            where: { status: cronjob_entity_1.JobStatus.FAILED },
        });
        const recentExecutions = await this.cronjobRepository.find({
            where: { lastRunAt: (0, typeorm_2.LessThan)(new Date(Date.now() - 24 * 60 * 60 * 1000)) },
            order: { lastRunAt: 'DESC' },
            take: 10,
        });
        return {
            totalJobs,
            activeJobs,
            runningJobs,
            failedJobs,
            recentExecutions,
            systemHealth: failedJobs === 0 ? 'healthy' : 'degraded',
        };
    }
    async getLogs(jobId, limit = 100) {
        const queryBuilder = this.cronjobRepository.createQueryBuilder('cronjob');
        if (jobId) {
            queryBuilder.where('cronjob.id = :jobId', { jobId });
        }
        queryBuilder
            .select([
            'cronjob.id',
            'cronjob.name',
            'cronjob.lastRunAt',
            'cronjob.lastCompletedAt',
            'cronjob.status',
            'cronjob.lastError',
        ])
            .orderBy('cronjob.lastRunAt', 'DESC')
            .limit(limit);
        const logs = await queryBuilder.getMany();
        return {
            logs,
            total: logs.length,
        };
    }
    async getExecutionHistory(id, limit = 50) {
        const cronjob = await this.findOne(id);
        return {
            cronjobId: id,
            cronjobName: cronjob.name,
            totalRuns: cronjob.runCount,
            totalFailures: cronjob.failureCount,
            lastRun: cronjob.lastRunAt,
            lastCompleted: cronjob.lastCompletedAt,
            lastError: cronjob.lastError,
            successRate: cronjob.runCount > 0
                ? ((cronjob.runCount - cronjob.failureCount) / cronjob.runCount * 100).toFixed(2) + '%'
                : 'N/A',
        };
    }
    async cleanupLogs(olderThanDays) {
        const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
        const result = await this.cronjobRepository.update({ lastRunAt: (0, typeorm_2.LessThan)(cutoffDate) }, { lastError: null });
        return {
            message: `Cleaned up logs older than ${olderThanDays} days`,
            affectedRows: result.affected,
        };
    }
    async getAvailableJobTypes() {
        return Object.values(cronjob_entity_1.JobType).map(type => ({
            value: type,
            label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        }));
    }
    async initializeActiveCronjobs() {
        const activeCronjobs = await this.cronjobRepository.find({
            where: { isActive: true },
        });
        for (const cronjob of activeCronjobs) {
            await this.scheduleJob(cronjob);
        }
        this.logger.log(`Initialized ${activeCronjobs.length} active cronjobs`);
    }
    async scheduleJob(cronjob) {
        try {
            const job = new cron_1.CronJob(cronjob.schedule, async () => {
                await this.runJob(cronjob.id);
            });
            this.schedulerRegistry.addCronJob(cronjob.name, job);
            job.start();
            this.logger.log(`Scheduled cronjob: ${cronjob.name} with schedule: ${cronjob.schedule}`);
        }
        catch (error) {
            this.logger.error(`Failed to schedule cronjob ${cronjob.name}:`, error.stack);
        }
    }
    async unscheduleJob(jobName) {
        try {
            if (this.schedulerRegistry.doesExist('cron', jobName)) {
                this.schedulerRegistry.deleteCronJob(jobName);
                this.logger.log(`Unscheduled cronjob: ${jobName}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to unschedule cronjob ${jobName}:`, error.stack);
        }
    }
};
exports.CronjobsService = CronjobsService;
exports.CronjobsService = CronjobsService = CronjobsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cronjob_entity_1.Cronjob)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        schedule_1.SchedulerRegistry,
        task_scheduler_service_1.TaskSchedulerService])
], CronjobsService);
//# sourceMappingURL=cronjobs.service.js.map