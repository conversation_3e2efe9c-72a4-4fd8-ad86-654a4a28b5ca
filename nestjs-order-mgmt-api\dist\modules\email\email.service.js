"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EmailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const nodemailer = require("nodemailer");
const Handlebars = require("handlebars");
let EmailService = EmailService_1 = class EmailService {
    constructor(emailQueue) {
        this.emailQueue = emailQueue;
        this.logger = new common_1.Logger(EmailService_1.name);
        this.initializeTransporter();
    }
    initializeTransporter() {
        this.transporter = nodemailer.createTransporter({
            host: process.env.SMTP_HOST || 'localhost',
            port: parseInt(process.env.SMTP_PORT) || 587,
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
            },
        });
    }
    async sendEmail(sendEmailDto) {
        const job = await this.emailQueue.add('send-email', {
            ...sendEmailDto,
            priority: sendEmailDto.priority || 'normal',
        }, {
            priority: this.getPriorityValue(sendEmailDto.priority),
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 2000,
            },
        });
        return {
            jobId: job.id,
            message: 'Email queued for sending',
        };
    }
    async sendBulkEmail(bulkEmailDto) {
        const jobs = bulkEmailDto.recipients.map(recipient => ({
            to: recipient.email,
            subject: this.renderTemplate(bulkEmailDto.subject, recipient.variables || {}),
            text: bulkEmailDto.text ? this.renderTemplate(bulkEmailDto.text, recipient.variables || {}) : undefined,
            html: bulkEmailDto.html ? this.renderTemplate(bulkEmailDto.html, recipient.variables || {}) : undefined,
            template: bulkEmailDto.template,
            variables: { ...bulkEmailDto.globalVariables, ...recipient.variables },
            priority: bulkEmailDto.priority || 'normal',
        }));
        const queuedJobs = await Promise.all(jobs.map(job => this.emailQueue.add('send-email', job, {
            priority: this.getPriorityValue(job.priority),
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 2000,
            },
        })));
        return {
            jobIds: queuedJobs.map(job => job.id),
            totalEmails: jobs.length,
            message: 'Bulk emails queued for sending',
        };
    }
    async sendTemplateEmail(templateName, to, variables) {
        const templates = this.getEmailTemplates();
        const template = templates[templateName];
        if (!template) {
            throw new Error(`Template ${templateName} not found`);
        }
        const subject = this.renderTemplate(template.subject, variables);
        const html = this.renderTemplate(template.html, variables);
        const text = this.renderTemplate(template.text || '', variables);
        return this.sendEmail({
            to,
            subject,
            html,
            text,
            priority: 'normal',
        });
    }
    async getQueueStatus() {
        const waiting = await this.emailQueue.getWaiting();
        const active = await this.emailQueue.getActive();
        const completed = await this.emailQueue.getCompleted();
        const failed = await this.emailQueue.getFailed();
        return {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            total: waiting.length + active.length + completed.length + failed.length,
        };
    }
    async getJobStatus(jobId) {
        const job = await this.emailQueue.getJob(jobId);
        if (!job) {
            return { status: 'not_found' };
        }
        return {
            id: job.id,
            status: await job.getState(),
            progress: job.progress(),
            data: job.data,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
            failedReason: job.failedReason,
        };
    }
    async retryFailedJobs() {
        const failedJobs = await this.emailQueue.getFailed();
        for (const job of failedJobs) {
            await job.retry();
        }
        return {
            retriedJobs: failedJobs.length,
            message: 'Failed jobs have been retried',
        };
    }
    async clearQueue() {
        await this.emailQueue.clean(0, 'completed');
        await this.emailQueue.clean(0, 'failed');
        return {
            message: 'Queue cleared successfully',
        };
    }
    async processEmail(emailData) {
        try {
            let html = emailData.html;
            let text = emailData.text;
            if (emailData.template && emailData.variables) {
                const templates = this.getEmailTemplates();
                const template = templates[emailData.template];
                if (template) {
                    html = this.renderTemplate(template.html, emailData.variables);
                    text = this.renderTemplate(template.text || '', emailData.variables);
                }
            }
            const mailOptions = {
                from: process.env.SMTP_FROM || '<EMAIL>',
                to: emailData.to,
                subject: emailData.subject,
                text,
                html,
                attachments: emailData.attachments,
            };
            const result = await this.transporter.sendMail(mailOptions);
            this.logger.log(`Email sent successfully to ${emailData.to}`, result.messageId);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to send email to ${emailData.to}`, error.stack);
            throw error;
        }
    }
    renderTemplate(template, variables) {
        try {
            const compiledTemplate = Handlebars.compile(template);
            return compiledTemplate(variables);
        }
        catch (error) {
            this.logger.error('Template rendering failed', error.stack);
            return template;
        }
    }
    getPriorityValue(priority) {
        switch (priority) {
            case 'high': return 1;
            case 'normal': return 5;
            case 'low': return 10;
            default: return 5;
        }
    }
    getEmailTemplates() {
        return {
            'order-confirmation': {
                subject: 'Order Confirmation - {{orderNumber}}',
                html: `
          <h1>Order Confirmed</h1>
          <p>Dear {{clientName}},</p>
          <p>Your inspection order {{orderNumber}} has been confirmed.</p>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Scheduled Date:</strong> {{inspectionDate}}</p>
          <p><strong>Inspector:</strong> {{inspectorName}}</p>
          <p>Thank you for choosing our services!</p>
        `,
                text: `
          Order Confirmed
          
          Dear {{clientName}},
          
          Your inspection order {{orderNumber}} has been confirmed.
          
          Property: {{propertyAddress}}
          Scheduled Date: {{inspectionDate}}
          Inspector: {{inspectorName}}
          
          Thank you for choosing our services!
        `,
            },
            'inspection-reminder': {
                subject: 'Inspection Reminder - {{orderNumber}}',
                html: `
          <h1>Inspection Reminder</h1>
          <p>Dear {{clientName}},</p>
          <p>This is a reminder that your inspection is scheduled for tomorrow.</p>
          <p><strong>Order:</strong> {{orderNumber}}</p>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Date & Time:</strong> {{inspectionDate}} at {{inspectionTime}}</p>
          <p><strong>Inspector:</strong> {{inspectorName}}</p>
          <p>Please ensure someone is available to provide access to the property.</p>
        `,
                text: `
          Inspection Reminder
          
          Dear {{clientName}},
          
          This is a reminder that your inspection is scheduled for tomorrow.
          
          Order: {{orderNumber}}
          Property: {{propertyAddress}}
          Date & Time: {{inspectionDate}} at {{inspectionTime}}
          Inspector: {{inspectorName}}
          
          Please ensure someone is available to provide access to the property.
        `,
            },
            'inspection-completed': {
                subject: 'Inspection Completed - {{orderNumber}}',
                html: `
          <h1>Inspection Completed</h1>
          <p>Dear {{clientName}},</p>
          <p>Your property inspection has been completed.</p>
          <p><strong>Order:</strong> {{orderNumber}}</p>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Inspector:</strong> {{inspectorName}}</p>
          <p>Your inspection report will be available shortly. You will receive another email once it's ready for download.</p>
        `,
                text: `
          Inspection Completed
          
          Dear {{clientName}},
          
          Your property inspection has been completed.
          
          Order: {{orderNumber}}
          Property: {{propertyAddress}}
          Inspector: {{inspectorName}}
          
          Your inspection report will be available shortly. You will receive another email once it's ready for download.
        `,
            },
        };
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = EmailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)('email')),
    __metadata("design:paramtypes", [Object])
], EmailService);
//# sourceMappingURL=email.service.js.map