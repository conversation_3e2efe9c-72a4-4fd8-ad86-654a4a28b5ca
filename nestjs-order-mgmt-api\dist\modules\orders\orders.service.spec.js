"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const common_1 = require("@nestjs/common");
const orders_service_1 = require("./orders.service");
const order_entity_1 = require("./entities/order.entity");
const property_entity_1 = require("../properties/entities/property.entity");
const inspector_entity_1 = require("../inspectors/entities/inspector.entity");
const schedule_entity_1 = require("../schedules/entities/schedule.entity");
describe('OrdersService', () => {
    let service;
    let orderRepository;
    let propertyRepository;
    let inspectorRepository;
    let scheduleRepository;
    const mockOrderRepository = {
        create: jest.fn(),
        save: jest.fn(),
        find: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(),
        manager: {
            find: jest.fn(),
            findOne: jest.fn(),
        },
    };
    const mockPropertyRepository = {
        findOne: jest.fn(),
    };
    const mockInspectorRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        increment: jest.fn(),
    };
    const mockScheduleRepository = {
        findOne: jest.fn(),
        update: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                orders_service_1.OrdersService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(order_entity_1.Order),
                    useValue: mockOrderRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(property_entity_1.Property),
                    useValue: mockPropertyRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(inspector_entity_1.Inspector),
                    useValue: mockInspectorRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(schedule_entity_1.Schedule),
                    useValue: mockScheduleRepository,
                },
            ],
        }).compile();
        service = module.get(orders_service_1.OrdersService);
        orderRepository = module.get((0, typeorm_1.getRepositoryToken)(order_entity_1.Order));
        propertyRepository = module.get((0, typeorm_1.getRepositoryToken)(property_entity_1.Property));
        inspectorRepository = module.get((0, typeorm_1.getRepositoryToken)(inspector_entity_1.Inspector));
        scheduleRepository = module.get((0, typeorm_1.getRepositoryToken)(schedule_entity_1.Schedule));
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('create', () => {
        it('should create an order successfully', async () => {
            const createOrderDto = {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                clientIds: [1, 2],
                assignedInspectorIds: [1],
            };
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, orderNumber: 'ORD-2024-0001', ...createOrderDto };
            mockInspectorRepository.find.mockResolvedValue([{ id: 1, isActive: true }]);
            mockOrderRepository.manager.find.mockResolvedValue([
                { id: 1, role: 'client' },
                { id: 2, role: 'client' },
            ]);
            mockOrderRepository.create.mockReturnValue(mockOrder);
            mockOrderRepository.save.mockResolvedValue(mockOrder);
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const result = await service.create(createOrderDto, user);
            expect(result.order).toBeDefined();
            expect(result.message).toBe('Order created successfully');
            expect(mockOrderRepository.create).toHaveBeenCalled();
            expect(mockOrderRepository.save).toHaveBeenCalled();
        });
        it('should throw NotFoundException when inspector not found', async () => {
            const createOrderDto = {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                assignedInspectorIds: [999],
            };
            const user = { role: 'admin', userId: 1 };
            mockInspectorRepository.find.mockResolvedValue([]);
            await expect(service.create(createOrderDto, user)).rejects.toThrow(common_1.NotFoundException);
        });
        it('should handle client role creating order', async () => {
            const createOrderDto = {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
            };
            const user = { role: 'client', userId: 1 };
            const mockOrder = { id: 1, orderNumber: 'ORD-2024-0001', clientIds: [1] };
            mockOrderRepository.create.mockReturnValue(mockOrder);
            mockOrderRepository.save.mockResolvedValue(mockOrder);
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const result = await service.create(createOrderDto, user);
            expect(result.order.clientIds).toContain(1);
        });
    });
    describe('findOne', () => {
        it('should return order for admin user', async () => {
            const orderId = 1;
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, clientIds: [2], assignedInspectorIds: [3] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const result = await service.findOne(orderId, user);
            expect(result).toEqual(mockOrder);
        });
        it('should return order for authorized client', async () => {
            const orderId = 1;
            const user = { role: 'client', userId: 2 };
            const mockOrder = { id: 1, clientIds: [2], assignedInspectorIds: [3] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const result = await service.findOne(orderId, user);
            expect(result).toEqual(mockOrder);
        });
        it('should throw ForbiddenException for unauthorized client', async () => {
            const orderId = 1;
            const user = { role: 'client', userId: 999 };
            const mockOrder = { id: 1, clientIds: [2], assignedInspectorIds: [3] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            await expect(service.findOne(orderId, user)).rejects.toThrow(common_1.ForbiddenException);
        });
        it('should throw NotFoundException when order not found', async () => {
            const orderId = 999;
            const user = { role: 'admin', userId: 1 };
            mockOrderRepository.findOne.mockResolvedValue(null);
            await expect(service.findOne(orderId, user)).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('assignInspectors', () => {
        it('should assign inspectors to pending order', async () => {
            const orderId = 1;
            const inspectorIds = [1, 2];
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, status: order_entity_1.OrderStatus.PENDING, clientIds: [1] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            mockInspectorRepository.find.mockResolvedValue([
                { id: 1, isActive: true, isAvailable: true },
                { id: 2, isActive: true, isAvailable: true },
            ]);
            mockOrderRepository.update.mockResolvedValue({ affected: 1 });
            const result = await service.assignInspectors(orderId, inspectorIds, user);
            expect(result.message).toBe('Inspectors assigned successfully');
            expect(mockOrderRepository.update).toHaveBeenCalledWith(orderId, {
                assignedInspectorIds: inspectorIds,
                status: order_entity_1.OrderStatus.ASSIGNED,
            });
        });
        it('should throw BadRequestException for non-pending order', async () => {
            const orderId = 1;
            const inspectorIds = [1];
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, status: order_entity_1.OrderStatus.COMPLETED, clientIds: [1] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            await expect(service.assignInspectors(orderId, inspectorIds, user)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('cancelOrder', () => {
        it('should cancel order with reason', async () => {
            const orderId = 1;
            const reason = 'Client requested cancellation';
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, status: order_entity_1.OrderStatus.PENDING, clientIds: [1] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            mockOrderRepository.update.mockResolvedValue({ affected: 1 });
            const result = await service.cancelOrder(orderId, reason, user);
            expect(result.message).toBe('Order cancelled successfully');
            expect(mockOrderRepository.update).toHaveBeenCalledWith(orderId, {
                status: order_entity_1.OrderStatus.CANCELLED,
                cancellationReason: reason,
                cancelledAt: expect.any(Date),
            });
        });
        it('should throw BadRequestException for completed order', async () => {
            const orderId = 1;
            const reason = 'Test reason';
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, status: order_entity_1.OrderStatus.COMPLETED, clientIds: [1] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            await expect(service.cancelOrder(orderId, reason, user)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('addClientToOrder', () => {
        it('should add client to order', async () => {
            const orderId = 1;
            const clientId = 3;
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, clientIds: [1, 2] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            mockOrderRepository.manager.findOne.mockResolvedValue({ id: 3, role: 'client' });
            mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1, 2, 3] });
            const result = await service.addClientToOrder(orderId, clientId, user);
            expect(result.message).toBe('Client added to order successfully');
            expect(mockOrder.clientIds).toContain(clientId);
        });
        it('should throw BadRequestException when client already exists', async () => {
            const orderId = 1;
            const clientId = 1;
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, clientIds: [1, 2] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            await expect(service.addClientToOrder(orderId, clientId, user)).rejects.toThrow(common_1.BadRequestException);
        });
    });
    describe('removeClientFromOrder', () => {
        it('should remove client from order', async () => {
            const orderId = 1;
            const clientId = 2;
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, clientIds: [1, 2] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1] });
            const result = await service.removeClientFromOrder(orderId, clientId, user);
            expect(result.message).toBe('Client removed from order successfully');
            expect(mockOrder.clientIds).not.toContain(clientId);
        });
        it('should throw BadRequestException when client not in order', async () => {
            const orderId = 1;
            const clientId = 999;
            const user = { role: 'admin', userId: 1 };
            const mockOrder = { id: 1, clientIds: [1, 2] };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            await expect(service.removeClientFromOrder(orderId, clientId, user)).rejects.toThrow(common_1.BadRequestException);
        });
    });
});
//# sourceMappingURL=orders.service.spec.js.map