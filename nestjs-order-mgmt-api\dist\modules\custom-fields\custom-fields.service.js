"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomFieldsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const custom_field_entity_1 = require("./entities/custom-field.entity");
const custom_field_value_entity_1 = require("./entities/custom-field-value.entity");
let CustomFieldsService = class CustomFieldsService {
    constructor(customFieldRepository, customFieldValueRepository) {
        this.customFieldRepository = customFieldRepository;
        this.customFieldValueRepository = customFieldValueRepository;
    }
    async create(createCustomFieldDto) {
        const existingField = await this.customFieldRepository.findOne({
            where: {
                key: createCustomFieldDto.key,
                entityType: createCustomFieldDto.entityType,
            },
        });
        if (existingField) {
            throw new common_1.BadRequestException('Custom field with this key already exists for this entity type');
        }
        const customField = this.customFieldRepository.create(createCustomFieldDto);
        const savedField = await this.customFieldRepository.save(customField);
        return {
            customField: savedField,
            message: 'Custom field created successfully',
        };
    }
    async findAll(query, user) {
        const { entityType, type, isActive, search } = query;
        const queryBuilder = this.customFieldRepository.createQueryBuilder('field');
        if (entityType) {
            queryBuilder.andWhere('field.entityType = :entityType', { entityType });
        }
        if (type) {
            queryBuilder.andWhere('field.type = :type', { type });
        }
        if (isActive !== undefined) {
            queryBuilder.andWhere('field.isActive = :isActive', { isActive });
        }
        if (search) {
            queryBuilder.andWhere('(field.name ILIKE :search OR field.key ILIKE :search OR field.description ILIKE :search)', { search: `%${search}%` });
        }
        if (user.role !== 'admin') {
            queryBuilder.andWhere('(field.permissions IS NULL OR :userRole = ANY(field.permissions))', { userRole: user.role });
        }
        queryBuilder.orderBy('field.entityType', 'ASC');
        queryBuilder.addOrderBy('field.sortOrder', 'ASC');
        queryBuilder.addOrderBy('field.name', 'ASC');
        const customFields = await queryBuilder.getMany();
        return {
            customFields,
        };
    }
    async findOne(id) {
        const customField = await this.customFieldRepository.findOne({
            where: { id },
            relations: ['values'],
        });
        if (!customField) {
            throw new common_1.NotFoundException('Custom field not found');
        }
        return customField;
    }
    async getByEntityType(entityType, user) {
        const queryBuilder = this.customFieldRepository.createQueryBuilder('field');
        queryBuilder.where('field.entityType = :entityType', { entityType: entityType });
        queryBuilder.andWhere('field.isActive = :isActive', { isActive: true });
        if (user.role !== 'admin') {
            queryBuilder.andWhere('(field.permissions IS NULL OR :userRole = ANY(field.permissions))', { userRole: user.role });
        }
        queryBuilder.orderBy('field.sortOrder', 'ASC');
        queryBuilder.addOrderBy('field.name', 'ASC');
        const customFields = await queryBuilder.getMany();
        return {
            entityType,
            customFields,
        };
    }
    async getEntityValues(entityType, entityId, user) {
        const fields = await this.getByEntityType(entityType, user);
        const values = await this.customFieldValueRepository.find({
            where: { entityType, entityId },
            relations: ['customField'],
        });
        const fieldsWithValues = fields.customFields.map(field => {
            const value = values.find(v => v.customFieldId === field.id);
            return {
                ...field,
                value: value ? this.parseValue(value, field.type) : field.defaultValue,
                hasValue: !!value,
            };
        });
        return {
            entityType,
            entityId,
            fields: fieldsWithValues,
        };
    }
    async update(id, updateCustomFieldDto) {
        const customField = await this.customFieldRepository.findOne({ where: { id } });
        if (!customField) {
            throw new common_1.NotFoundException('Custom field not found');
        }
        await this.customFieldRepository.update(id, updateCustomFieldDto);
        const updatedField = await this.findOne(id);
        return {
            customField: updatedField,
            message: 'Custom field updated successfully',
        };
    }
    async updateStatus(id, isActive) {
        const customField = await this.customFieldRepository.findOne({ where: { id } });
        if (!customField) {
            throw new common_1.NotFoundException('Custom field not found');
        }
        await this.customFieldRepository.update(id, { isActive });
        return {
            message: `Custom field ${isActive ? 'activated' : 'deactivated'} successfully`,
        };
    }
    async remove(id) {
        const customField = await this.customFieldRepository.findOne({ where: { id } });
        if (!customField) {
            throw new common_1.NotFoundException('Custom field not found');
        }
        await this.customFieldValueRepository.delete({ customFieldId: id });
        await this.customFieldRepository.remove(customField);
        return {
            message: 'Custom field deleted successfully',
        };
    }
    async setValue(setValueDto, user) {
        const { customFieldId, entityType, entityId, value } = setValueDto;
        const customField = await this.customFieldRepository.findOne({
            where: { id: customFieldId },
        });
        if (!customField) {
            throw new common_1.NotFoundException('Custom field not found');
        }
        if (!this.hasPermission(customField, user)) {
            throw new common_1.ForbiddenException('You do not have permission to modify this field');
        }
        const validation = this.validateValue(value, customField);
        if (!validation.isValid) {
            throw new common_1.BadRequestException(`Invalid value: ${validation.errors.join(', ')}`);
        }
        let fieldValue = await this.customFieldValueRepository.findOne({
            where: { customFieldId, entityType, entityId },
        });
        if (!fieldValue) {
            fieldValue = this.customFieldValueRepository.create({
                customFieldId,
                entityType,
                entityId,
            });
        }
        if (this.isComplexType(customField.type)) {
            fieldValue.jsonValue = value;
            fieldValue.value = JSON.stringify(value);
        }
        else {
            fieldValue.value = String(value);
            fieldValue.jsonValue = null;
        }
        await this.customFieldValueRepository.save(fieldValue);
        return {
            message: 'Custom field value set successfully',
            value: this.parseValue(fieldValue, customField.type),
        };
    }
    async setEntityValues(entityType, entityId, values, user) {
        const results = [];
        const fields = await this.customFieldRepository.find({
            where: { entityType: entityType, isActive: true },
        });
        for (const [fieldKey, value] of Object.entries(values)) {
            const field = fields.find(f => f.key === fieldKey);
            if (!field) {
                results.push({ fieldKey, success: false, error: 'Field not found' });
                continue;
            }
            try {
                await this.setValue({
                    customFieldId: field.id,
                    entityType,
                    entityId,
                    value,
                }, user);
                results.push({ fieldKey, success: true });
            }
            catch (error) {
                results.push({ fieldKey, success: false, error: error.message });
            }
        }
        return {
            results,
            message: 'Custom field values update completed',
        };
    }
    async deleteValue(entityType, entityId, fieldId, user) {
        const customField = await this.customFieldRepository.findOne({
            where: { id: fieldId },
        });
        if (!customField) {
            throw new common_1.NotFoundException('Custom field not found');
        }
        if (!this.hasPermission(customField, user)) {
            throw new common_1.ForbiddenException('You do not have permission to modify this field');
        }
        const result = await this.customFieldValueRepository.delete({
            customFieldId: fieldId,
            entityType,
            entityId,
        });
        if (result.affected === 0) {
            throw new common_1.NotFoundException('Custom field value not found');
        }
        return {
            message: 'Custom field value deleted successfully',
        };
    }
    async duplicate(id) {
        const customField = await this.findOne(id);
        const duplicatedField = this.customFieldRepository.create({
            ...customField,
            id: undefined,
            name: `${customField.name} (Copy)`,
            key: `${customField.key}_copy_${Date.now()}`,
            values: undefined,
        });
        const savedField = await this.customFieldRepository.save(duplicatedField);
        return {
            customField: savedField,
            message: 'Custom field duplicated successfully',
        };
    }
    async getAvailableTypes() {
        return Object.values(custom_field_entity_1.CustomFieldType).map(type => ({
            value: type,
            label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        }));
    }
    async getAvailableEntities() {
        return Object.values(custom_field_entity_1.CustomFieldEntity).map(entity => ({
            value: entity,
            label: entity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        }));
    }
    hasPermission(customField, user) {
        if (user.role === 'admin') {
            return true;
        }
        if (!customField.permissions || customField.permissions.length === 0) {
            return true;
        }
        return customField.permissions.includes(user.role);
    }
    validateValue(value, customField) {
        const errors = [];
        if (customField.isRequired && (value === null || value === undefined || value === '')) {
            errors.push('This field is required');
        }
        if (value !== null && value !== undefined && value !== '') {
            switch (customField.type) {
                case custom_field_entity_1.CustomFieldType.NUMBER:
                    if (isNaN(Number(value))) {
                        errors.push('Value must be a number');
                    }
                    break;
                case custom_field_entity_1.CustomFieldType.EMAIL:
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(String(value))) {
                        errors.push('Value must be a valid email address');
                    }
                    break;
                case custom_field_entity_1.CustomFieldType.URL:
                    try {
                        new URL(String(value));
                    }
                    catch {
                        errors.push('Value must be a valid URL');
                    }
                    break;
            }
        }
        if (customField.options?.validation) {
            const validation = customField.options.validation;
            if (validation.minLength && String(value).length < validation.minLength) {
                errors.push(`Value must be at least ${validation.minLength} characters long`);
            }
            if (validation.maxLength && String(value).length > validation.maxLength) {
                errors.push(`Value must be no more than ${validation.maxLength} characters long`);
            }
            if (validation.pattern) {
                const regex = new RegExp(validation.pattern);
                if (!regex.test(String(value))) {
                    errors.push('Value does not match the required pattern');
                }
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    parseValue(fieldValue, fieldType) {
        if (this.isComplexType(fieldType)) {
            return fieldValue.jsonValue;
        }
        const value = fieldValue.value;
        if (!value)
            return null;
        switch (fieldType) {
            case custom_field_entity_1.CustomFieldType.NUMBER:
                return parseFloat(value);
            case custom_field_entity_1.CustomFieldType.BOOLEAN:
                return value === 'true';
            case custom_field_entity_1.CustomFieldType.DATE:
            case custom_field_entity_1.CustomFieldType.DATETIME:
                return new Date(value);
            default:
                return value;
        }
    }
    isComplexType(fieldType) {
        return [
            custom_field_entity_1.CustomFieldType.MULTISELECT,
            custom_field_entity_1.CustomFieldType.CHECKBOX,
            custom_field_entity_1.CustomFieldType.JSON,
        ].includes(fieldType);
    }
};
exports.CustomFieldsService = CustomFieldsService;
exports.CustomFieldsService = CustomFieldsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(custom_field_entity_1.CustomField)),
    __param(1, (0, typeorm_1.InjectRepository)(custom_field_value_entity_1.CustomFieldValue)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CustomFieldsService);
//# sourceMappingURL=custom-fields.service.js.map