"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkEmailDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class EmailRecipientDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient email address',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], EmailRecipientDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient-specific variables',
        example: { name: 'John Doe', orderNumber: 'ORD-001' },
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], EmailRecipientDto.prototype, "variables", void 0);
class BulkEmailDto {
    constructor() {
        this.priority = 'normal';
    }
}
exports.BulkEmailDto = BulkEmailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of email recipients',
        type: [EmailRecipientDto],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => EmailRecipientDto),
    __metadata("design:type", Array)
], BulkEmailDto.prototype, "recipients", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email subject template',
        example: 'Order Confirmation - {{orderNumber}}',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], BulkEmailDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Plain text email template',
        example: 'Dear {{name}}, your order {{orderNumber}} has been confirmed.',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkEmailDto.prototype, "text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'HTML email template',
        example: '<h1>Dear {{name}}</h1><p>Your order {{orderNumber}} has been confirmed.</p>',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkEmailDto.prototype, "html", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email template name',
        example: 'order-confirmation',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkEmailDto.prototype, "template", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Global variables applied to all emails',
        example: { companyName: 'ABC Inspections', supportEmail: '<EMAIL>' },
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], BulkEmailDto.prototype, "globalVariables", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email priority',
        enum: ['low', 'normal', 'high'],
        default: 'normal',
        required: false,
    }),
    (0, class_validator_1.IsEnum)(['low', 'normal', 'high']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], BulkEmailDto.prototype, "priority", void 0);
//# sourceMappingURL=bulk-email.dto.js.map