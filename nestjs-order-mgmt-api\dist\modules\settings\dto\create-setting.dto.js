"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSettingDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const setting_entity_1 = require("../entities/setting.entity");
class SettingOptionsDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Minimum value for number type', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], SettingOptionsDto.prototype, "min", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Maximum value for number type', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], SettingOptionsDto.prototype, "max", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Available choices for select type',
        type: [Object],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], SettingOptionsDto.prototype, "choices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Placeholder text', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SettingOptionsDto.prototype, "placeholder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Help text', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SettingOptionsDto.prototype, "helpText", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Validation regex pattern', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SettingOptionsDto.prototype, "validation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Should value be encrypted', default: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], SettingOptionsDto.prototype, "encrypted", void 0);
class CreateSettingDto {
    constructor() {
        this.isRequired = false;
        this.isSecret = false;
        this.isEditable = true;
        this.isVisible = true;
        this.sortOrder = 0;
    }
}
exports.CreateSettingDto = CreateSettingDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting unique key',
        example: 'company_name',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting display name',
        example: 'Company Name',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting description',
        example: 'The name of your company',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting type',
        enum: setting_entity_1.SettingType,
        example: setting_entity_1.SettingType.STRING,
    }),
    (0, class_validator_1.IsEnum)(setting_entity_1.SettingType),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting category',
        enum: setting_entity_1.SettingCategory,
        example: setting_entity_1.SettingCategory.GENERAL,
    }),
    (0, class_validator_1.IsEnum)(setting_entity_1.SettingCategory),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting value',
        example: 'ABC Inspection Services',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Default value',
        example: 'My Company',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "defaultValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting options',
        type: SettingOptionsDto,
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SettingOptionsDto),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", SettingOptionsDto)
], CreateSettingDto.prototype, "options", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is setting required',
        default: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateSettingDto.prototype, "isRequired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is setting secret (encrypted)',
        default: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateSettingDto.prototype, "isSecret", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is setting editable',
        default: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateSettingDto.prototype, "isEditable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is setting visible in UI',
        default: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateSettingDto.prototype, "isVisible", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sort order',
        default: 0,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateSettingDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Setting group',
        example: 'Company Information',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSettingDto.prototype, "group", void 0);
//# sourceMappingURL=create-setting.dto.js.map