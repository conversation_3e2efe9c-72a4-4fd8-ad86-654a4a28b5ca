"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronjobsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const cronjobs_service_1 = require("./cronjobs.service");
const create_cronjob_dto_1 = require("./dto/create-cronjob.dto");
const update_cronjob_dto_1 = require("./dto/update-cronjob.dto");
const cronjob_query_dto_1 = require("./dto/cronjob-query.dto");
const auth_decorator_1 = require("../../common/decorators/auth.decorator");
let CronjobsController = class CronjobsController {
    constructor(cronjobsService) {
        this.cronjobsService = cronjobsService;
    }
    async create(createCronjobDto) {
        return this.cronjobsService.create(createCronjobDto);
    }
    async findAll(query) {
        return this.cronjobsService.findAll(query);
    }
    async getSystemStatus() {
        return this.cronjobsService.getSystemStatus();
    }
    async getLogs(jobId, limit) {
        return this.cronjobsService.getLogs(jobId, limit);
    }
    async findOne(id) {
        return this.cronjobsService.findOne(id);
    }
    async update(id, updateCronjobDto) {
        return this.cronjobsService.update(id, updateCronjobDto);
    }
    async enable(id) {
        return this.cronjobsService.updateStatus(id, true);
    }
    async disable(id) {
        return this.cronjobsService.updateStatus(id, false);
    }
    async runJob(id) {
        return this.cronjobsService.runJob(id);
    }
    async remove(id) {
        return this.cronjobsService.remove(id);
    }
    async getExecutionHistory(id, limit) {
        return this.cronjobsService.getExecutionHistory(id, limit);
    }
    async cleanupLogs(cleanupData) {
        return this.cronjobsService.cleanupLogs(cleanupData.olderThanDays);
    }
    async getAvailableJobTypes() {
        return this.cronjobsService.getAvailableJobTypes();
    }
};
exports.CronjobsController = CronjobsController;
__decorate([
    (0, common_1.Post)(),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new cronjob' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Cronjob successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cronjob_dto_1.CreateCronjobDto]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all cronjobs with filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjobs retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'isActive', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'jobType', required: false, type: String }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [cronjob_query_dto_1.CronjobQueryDto]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get cronjob system status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'System status retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "getSystemStatus", null);
__decorate([
    (0, common_1.Get)('logs'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get cronjob execution logs' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Logs retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'jobId', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    __param(0, (0, common_1.Query)('jobId')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "getLogs", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get cronjob by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjob retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cronjob not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update cronjob' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjob updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cronjob not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_cronjob_dto_1.UpdateCronjobDto]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/enable'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Enable cronjob' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjob enabled successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "enable", null);
__decorate([
    (0, common_1.Patch)(':id/disable'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Disable cronjob' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjob disabled successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "disable", null);
__decorate([
    (0, common_1.Post)(':id/run'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Manually run cronjob' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjob executed successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "runJob", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete cronjob' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cronjob deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Cronjob not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id/history'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get cronjob execution history' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution history retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "getExecutionHistory", null);
__decorate([
    (0, common_1.Post)('cleanup'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Cleanup old cronjob logs' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cleanup completed successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "cleanupLogs", null);
__decorate([
    (0, common_1.Get)('types/available'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available job types' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job types retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CronjobsController.prototype, "getAvailableJobTypes", null);
exports.CronjobsController = CronjobsController = __decorate([
    (0, swagger_1.ApiTags)('Cronjobs'),
    (0, common_1.Controller)('cronjobs'),
    __metadata("design:paramtypes", [cronjobs_service_1.CronjobsService])
], CronjobsController);
//# sourceMappingURL=cronjobs.controller.js.map