"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomFieldsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const custom_fields_service_1 = require("./custom-fields.service");
const create_custom_field_dto_1 = require("./dto/create-custom-field.dto");
const update_custom_field_dto_1 = require("./dto/update-custom-field.dto");
const custom_field_query_dto_1 = require("./dto/custom-field-query.dto");
const set_custom_field_value_dto_1 = require("./dto/set-custom-field-value.dto");
const auth_decorator_1 = require("../../common/decorators/auth.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let CustomFieldsController = class CustomFieldsController {
    constructor(customFieldsService) {
        this.customFieldsService = customFieldsService;
    }
    async create(createCustomFieldDto) {
        return this.customFieldsService.create(createCustomFieldDto);
    }
    async findAll(query, user) {
        return this.customFieldsService.findAll(query, user);
    }
    async getByEntityType(entityType, user) {
        return this.customFieldsService.getByEntityType(entityType, user);
    }
    async getEntityValues(entityType, entityId, user) {
        return this.customFieldsService.getEntityValues(entityType, entityId, user);
    }
    async findOne(id) {
        return this.customFieldsService.findOne(id);
    }
    async update(id, updateCustomFieldDto) {
        return this.customFieldsService.update(id, updateCustomFieldDto);
    }
    async activate(id) {
        return this.customFieldsService.updateStatus(id, true);
    }
    async deactivate(id) {
        return this.customFieldsService.updateStatus(id, false);
    }
    async remove(id) {
        return this.customFieldsService.remove(id);
    }
    async setValue(setValueDto, user) {
        return this.customFieldsService.setValue(setValueDto, user);
    }
    async setEntityValues(entityType, entityId, values, user) {
        return this.customFieldsService.setEntityValues(entityType, entityId, values, user);
    }
    async deleteValue(entityType, entityId, fieldId, user) {
        return this.customFieldsService.deleteValue(entityType, entityId, fieldId, user);
    }
    async getAvailableTypes() {
        return this.customFieldsService.getAvailableTypes();
    }
    async getAvailableEntities() {
        return this.customFieldsService.getAvailableEntities();
    }
    async duplicate(id) {
        return this.customFieldsService.duplicate(id);
    }
};
exports.CustomFieldsController = CustomFieldsController;
__decorate([
    (0, common_1.Post)(),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new custom field' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Custom field successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_custom_field_dto_1.CreateCustomFieldDto]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all custom fields with filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom fields retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'entityType', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'isActive', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [custom_field_query_dto_1.CustomFieldQueryDto, Object]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('entity/:entityType'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get custom fields for specific entity type' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom fields retrieved successfully' }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "getByEntityType", null);
__decorate([
    (0, common_1.Get)('entity/:entityType/:entityId/values'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get custom field values for specific entity' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field values retrieved successfully' }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseIntPipe)),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Object]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "getEntityValues", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get custom field by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Custom field not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Update custom field' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Custom field not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_custom_field_dto_1.UpdateCustomFieldDto]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/activate'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Activate custom field' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field activated successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "activate", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Deactivate custom field' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field deactivated successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "deactivate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete custom field' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Custom field not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('values'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Set custom field value' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field value set successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [set_custom_field_value_dto_1.SetCustomFieldValueDto, Object]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "setValue", null);
__decorate([
    (0, common_1.Post)('entity/:entityType/:entityId/values'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Set multiple custom field values for entity' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field values set successfully' }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Object, Object]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "setEntityValues", null);
__decorate([
    (0, common_1.Delete)('values/:entityType/:entityId/:fieldId'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete custom field value' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Custom field value deleted successfully' }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Param)('fieldId', common_1.ParseIntPipe)),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, Object]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "deleteValue", null);
__decorate([
    (0, common_1.Get)('types/available'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available custom field types' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Field types retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "getAvailableTypes", null);
__decorate([
    (0, common_1.Get)('entities/available'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available entity types' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Entity types retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "getAvailableEntities", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate custom field' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Custom field duplicated successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CustomFieldsController.prototype, "duplicate", null);
exports.CustomFieldsController = CustomFieldsController = __decorate([
    (0, swagger_1.ApiTags)('Custom Fields'),
    (0, common_1.Controller)('custom-fields'),
    __metadata("design:paramtypes", [custom_fields_service_1.CustomFieldsService])
], CustomFieldsController);
//# sourceMappingURL=custom-fields.controller.js.map