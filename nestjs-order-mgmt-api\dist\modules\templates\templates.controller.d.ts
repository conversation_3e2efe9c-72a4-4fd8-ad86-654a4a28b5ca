import { TemplatesService } from './templates.service';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { TemplateQueryDto } from './dto/template-query.dto';
import { RenderTemplateDto } from './dto/render-template.dto';
export declare class TemplatesController {
    private readonly templatesService;
    constructor(templatesService: TemplatesService);
    create(createTemplateDto: CreateTemplateDto): Promise<{
        template: import("./entities/template.entity").Template;
        message: string;
    }>;
    findAll(query: TemplateQueryDto): Promise<{
        templates: import("./entities/template.entity").Template[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getCategories(): Promise<{
        value: import("./entities/template.entity").TemplateCategory;
        label: string;
    }[]>;
    getAvailableVariables(): Promise<{
        name: string;
        description: string;
        type: string;
    }[]>;
    findOne(id: number): Promise<import("./entities/template.entity").Template>;
    render(id: number, renderData: RenderTemplateDto): Promise<{
        subject: string;
        content: string;
        htmlContent: any;
        type: import("./entities/template.entity").TemplateType;
        category: import("./entities/template.entity").TemplateCategory;
    }>;
    preview(id: number): Promise<{
        subject: string;
        content: string;
        htmlContent: any;
        type: import("./entities/template.entity").TemplateType;
        category: import("./entities/template.entity").TemplateCategory;
    }>;
    duplicate(id: number): Promise<{
        template: import("./entities/template.entity").Template;
        message: string;
    }>;
    update(id: number, updateTemplateDto: UpdateTemplateDto): Promise<{
        template: import("./entities/template.entity").Template;
        message: string;
    }>;
    activate(id: number): Promise<{
        message: string;
    }>;
    deactivate(id: number): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findByCategory(category: string): Promise<import("./entities/template.entity").Template[]>;
    validateTemplate(templateData: {
        content: string;
        htmlContent?: string;
    }): Promise<{
        isValid: boolean;
        errors: string[];
    }>;
}
