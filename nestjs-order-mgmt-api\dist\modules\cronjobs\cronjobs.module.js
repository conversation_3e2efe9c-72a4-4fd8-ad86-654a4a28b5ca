"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronjobsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const bull_1 = require("@nestjs/bull");
const cronjobs_controller_1 = require("./cronjobs.controller");
const cronjobs_service_1 = require("./cronjobs.service");
const task_scheduler_service_1 = require("./task-scheduler.service");
const cronjob_entity_1 = require("./entities/cronjob.entity");
let CronjobsModule = class CronjobsModule {
};
exports.CronjobsModule = CronjobsModule;
exports.CronjobsModule = CronjobsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cronjob_entity_1.Cronjob]),
            bull_1.BullModule.registerQueue({
                name: 'cronjobs',
            }),
        ],
        controllers: [cronjobs_controller_1.CronjobsController],
        providers: [cronjobs_service_1.CronjobsService, task_scheduler_service_1.TaskSchedulerService],
        exports: [cronjobs_service_1.CronjobsService, task_scheduler_service_1.TaskSchedulerService],
    })
], CronjobsModule);
//# sourceMappingURL=cronjobs.module.js.map