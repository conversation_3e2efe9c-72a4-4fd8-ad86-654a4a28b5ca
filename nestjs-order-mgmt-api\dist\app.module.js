"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const schedule_1 = require("@nestjs/schedule");
const bull_1 = require("@nestjs/bull");
const throttler_1 = require("@nestjs/throttler");
const auth_module_1 = require("./modules/auth/auth.module");
const users_module_1 = require("./modules/users/users.module");
const orders_module_1 = require("./modules/orders/orders.module");
const inspectors_module_1 = require("./modules/inspectors/inspectors.module");
const schedules_module_1 = require("./modules/schedules/schedules.module");
const properties_module_1 = require("./modules/properties/properties.module");
const email_module_1 = require("./modules/email/email.module");
const cronjobs_module_1 = require("./modules/cronjobs/cronjobs.module");
const templates_module_1 = require("./modules/templates/templates.module");
const settings_module_1 = require("./modules/settings/settings.module");
const custom_fields_module_1 = require("./modules/custom-fields/custom-fields.module");
const database_config_1 = require("./config/database.config");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                useFactory: database_config_1.databaseConfig,
            }),
            schedule_1.ScheduleModule.forRoot(),
            bull_1.BullModule.forRoot({
                redis: {
                    host: process.env.REDIS_HOST || 'localhost',
                    port: parseInt(process.env.REDIS_PORT) || 6379,
                },
            }),
            throttler_1.ThrottlerModule.forRoot([
                {
                    ttl: 60000,
                    limit: 100,
                },
            ]),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            orders_module_1.OrdersModule,
            inspectors_module_1.InspectorsModule,
            schedules_module_1.SchedulesModule,
            properties_module_1.PropertiesModule,
            email_module_1.EmailModule,
            cronjobs_module_1.CronjobsModule,
            templates_module_1.TemplatesModule,
            settings_module_1.SettingsModule,
            custom_fields_module_1.CustomFieldsModule,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map