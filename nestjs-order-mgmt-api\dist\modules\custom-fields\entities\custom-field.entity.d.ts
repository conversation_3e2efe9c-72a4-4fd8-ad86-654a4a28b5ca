import { CustomFieldValue } from './custom-field-value.entity';
export declare enum CustomFieldType {
    TEXT = "text",
    TEXTAREA = "textarea",
    NUMBER = "number",
    EMAIL = "email",
    PHONE = "phone",
    URL = "url",
    DATE = "date",
    DATETIME = "datetime",
    BOOLEAN = "boolean",
    SELECT = "select",
    MULTISELECT = "multiselect",
    RADIO = "radio",
    CHECKBOX = "checkbox",
    FILE = "file",
    IMAGE = "image",
    JSON = "json"
}
export declare enum CustomFieldEntity {
    ORDER = "order",
    PROPERTY = "property",
    INSPECTOR = "inspector",
    USER = "user",
    SCHEDULE = "schedule"
}
export declare class CustomField {
    id: number;
    name: string;
    key: string;
    description: string;
    type: CustomFieldType;
    entityType: CustomFieldEntity;
    options: {
        choices?: {
            value: string;
            label: string;
        }[];
        placeholder?: string;
        helpText?: string;
        validation?: {
            required?: boolean;
            minLength?: number;
            maxLength?: number;
            min?: number;
            max?: number;
            pattern?: string;
        };
        display?: {
            width?: string;
            columns?: number;
            rows?: number;
        };
    };
    defaultValue: string;
    isRequired: boolean;
    isActive: boolean;
    isVisible: boolean;
    isSearchable: boolean;
    sortOrder: number;
    group: string;
    permissions: string[];
    createdAt: Date;
    updatedAt: Date;
    values: CustomFieldValue[];
}
