import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { RegisterDto } from './dto/register.dto';
export declare class AuthService {
    private readonly userRepository;
    private readonly jwtService;
    constructor(userRepository: Repository<User>, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    register(registerDto: RegisterDto): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: import("../users/entities/user.entity").UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            notes: string;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    login(user: any): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            role: any;
        };
    }>;
    refreshToken(refreshToken: string): Promise<{
        access_token: string;
        refresh_token: string;
    }>;
    verifyToken(token: string): Promise<{
        valid: boolean;
        user: {
            id: number;
            email: string;
            name: string;
            role: import("../users/entities/user.entity").UserRole;
        };
    }>;
    getProfile(userId: number): Promise<{
        id: number;
        name: string;
        email: string;
        phone: string;
        role: import("../users/entities/user.entity").UserRole;
        isActive: boolean;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
        notes: string;
        orders: import("../orders/entities/order.entity").Order[];
    }>;
}
