import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import * as nodemailer from 'nodemailer';
import * as Handlebars from 'handlebars';

import { SendEmailDto } from './dto/send-email.dto';
import { BulkEmailDto } from './dto/bulk-email.dto';

export interface EmailJob {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  template?: string;
  variables?: Record<string, any>;
  attachments?: any[];
  priority?: 'low' | 'normal' | 'high';
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(
    @InjectQueue('email') private emailQueue: Queue,
  ) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  async sendEmail(sendEmailDto: SendEmailDto) {
    const job = await this.emailQueue.add('send-email', {
      ...sendEmailDto,
      priority: sendEmailDto.priority || 'normal',
    }, {
      priority: this.getPriorityValue(sendEmailDto.priority),
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    });

    return {
      jobId: job.id,
      message: 'Email queued for sending',
    };
  }

  async sendBulkEmail(bulkEmailDto: BulkEmailDto) {
    const jobs = bulkEmailDto.recipients.map(recipient => ({
      to: recipient.email,
      subject: this.renderTemplate(bulkEmailDto.subject, recipient.variables || {}),
      text: bulkEmailDto.text ? this.renderTemplate(bulkEmailDto.text, recipient.variables || {}) : undefined,
      html: bulkEmailDto.html ? this.renderTemplate(bulkEmailDto.html, recipient.variables || {}) : undefined,
      template: bulkEmailDto.template,
      variables: { ...bulkEmailDto.globalVariables, ...recipient.variables },
      priority: bulkEmailDto.priority || 'normal',
    }));

    const queuedJobs = await Promise.all(
      jobs.map(job => 
        this.emailQueue.add('send-email', job, {
          priority: this.getPriorityValue(job.priority),
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        })
      )
    );

    return {
      jobIds: queuedJobs.map(job => job.id),
      totalEmails: jobs.length,
      message: 'Bulk emails queued for sending',
    };
  }

  async sendTemplateEmail(templateName: string, to: string | string[], variables: Record<string, any>) {
    // This would typically load template from database
    // For now, using a simple template system
    const templates = this.getEmailTemplates();
    const template = templates[templateName];

    if (!template) {
      throw new Error(`Template ${templateName} not found`);
    }

    const subject = this.renderTemplate(template.subject, variables);
    const html = this.renderTemplate(template.html, variables);
    const text = this.renderTemplate(template.text || '', variables);

    return this.sendEmail({
      to,
      subject,
      html,
      text,
      priority: 'normal',
    });
  }

  async getQueueStatus() {
    const waiting = await this.emailQueue.getWaiting();
    const active = await this.emailQueue.getActive();
    const completed = await this.emailQueue.getCompleted();
    const failed = await this.emailQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length,
    };
  }

  async getJobStatus(jobId: string) {
    const job = await this.emailQueue.getJob(jobId);
    
    if (!job) {
      return { status: 'not_found' };
    }

    return {
      id: job.id,
      status: await job.getState(),
      progress: job.progress(),
      data: job.data,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
      failedReason: job.failedReason,
    };
  }

  async retryFailedJobs() {
    const failedJobs = await this.emailQueue.getFailed();
    
    for (const job of failedJobs) {
      await job.retry();
    }

    return {
      retriedJobs: failedJobs.length,
      message: 'Failed jobs have been retried',
    };
  }

  async clearQueue() {
    await this.emailQueue.clean(0, 'completed');
    await this.emailQueue.clean(0, 'failed');

    return {
      message: 'Queue cleared successfully',
    };
  }

  // This method is called by the email processor
  async processEmail(emailData: EmailJob) {
    try {
      let html = emailData.html;
      let text = emailData.text;

      // If template is specified, render it
      if (emailData.template && emailData.variables) {
        const templates = this.getEmailTemplates();
        const template = templates[emailData.template];
        
        if (template) {
          html = this.renderTemplate(template.html, emailData.variables);
          text = this.renderTemplate(template.text || '', emailData.variables);
        }
      }

      const mailOptions = {
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: emailData.to,
        subject: emailData.subject,
        text,
        html,
        attachments: emailData.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      this.logger.log(`Email sent successfully to ${emailData.to}`, result.messageId);
      return result;
    } catch (error) {
      this.logger.error(`Failed to send email to ${emailData.to}`, error.stack);
      throw error;
    }
  }

  private renderTemplate(template: string, variables: Record<string, any>): string {
    try {
      const compiledTemplate = Handlebars.compile(template);
      return compiledTemplate(variables);
    } catch (error) {
      this.logger.error('Template rendering failed', error.stack);
      return template; // Return original template if rendering fails
    }
  }

  private getPriorityValue(priority?: string): number {
    switch (priority) {
      case 'high': return 1;
      case 'normal': return 5;
      case 'low': return 10;
      default: return 5;
    }
  }

  private getEmailTemplates() {
    // This would typically come from a database or template service
    return {
      'order-confirmation': {
        subject: 'Order Confirmation - {{orderNumber}}',
        html: `
          <h1>Order Confirmed</h1>
          <p>Dear {{clientName}},</p>
          <p>Your inspection order {{orderNumber}} has been confirmed.</p>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Scheduled Date:</strong> {{inspectionDate}}</p>
          <p><strong>Inspector:</strong> {{inspectorName}}</p>
          <p>Thank you for choosing our services!</p>
        `,
        text: `
          Order Confirmed
          
          Dear {{clientName}},
          
          Your inspection order {{orderNumber}} has been confirmed.
          
          Property: {{propertyAddress}}
          Scheduled Date: {{inspectionDate}}
          Inspector: {{inspectorName}}
          
          Thank you for choosing our services!
        `,
      },
      'inspection-reminder': {
        subject: 'Inspection Reminder - {{orderNumber}}',
        html: `
          <h1>Inspection Reminder</h1>
          <p>Dear {{clientName}},</p>
          <p>This is a reminder that your inspection is scheduled for tomorrow.</p>
          <p><strong>Order:</strong> {{orderNumber}}</p>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Date & Time:</strong> {{inspectionDate}} at {{inspectionTime}}</p>
          <p><strong>Inspector:</strong> {{inspectorName}}</p>
          <p>Please ensure someone is available to provide access to the property.</p>
        `,
        text: `
          Inspection Reminder
          
          Dear {{clientName}},
          
          This is a reminder that your inspection is scheduled for tomorrow.
          
          Order: {{orderNumber}}
          Property: {{propertyAddress}}
          Date & Time: {{inspectionDate}} at {{inspectionTime}}
          Inspector: {{inspectorName}}
          
          Please ensure someone is available to provide access to the property.
        `,
      },
      'inspection-completed': {
        subject: 'Inspection Completed - {{orderNumber}}',
        html: `
          <h1>Inspection Completed</h1>
          <p>Dear {{clientName}},</p>
          <p>Your property inspection has been completed.</p>
          <p><strong>Order:</strong> {{orderNumber}}</p>
          <p><strong>Property:</strong> {{propertyAddress}}</p>
          <p><strong>Inspector:</strong> {{inspectorName}}</p>
          <p>Your inspection report will be available shortly. You will receive another email once it's ready for download.</p>
        `,
        text: `
          Inspection Completed
          
          Dear {{clientName}},
          
          Your property inspection has been completed.
          
          Order: {{orderNumber}}
          Property: {{propertyAddress}}
          Inspector: {{inspectorName}}
          
          Your inspection report will be available shortly. You will receive another email once it's ready for download.
        `,
      },
    };
  }
}
