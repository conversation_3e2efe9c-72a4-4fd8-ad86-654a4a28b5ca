import { getMockOrdersResponse } from '@/data/mockOrders';
import mockOrders from '@/data/mockOrders';
import { Order } from '@/types/order';

// Mock API client for development and testing
export const mockApiClient = async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Parse the endpoint
  const url = new URL(endpoint, 'http://localhost');
  const path = url.pathname;
  const searchParams = url.searchParams;

  // Handle different endpoints
  if (path === '/api/orders') {
    if (options.method === 'POST') {
      // Create a new order
      const orderData = JSON.parse(options.body as string);
      const newOrder: Order = {
        ...orderData,
        id: Math.max(...mockOrders.map(o => o.id)) + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      mockOrders.push(newOrder);
      
      return {
        success: true,
        data: {
          order: newOrder
        }
      } as unknown as T;
    } else {
      // Get orders with pagination
      const page = parseInt(searchParams.get('page') || '1');
      const pageSize = parseInt(searchParams.get('pageSize') || '10');
      
      return getMockOrdersResponse(page, pageSize) as unknown as T;
    }
  } else if (path.match(/\/api\/orders\/\d+/)) {
    const orderId = parseInt(path.split('/').pop() || '0');
    const orderIndex = mockOrders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      throw new Error('Order not found');
    }
    
    if (options.method === 'PATCH') {
      // Update an order
      const orderData = JSON.parse(options.body as string);
      const updatedOrder = {
        ...mockOrders[orderIndex],
        ...orderData,
        updatedAt: new Date().toISOString()
      };
      
      mockOrders[orderIndex] = updatedOrder;
      
      return {
        success: true,
        data: {
          order: updatedOrder
        }
      } as unknown as T;
    } else if (options.method === 'DELETE') {
      // Delete an order
      mockOrders.splice(orderIndex, 1);
      
      return {
        success: true
      } as unknown as T;
    } else {
      // Get a single order
      return {
        success: true,
        data: {
          order: mockOrders[orderIndex]
        }
      } as unknown as T;
    }
  }
  
  // Default response for unhandled endpoints
  throw new Error(`Unhandled endpoint: ${endpoint}`);
};
