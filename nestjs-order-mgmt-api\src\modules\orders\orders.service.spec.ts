import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';

import { OrdersService } from './orders.service';
import { Order, OrderStatus } from './entities/order.entity';
import { Property } from '../properties/entities/property.entity';
import { Inspector } from '../inspectors/entities/inspector.entity';
import { Schedule } from '../schedules/entities/schedule.entity';

describe('OrdersService', () => {
  let service: OrdersService;
  let orderRepository: Repository<Order>;
  let propertyRepository: Repository<Property>;
  let inspectorRepository: Repository<Inspector>;
  let scheduleRepository: Repository<Schedule>;

  const mockOrderRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(),
    manager: {
      find: jest.fn(),
      findOne: jest.fn(),
    },
  };

  const mockPropertyRepository = {
    findOne: jest.fn(),
  };

  const mockInspectorRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    increment: jest.fn(),
  };

  const mockScheduleRepository = {
    findOne: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrdersService,
        {
          provide: getRepositoryToken(Order),
          useValue: mockOrderRepository,
        },
        {
          provide: getRepositoryToken(Property),
          useValue: mockPropertyRepository,
        },
        {
          provide: getRepositoryToken(Inspector),
          useValue: mockInspectorRepository,
        },
        {
          provide: getRepositoryToken(Schedule),
          useValue: mockScheduleRepository,
        },
      ],
    }).compile();

    service = module.get<OrdersService>(OrdersService);
    orderRepository = module.get<Repository<Order>>(getRepositoryToken(Order));
    propertyRepository = module.get<Repository<Property>>(getRepositoryToken(Property));
    inspectorRepository = module.get<Repository<Inspector>>(getRepositoryToken(Inspector));
    scheduleRepository = module.get<Repository<Schedule>>(getRepositoryToken(Schedule));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create an order successfully', async () => {
      const createOrderDto = {
        propertyType: 'residential',
        addressLine1: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        clientIds: [1, 2],
        assignedInspectorIds: [1],
      };

      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, orderNumber: 'ORD-2024-0001', ...createOrderDto };

      mockInspectorRepository.find.mockResolvedValue([{ id: 1, isActive: true }]);
      mockOrderRepository.manager.find.mockResolvedValue([
        { id: 1, role: 'client' },
        { id: 2, role: 'client' },
      ]);
      mockOrderRepository.create.mockReturnValue(mockOrder);
      mockOrderRepository.save.mockResolvedValue(mockOrder);
      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      const result = await service.create(createOrderDto, user);

      expect(result.order).toBeDefined();
      expect(result.message).toBe('Order created successfully');
      expect(mockOrderRepository.create).toHaveBeenCalled();
      expect(mockOrderRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException when inspector not found', async () => {
      const createOrderDto = {
        propertyType: 'residential',
        addressLine1: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        assignedInspectorIds: [999],
      };

      const user = { role: 'admin', userId: 1 };

      mockInspectorRepository.find.mockResolvedValue([]);

      await expect(service.create(createOrderDto, user)).rejects.toThrow(NotFoundException);
    });

    it('should handle client role creating order', async () => {
      const createOrderDto = {
        propertyType: 'residential',
        addressLine1: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
      };

      const user = { role: 'client', userId: 1 };
      const mockOrder = { id: 1, orderNumber: 'ORD-2024-0001', clientIds: [1] };

      mockOrderRepository.create.mockReturnValue(mockOrder);
      mockOrderRepository.save.mockResolvedValue(mockOrder);
      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      const result = await service.create(createOrderDto, user);

      expect(result.order.clientIds).toContain(1);
    });
  });

  describe('findOne', () => {
    it('should return order for admin user', async () => {
      const orderId = 1;
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, clientIds: [2], assignedInspectorIds: [3] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      const result = await service.findOne(orderId, user);

      expect(result).toEqual(mockOrder);
    });

    it('should return order for authorized client', async () => {
      const orderId = 1;
      const user = { role: 'client', userId: 2 };
      const mockOrder = { id: 1, clientIds: [2], assignedInspectorIds: [3] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      const result = await service.findOne(orderId, user);

      expect(result).toEqual(mockOrder);
    });

    it('should throw ForbiddenException for unauthorized client', async () => {
      const orderId = 1;
      const user = { role: 'client', userId: 999 };
      const mockOrder = { id: 1, clientIds: [2], assignedInspectorIds: [3] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      await expect(service.findOne(orderId, user)).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException when order not found', async () => {
      const orderId = 999;
      const user = { role: 'admin', userId: 1 };

      mockOrderRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(orderId, user)).rejects.toThrow(NotFoundException);
    });
  });

  describe('assignInspectors', () => {
    it('should assign inspectors to pending order', async () => {
      const orderId = 1;
      const inspectorIds = [1, 2];
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, status: OrderStatus.PENDING, clientIds: [1] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);
      mockInspectorRepository.find.mockResolvedValue([
        { id: 1, isActive: true, isAvailable: true },
        { id: 2, isActive: true, isAvailable: true },
      ]);
      mockOrderRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.assignInspectors(orderId, inspectorIds, user);

      expect(result.message).toBe('Inspectors assigned successfully');
      expect(mockOrderRepository.update).toHaveBeenCalledWith(orderId, {
        assignedInspectorIds: inspectorIds,
        status: OrderStatus.ASSIGNED,
      });
    });

    it('should throw BadRequestException for non-pending order', async () => {
      const orderId = 1;
      const inspectorIds = [1];
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, status: OrderStatus.COMPLETED, clientIds: [1] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      await expect(service.assignInspectors(orderId, inspectorIds, user)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('cancelOrder', () => {
    it('should cancel order with reason', async () => {
      const orderId = 1;
      const reason = 'Client requested cancellation';
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, status: OrderStatus.PENDING, clientIds: [1] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);
      mockOrderRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.cancelOrder(orderId, reason, user);

      expect(result.message).toBe('Order cancelled successfully');
      expect(mockOrderRepository.update).toHaveBeenCalledWith(orderId, {
        status: OrderStatus.CANCELLED,
        cancellationReason: reason,
        cancelledAt: expect.any(Date),
      });
    });

    it('should throw BadRequestException for completed order', async () => {
      const orderId = 1;
      const reason = 'Test reason';
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, status: OrderStatus.COMPLETED, clientIds: [1] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      await expect(service.cancelOrder(orderId, reason, user)).rejects.toThrow(BadRequestException);
    });
  });

  describe('addClientToOrder', () => {
    it('should add client to order', async () => {
      const orderId = 1;
      const clientId = 3;
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, clientIds: [1, 2] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);
      mockOrderRepository.manager.findOne.mockResolvedValue({ id: 3, role: 'client' });
      mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1, 2, 3] });

      const result = await service.addClientToOrder(orderId, clientId, user);

      expect(result.message).toBe('Client added to order successfully');
      expect(mockOrder.clientIds).toContain(clientId);
    });

    it('should throw BadRequestException when client already exists', async () => {
      const orderId = 1;
      const clientId = 1;
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, clientIds: [1, 2] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      await expect(service.addClientToOrder(orderId, clientId, user)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('removeClientFromOrder', () => {
    it('should remove client from order', async () => {
      const orderId = 1;
      const clientId = 2;
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, clientIds: [1, 2] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);
      mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1] });

      const result = await service.removeClientFromOrder(orderId, clientId, user);

      expect(result.message).toBe('Client removed from order successfully');
      expect(mockOrder.clientIds).not.toContain(clientId);
    });

    it('should throw BadRequestException when client not in order', async () => {
      const orderId = 1;
      const clientId = 999;
      const user = { role: 'admin', userId: 1 };
      const mockOrder = { id: 1, clientIds: [1, 2] };

      mockOrderRepository.findOne.mockResolvedValue(mockOrder);

      await expect(service.removeClientFromOrder(orderId, clientId, user)).rejects.toThrow(
        BadRequestException,
      );
    });
  });
});
