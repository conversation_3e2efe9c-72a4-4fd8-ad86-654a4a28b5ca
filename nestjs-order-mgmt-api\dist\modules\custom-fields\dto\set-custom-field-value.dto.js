"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetCustomFieldValueDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SetCustomFieldValueDto {
}
exports.SetCustomFieldValueDto = SetCustomFieldValueDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Custom field ID',
        example: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SetCustomFieldValueDto.prototype, "customFieldId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity type',
        example: 'order',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SetCustomFieldValueDto.prototype, "entityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID',
        example: 123,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SetCustomFieldValueDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Field value',
        example: 'Sample value',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Object)
], SetCustomFieldValueDto.prototype, "value", void 0);
//# sourceMappingURL=set-custom-field-value.dto.js.map