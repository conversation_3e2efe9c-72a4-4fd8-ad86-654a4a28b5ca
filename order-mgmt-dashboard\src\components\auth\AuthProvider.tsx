import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { fetchCurrentUser } from '@/redux/slices/userSlice';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider component that handles authentication state and user data loading
 * This component will fetch the current user data when the application starts
 * if there is a token in localStorage
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const dispatch = useAppDispatch();
  const { currentUser } = useAppSelector(state => state.user);

  useEffect(() => {
    // Check if there's a token in localStorage
    const token = localStorage.getItem('access_token');

    // If there's a token and no current user, fetch the user data
    if (token && !currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  return <>{children}</>;
}

export default AuthProvider;
