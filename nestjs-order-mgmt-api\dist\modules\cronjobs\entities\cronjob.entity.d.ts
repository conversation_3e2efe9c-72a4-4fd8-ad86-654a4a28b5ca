export declare enum JobStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare enum JobType {
    EMAIL_REMINDER = "email_reminder",
    CLEANUP_LOGS = "cleanup_logs",
    GENERATE_REPORTS = "generate_reports",
    SYNC_DATA = "sync_data",
    BACKUP_DATABASE = "backup_database",
    UPDATE_SCHEDULES = "update_schedules",
    SEND_NOTIFICATIONS = "send_notifications",
    CUSTOM = "custom"
}
export declare class Cronjob {
    id: number;
    name: string;
    description: string;
    schedule: string;
    jobType: JobType;
    status: JobStatus;
    configuration: any;
    isActive: boolean;
    timeout: number;
    maxRetries: number;
    lastRunAt: Date;
    lastCompletedAt: Date;
    runCount: number;
    failureCount: number;
    lastError: string;
    createdAt: Date;
    updatedAt: Date;
}
