export declare enum CronjobStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare enum CronjobType {
    EMAIL = "email",
    NOTIFICATION = "notification",
    CLEANUP = "cleanup",
    REPORT = "report",
    SYNC = "sync"
}
export declare class Cronjob {
    id: number;
    type: CronjobType;
    status: CronjobStatus;
    payload: any;
    scheduledAt: Date;
    startedAt: Date;
    completedAt: Date;
    attempts: number;
    maxAttempts: number;
    error: string;
    result: string;
    priority: number;
    createdAt: Date;
    updatedAt: Date;
}
