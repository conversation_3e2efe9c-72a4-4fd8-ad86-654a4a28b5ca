import { Repository } from 'typeorm';
import { Schedule } from './entities/schedule.entity';
import { Inspector } from '../inspectors/entities/inspector.entity';
import { Order } from '../orders/entities/order.entity';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { ScheduleQueryDto } from './dto/schedule-query.dto';
import { CheckConflictDto } from './dto/check-conflict.dto';
import { BulkCreateScheduleDto } from './dto/bulk-create-schedule.dto';
export declare class SchedulesService {
    private readonly scheduleRepository;
    private readonly inspectorRepository;
    private readonly orderRepository;
    constructor(scheduleRepository: Repository<Schedule>, inspectorRepository: Repository<Inspector>, orderRepository: Repository<Order>);
    create(createScheduleDto: CreateScheduleDto, user: any): Promise<{
        schedule: Schedule;
        message: string;
    }>;
    bulkCreate(bulkCreateDto: BulkCreateScheduleDto, user: any): Promise<{
        results: any[];
        message: string;
    }>;
    findAll(query: ScheduleQueryDto, user: any): Promise<{
        schedules: Schedule[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number, user: any): Promise<Schedule>;
    update(id: number, updateScheduleDto: UpdateScheduleDto, user: any): Promise<{
        schedule: Schedule;
        message: string;
    }>;
    remove(id: number, user: any): Promise<{
        message: string;
    }>;
    checkConflicts(checkConflictDto: CheckConflictDto): Promise<{
        hasConflicts: boolean;
        conflicts: Schedule[];
    }>;
    assignOrder(scheduleId: number, orderId: number, user: any): Promise<{
        message: string;
        schedule: Schedule;
    }>;
    unassignOrder(scheduleId: number, user: any): Promise<{
        message: string;
        schedule: Schedule;
    }>;
    getInspectorAvailability(inspectorId: number, startDate: string, endDate: string): Promise<{
        inspectorId: number;
        startDate: string;
        endDate: string;
        availability: {};
    }>;
    getCalendarView(inspectorId: number, month?: string, year?: string): Promise<{
        month: number;
        year: number;
        schedules: {
            id: number;
            date: string;
            startTime: string;
            endTime: string;
            available: boolean;
            order: {
                id: number;
                orderNumber: string;
                clientName: string;
            };
        }[];
    }>;
    getScheduleStats(): Promise<{
        totalSchedules: number;
        availableSchedules: number;
        assignedSchedules: number;
        schedulesByInspector: any[];
    }>;
    createRecurringSchedules(recurringData: any, user: any): Promise<{
        schedules: any[];
        message: string;
    }>;
    private checkTimeConflicts;
    private getDailyAssignmentCount;
    private processAvailabilityData;
    private formatCalendarData;
}
