{"version": 3, "sources": ["../../../src/telemetry/events/plugins.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\n\nconst EVENT_PLUGIN_PRESENT = 'NEXT_PACKAGE_DETECTED'\ntype NextPluginsEvent = {\n  eventName: string\n  payload: {\n    packageName: string\n    packageVersion: string\n  }\n}\n\nexport async function eventNextPlugins(\n  dir: string\n): Promise<Array<NextPluginsEvent>> {\n  try {\n    const packageJsonPath = await findUp('package.json', { cwd: dir })\n    if (!packageJsonPath) {\n      return []\n    }\n\n    const { dependencies = {}, devDependencies = {} } = require(packageJsonPath)\n\n    const deps = { ...devDependencies, ...dependencies }\n\n    return Object.keys(deps).reduce(\n      (events: NextPluginsEvent[], plugin: string): NextPluginsEvent[] => {\n        const version = deps[plugin]\n        // Don't add deps without a version set\n        if (!version) {\n          return events\n        }\n\n        events.push({\n          eventName: EVENT_PLUGIN_PRESENT,\n          payload: {\n            packageName: plugin,\n            packageVersion: version,\n          },\n        })\n\n        return events\n      },\n      []\n    )\n  } catch (_) {\n    return []\n  }\n}\n"], "names": ["eventNextPlugins", "EVENT_PLUGIN_PRESENT", "dir", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "Object", "keys", "reduce", "events", "plugin", "version", "push", "eventName", "payload", "packageName", "packageVersion", "_"], "mappings": ";;;;+BA<PERSON>sBA;;;eAAAA;;;+DAXH;;;;;;AAEnB,MAAMC,uBAAuB;AAStB,eAAeD,iBACpBE,GAAW;IAEX,IAAI;QACF,MAAMC,kBAAkB,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YAAEC,KAAKH;QAAI;QAChE,IAAI,CAACC,iBAAiB;YACpB,OAAO,EAAE;QACX;QAEA,MAAM,EAAEG,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGC,QAAQL;QAE5D,MAAMM,OAAO;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;QAAC;QAEnD,OAAOI,OAAOC,IAAI,CAACF,MAAMG,MAAM,CAC7B,CAACC,QAA4BC;YAC3B,MAAMC,UAAUN,IAAI,CAACK,OAAO;YAC5B,uCAAuC;YACvC,IAAI,CAACC,SAAS;gBACZ,OAAOF;YACT;YAEAA,OAAOG,IAAI,CAAC;gBACVC,WAAWhB;gBACXiB,SAAS;oBACPC,aAAaL;oBACbM,gBAAgBL;gBAClB;YACF;YAEA,OAAOF;QACT,GACA,EAAE;IAEN,EAAE,OAAOQ,GAAG;QACV,OAAO,EAAE;IACX;AACF"}