import { PropertyType } from '../entities/order.entity';
export declare class CreateOrderDto {
    clientName: string;
    clientEmail: string;
    clientPhone?: string;
    addressLine1: string;
    city: string;
    zipCode: string;
    state: string;
    propertyType?: PropertyType;
    yearBuilt?: number;
    foundationType?: string;
    gateCode?: string;
    lockboxCode?: string;
    alarmCode?: string;
    mlsNumber?: string;
    note?: string;
    isClientAttending?: boolean;
    isOccupied?: boolean;
    hasUtilities?: boolean;
    hasAlarm?: boolean;
    services?: {
        [key: string]: boolean;
    };
    agentName?: string;
    agentEmail?: string;
    agentPhone?: string;
    isSeller?: boolean;
    isBuyer?: boolean;
    inspectionFee: number;
    thirdPartyFee?: number;
    discountFee?: number;
    processingFee?: number;
    assignedInspectorIds?: number[];
    propertyId?: number;
    clientId?: number;
}
