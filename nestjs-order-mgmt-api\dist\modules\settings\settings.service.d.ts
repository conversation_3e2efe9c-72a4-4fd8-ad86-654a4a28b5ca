import { Repository } from 'typeorm';
import { Setting, SettingType, SettingCategory } from './entities/setting.entity';
import { CreateSettingDto } from './dto/create-setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { SettingQueryDto } from './dto/setting-query.dto';
export declare class SettingsService {
    private readonly settingRepository;
    private readonly encryptionKey;
    constructor(settingRepository: Repository<Setting>);
    create(createSettingDto: CreateSettingDto): Promise<{
        setting: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: SettingType;
            category: SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        };
        message: string;
    }>;
    findAll(query: SettingQueryDto): Promise<{
        settings: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: SettingType;
            category: SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    }>;
    findOne(id: number): Promise<{
        value: any;
        id: number;
        key: string;
        name: string;
        description: string;
        type: SettingType;
        category: SettingCategory;
        defaultValue: string;
        options: {
            min?: number;
            max?: number;
            choices?: {
                value: string;
                label: string;
            }[];
            placeholder?: string;
            helpText?: string;
            validation?: string;
            encrypted?: boolean;
        };
        isRequired: boolean;
        isSecret: boolean;
        isEditable: boolean;
        isVisible: boolean;
        sortOrder: number;
        group: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getByKey(key: string): Promise<{
        value: any;
        id: number;
        key: string;
        name: string;
        description: string;
        type: SettingType;
        category: SettingCategory;
        defaultValue: string;
        options: {
            min?: number;
            max?: number;
            choices?: {
                value: string;
                label: string;
            }[];
            placeholder?: string;
            helpText?: string;
            validation?: string;
            encrypted?: boolean;
        };
        isRequired: boolean;
        isSecret: boolean;
        isEditable: boolean;
        isVisible: boolean;
        sortOrder: number;
        group: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getValue(key: string, defaultValue?: any): Promise<any>;
    getByCategory(category: string): Promise<{
        category: string;
        settings: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: SettingType;
            category: SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        }[];
    }>;
    update(id: number, updateSettingDto: UpdateSettingDto): Promise<{
        setting: {
            value: any;
            id: number;
            key: string;
            name: string;
            description: string;
            type: SettingType;
            category: SettingCategory;
            defaultValue: string;
            options: {
                min?: number;
                max?: number;
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: string;
                encrypted?: boolean;
            };
            isRequired: boolean;
            isSecret: boolean;
            isEditable: boolean;
            isVisible: boolean;
            sortOrder: number;
            group: string;
            createdAt: Date;
            updatedAt: Date;
        };
        message: string;
    }>;
    updateValue(key: string, value: any): Promise<{
        message: string;
        value: any;
    }>;
    bulkUpdate(settings: {
        key: string;
        value: any;
    }[]): Promise<{
        results: any[];
        message: string;
    }>;
    resetToDefault(key: string): Promise<{
        message: string;
        value: any;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getPublicSettings(): Promise<{}>;
    getCategories(): Promise<{
        value: SettingCategory;
        label: string;
    }[]>;
    createBackup(): Promise<{
        backup: {
            timestamp: string;
            version: string;
            settings: {
                key: string;
                value: string;
                type: SettingType;
                category: SettingCategory;
            }[];
        };
        message: string;
    }>;
    restoreBackup(backupData: any): Promise<{
        results: any[];
        message: string;
    }>;
    validateSetting(key: string, value: any): {
        isValid: boolean;
        errors: string[];
    };
    private formatSetting;
    private parseValue;
    private parseValueByType;
    private encryptValue;
    private decryptValue;
}
