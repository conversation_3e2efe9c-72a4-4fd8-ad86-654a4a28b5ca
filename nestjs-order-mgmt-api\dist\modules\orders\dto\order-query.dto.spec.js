"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const order_query_dto_1 = require("./order-query.dto");
const order_entity_1 = require("../entities/order.entity");
describe('OrderQueryDto', () => {
    describe('Validation', () => {
        it('should pass validation with valid query parameters', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                page: 1,
                limit: 10,
                status: order_entity_1.OrderStatus.PENDING,
                clientId: 1,
                inspectorId: 1,
                propertyId: 1,
                startDate: '2024-01-01',
                endDate: '2024-12-31',
                search: 'test search',
                sortBy: 'createdAt',
                sortOrder: 'DESC',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should pass validation with minimal parameters', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {});
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
        });
        it('should fail validation with invalid page number', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                page: 0,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const pageError = errors.find(error => error.property === 'page');
            expect(pageError).toBeDefined();
            expect(pageError?.constraints).toHaveProperty('min');
        });
        it('should fail validation with invalid limit', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                limit: 0,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const limitError = errors.find(error => error.property === 'limit');
            expect(limitError).toBeDefined();
            expect(limitError?.constraints).toHaveProperty('min');
        });
        it('should fail validation with invalid status', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                status: 'invalid_status',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const statusError = errors.find(error => error.property === 'status');
            expect(statusError).toBeDefined();
            expect(statusError?.constraints).toHaveProperty('isEnum');
        });
        it('should fail validation with invalid sort order', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                sortOrder: 'INVALID',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors.length).toBeGreaterThan(0);
            const sortOrderError = errors.find(error => error.property === 'sortOrder');
            expect(sortOrderError).toBeDefined();
            expect(sortOrderError?.constraints).toHaveProperty('isIn');
        });
        it('should validate all order statuses', async () => {
            const statuses = [
                order_entity_1.OrderStatus.PENDING,
                order_entity_1.OrderStatus.ASSIGNED,
                order_entity_1.OrderStatus.SCHEDULED,
                order_entity_1.OrderStatus.IN_PROGRESS,
                order_entity_1.OrderStatus.COMPLETED,
                order_entity_1.OrderStatus.CANCELLED,
            ];
            for (const status of statuses) {
                const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, { status });
                const errors = await (0, class_validator_1.validate)(dto);
                expect(errors).toHaveLength(0);
            }
        });
        it('should validate both sort orders', async () => {
            const ascDto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, { sortOrder: 'ASC' });
            const descDto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, { sortOrder: 'DESC' });
            const ascErrors = await (0, class_validator_1.validate)(ascDto);
            const descErrors = await (0, class_validator_1.validate)(descDto);
            expect(ascErrors).toHaveLength(0);
            expect(descErrors).toHaveLength(0);
        });
    });
    describe('Data Transformation', () => {
        it('should transform string numbers to actual numbers', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                page: '2',
                limit: '20',
                clientId: '123',
                inspectorId: '456',
                propertyId: '789',
            });
            expect(typeof dto.page).toBe('number');
            expect(typeof dto.limit).toBe('number');
            expect(typeof dto.clientId).toBe('number');
            expect(typeof dto.inspectorId).toBe('number');
            expect(typeof dto.propertyId).toBe('number');
            expect(dto.page).toBe(2);
            expect(dto.limit).toBe(20);
            expect(dto.clientId).toBe(123);
            expect(dto.inspectorId).toBe(456);
            expect(dto.propertyId).toBe(789);
        });
        it('should set default values correctly', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {});
            expect(dto.page).toBe(1);
            expect(dto.limit).toBe(10);
            expect(dto.sortBy).toBe('createdAt');
            expect(dto.sortOrder).toBe('DESC');
        });
        it('should preserve string values for text fields', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                search: 'test search query',
                startDate: '2024-01-01',
                endDate: '2024-12-31',
                sortBy: 'orderNumber',
            });
            expect(typeof dto.search).toBe('string');
            expect(typeof dto.startDate).toBe('string');
            expect(typeof dto.endDate).toBe('string');
            expect(typeof dto.sortBy).toBe('string');
            expect(dto.search).toBe('test search query');
            expect(dto.startDate).toBe('2024-01-01');
            expect(dto.endDate).toBe('2024-12-31');
            expect(dto.sortBy).toBe('orderNumber');
        });
    });
    describe('Optional Fields', () => {
        it('should handle all optional fields as undefined', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                page: 1,
                limit: 10,
            });
            expect(dto.status).toBeUndefined();
            expect(dto.clientId).toBeUndefined();
            expect(dto.inspectorId).toBeUndefined();
            expect(dto.propertyId).toBeUndefined();
            expect(dto.startDate).toBeUndefined();
            expect(dto.endDate).toBeUndefined();
            expect(dto.search).toBeUndefined();
        });
        it('should handle partial field population', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                status: order_entity_1.OrderStatus.PENDING,
                clientId: 1,
                search: 'partial search',
            });
            expect(dto.status).toBe(order_entity_1.OrderStatus.PENDING);
            expect(dto.clientId).toBe(1);
            expect(dto.search).toBe('partial search');
            expect(dto.inspectorId).toBeUndefined();
            expect(dto.propertyId).toBeUndefined();
            expect(dto.startDate).toBeUndefined();
            expect(dto.endDate).toBeUndefined();
        });
    });
    describe('Edge Cases', () => {
        it('should handle very large page numbers', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                page: 999999,
                limit: 1000,
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.page).toBe(999999);
            expect(dto.limit).toBe(1000);
        });
        it('should handle empty string values', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                search: '',
                startDate: '',
                endDate: '',
                sortBy: '',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.search).toBe('');
            expect(dto.startDate).toBe('');
            expect(dto.endDate).toBe('');
            expect(dto.sortBy).toBe('');
        });
        it('should handle special characters in search', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                search: 'test@#$%^&*()_+-=[]{}|;:,.<>?',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.search).toBe('test@#$%^&*()_+-=[]{}|;:,.<>?');
        });
        it('should handle unicode characters in search', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                search: 'тест 测试 テスト',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.search).toBe('тест 测试 テスト');
        });
    });
    describe('Business Logic Validation', () => {
        it('should validate date format expectations', async () => {
            const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, {
                startDate: '2024-01-01',
                endDate: '2024-12-31',
            });
            const errors = await (0, class_validator_1.validate)(dto);
            expect(errors).toHaveLength(0);
            expect(dto.startDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
            expect(dto.endDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        });
        it('should handle common sort fields', async () => {
            const commonSortFields = [
                'createdAt',
                'updatedAt',
                'orderNumber',
                'status',
                'inspectionDate',
                'totalAmount',
            ];
            for (const sortBy of commonSortFields) {
                const dto = (0, class_transformer_1.plainToClass)(order_query_dto_1.OrderQueryDto, { sortBy });
                const errors = await (0, class_validator_1.validate)(dto);
                expect(errors).toHaveLength(0);
                expect(dto.sortBy).toBe(sortBy);
            }
        });
    });
});
//# sourceMappingURL=order-query.dto.spec.js.map