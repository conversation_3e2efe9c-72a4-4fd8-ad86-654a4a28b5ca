import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsNotEmpty } from 'class-validator';

export class RenderTemplateDto {
  @ApiProperty({
    description: 'Variables to render in template',
    example: {
      clientName: '<PERSON>',
      orderNumber: 'ORD-2024-001',
      inspectionDate: '2024-01-15',
      propertyAddress: '123 Main St, New York, NY 10001',
    },
  })
  @IsObject()
  @IsNotEmpty()
  variables: Record<string, any>;
}
