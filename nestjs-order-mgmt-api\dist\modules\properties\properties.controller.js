"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const properties_service_1 = require("./properties.service");
const create_property_dto_1 = require("./dto/create-property.dto");
const update_property_dto_1 = require("./dto/update-property.dto");
const property_query_dto_1 = require("./dto/property-query.dto");
const create_property_tag_dto_1 = require("./dto/create-property-tag.dto");
const auth_decorator_1 = require("../../common/decorators/auth.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let PropertiesController = class PropertiesController {
    constructor(propertiesService) {
        this.propertiesService = propertiesService;
    }
    async create(createPropertyDto, user) {
        return this.propertiesService.create(createPropertyDto);
    }
    async findAll(query) {
        return this.propertiesService.findAll(query);
    }
    async search(searchTerm) {
        return this.propertiesService.searchByAddress(searchTerm);
    }
    async findNearby(latitude, longitude, radius = 5) {
        return this.propertiesService.findNearby(latitude, longitude, radius);
    }
    async findOne(id) {
        return this.propertiesService.findOne(id);
    }
    async update(id, updatePropertyDto) {
        return this.propertiesService.update(id, updatePropertyDto);
    }
    async remove(id) {
        return this.propertiesService.remove(id);
    }
    async getPropertyOrders(id) {
        return this.propertiesService.getPropertyOrders(id);
    }
    async createTag(createTagDto) {
        return this.propertiesService.createTag(createTagDto);
    }
    async getTags() {
        return this.propertiesService.getTags();
    }
    async updatePropertyTags(id, tagIds) {
        return this.propertiesService.updatePropertyTags(id, tagIds.tagIds);
    }
};
exports.PropertiesController = PropertiesController;
__decorate([
    (0, common_1.Post)(),
    (0, auth_decorator_1.Auth)('admin', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new property' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Property successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_property_dto_1.CreatePropertyDto, Object]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all properties with filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Properties retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'propertyType', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [property_query_dto_1.PropertyQueryDto]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Search properties by address' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Properties found' }),
    (0, swagger_1.ApiQuery)({ name: 'q', required: true, type: String }),
    __param(0, (0, common_1.Query)('q')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('nearby'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Find properties nearby a location' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Nearby properties found' }),
    (0, swagger_1.ApiQuery)({ name: 'lat', required: true, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'lng', required: true, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'radius', required: false, type: Number }),
    __param(0, (0, common_1.Query)('lat')),
    __param(1, (0, common_1.Query)('lng')),
    __param(2, (0, common_1.Query)('radius')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "findNearby", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get property by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Property retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Property not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, auth_decorator_1.Auth)('admin', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Update property' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Property updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Property not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_property_dto_1.UpdatePropertyDto]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete property' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Property deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Property not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id/orders'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get property inspection orders' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Property orders retrieved' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "getPropertyOrders", null);
__decorate([
    (0, common_1.Post)('tags'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Create property tag' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Tag created successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_property_tag_dto_1.CreatePropertyTagDto]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "createTag", null);
__decorate([
    (0, common_1.Get)('tags'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all property tags' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Tags retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "getTags", null);
__decorate([
    (0, common_1.Patch)(':id/tags'),
    (0, auth_decorator_1.Auth)('admin', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Update property tags' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Property tags updated' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], PropertiesController.prototype, "updatePropertyTags", null);
exports.PropertiesController = PropertiesController = __decorate([
    (0, swagger_1.ApiTags)('Properties'),
    (0, common_1.Controller)('properties'),
    __metadata("design:paramtypes", [properties_service_1.PropertiesService])
], PropertiesController);
//# sourceMappingURL=properties.controller.js.map