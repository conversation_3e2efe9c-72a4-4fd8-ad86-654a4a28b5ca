import { Repository } from 'typeorm';
import { Inspector } from './entities/inspector.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { CreateInspectorDto } from './dto/create-inspector.dto';
import { UpdateInspectorDto } from './dto/update-inspector.dto';
import { InspectorQueryDto } from './dto/inspector-query.dto';
export declare class InspectorsService {
    private readonly inspectorRepository;
    private readonly scheduleRepository;
    constructor(inspectorRepository: Repository<Inspector>, scheduleRepository: Repository<Schedule>);
    create(createInspectorDto: CreateInspectorDto): Promise<{
        inspector: Inspector;
        message: string;
    }>;
    findAll(query: InspectorQueryDto): Promise<{
        inspectors: Inspector[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<Inspector>;
    update(id: number, updateInspectorDto: UpdateInspectorDto): Promise<{
        inspector: Inspector;
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findAvailable(date: string, startTime: string, endTime: string): Promise<{
        date: string;
        timeSlot: {
            startTime: string;
            endTime: string;
        };
        availableInspectors: any[];
    }>;
    getSchedule(id: number, startDate?: string, endDate?: string): Promise<{
        inspector: {
            id: number;
            name: string;
            email: string;
        };
        schedules: Schedule[];
        period: {
            startDate: string;
            endDate: string;
        };
    }>;
    getStats(id: number): Promise<{
        inspector: {
            id: number;
            name: string;
            rating: number;
            completedInspections: number;
        };
        statistics: {
            totalSchedules: number;
            completedInspections: number;
            upcomingSchedules: number;
            averageRating: number;
            monthlyStats: any[];
        };
    }>;
    updateAvailability(id: number, isAvailable: boolean): Promise<{
        message: string;
    }>;
    updateRating(id: number, rating: number): Promise<{
        message: string;
        newRating: number;
    }>;
    getSpecializations(): Promise<string[]>;
    private getMonthlyStats;
}
