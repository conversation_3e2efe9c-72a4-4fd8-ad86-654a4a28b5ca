"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InspectorsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const inspector_entity_1 = require("./entities/inspector.entity");
const schedule_entity_1 = require("../schedules/entities/schedule.entity");
let InspectorsService = class InspectorsService {
    constructor(inspectorRepository, scheduleRepository) {
        this.inspectorRepository = inspectorRepository;
        this.scheduleRepository = scheduleRepository;
    }
    async create(createInspectorDto) {
        const existingInspector = await this.inspectorRepository.findOne({
            where: { email: createInspectorDto.email },
        });
        if (existingInspector) {
            throw new common_1.ConflictException('Inspector with this email already exists');
        }
        const inspector = this.inspectorRepository.create(createInspectorDto);
        const savedInspector = await this.inspectorRepository.save(inspector);
        return {
            inspector: savedInspector,
            message: 'Inspector created successfully',
        };
    }
    async findAll(query) {
        const { page = 1, limit = 10, isActive, isAvailable, search, specialization, sortBy = 'name', sortOrder = 'ASC', } = query;
        const queryBuilder = this.inspectorRepository.createQueryBuilder('inspector');
        if (isActive !== undefined) {
            queryBuilder.andWhere('inspector.isActive = :isActive', { isActive });
        }
        if (isAvailable !== undefined) {
            queryBuilder.andWhere('inspector.isAvailable = :isAvailable', { isAvailable });
        }
        if (search) {
            queryBuilder.andWhere('(inspector.name ILIKE :search OR inspector.email ILIKE :search OR inspector.licenseNumber ILIKE :search)', { search: `%${search}%` });
        }
        if (specialization) {
            queryBuilder.andWhere(':specialization = ANY(inspector.specializations)', {
                specialization,
            });
        }
        queryBuilder.orderBy(`inspector.${sortBy}`, sortOrder);
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [inspectors, total] = await queryBuilder.getManyAndCount();
        return {
            inspectors,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const inspector = await this.inspectorRepository.findOne({
            where: { id },
            relations: ['schedules'],
        });
        if (!inspector) {
            throw new common_1.NotFoundException('Inspector not found');
        }
        return inspector;
    }
    async update(id, updateInspectorDto) {
        const inspector = await this.inspectorRepository.findOne({ where: { id } });
        if (!inspector) {
            throw new common_1.NotFoundException('Inspector not found');
        }
        if (updateInspectorDto.email && updateInspectorDto.email !== inspector.email) {
            const existingInspector = await this.inspectorRepository.findOne({
                where: { email: updateInspectorDto.email },
            });
            if (existingInspector) {
                throw new common_1.ConflictException('Email already in use');
            }
        }
        await this.inspectorRepository.update(id, updateInspectorDto);
        const updatedInspector = await this.findOne(id);
        return {
            inspector: updatedInspector,
            message: 'Inspector updated successfully',
        };
    }
    async remove(id) {
        const inspector = await this.inspectorRepository.findOne({ where: { id } });
        if (!inspector) {
            throw new common_1.NotFoundException('Inspector not found');
        }
        const activeSchedules = await this.scheduleRepository.count({
            where: { inspectorId: id, available: false },
        });
        if (activeSchedules > 0) {
            throw new common_1.ConflictException('Cannot delete inspector with active schedules');
        }
        await this.inspectorRepository.remove(inspector);
        return {
            message: 'Inspector deleted successfully',
        };
    }
    async findAvailable(date, startTime, endTime) {
        const availableInspectors = await this.inspectorRepository.find({
            where: { isActive: true, isAvailable: true },
        });
        const inspectorsWithoutConflicts = [];
        for (const inspector of availableInspectors) {
            const conflicts = await this.scheduleRepository.count({
                where: {
                    inspectorId: inspector.id,
                    date,
                    startTime: (0, typeorm_2.Between)(startTime, endTime),
                },
            });
            if (conflicts === 0) {
                const dailyAssignments = await this.scheduleRepository.count({
                    where: {
                        inspectorId: inspector.id,
                        date,
                        available: false,
                    },
                });
                if (dailyAssignments < 3) {
                    inspectorsWithoutConflicts.push({
                        ...inspector,
                        dailyAssignments,
                        availableSlots: 3 - dailyAssignments,
                    });
                }
            }
        }
        return {
            date,
            timeSlot: { startTime, endTime },
            availableInspectors: inspectorsWithoutConflicts,
        };
    }
    async getSchedule(id, startDate, endDate) {
        const inspector = await this.findOne(id);
        const queryBuilder = this.scheduleRepository.createQueryBuilder('schedule');
        queryBuilder.where('schedule.inspectorId = :id', { id });
        if (startDate && endDate) {
            queryBuilder.andWhere('schedule.date BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        queryBuilder.leftJoinAndSelect('schedule.order', 'order');
        queryBuilder.orderBy('schedule.date', 'ASC');
        queryBuilder.addOrderBy('schedule.startTime', 'ASC');
        const schedules = await queryBuilder.getMany();
        return {
            inspector: {
                id: inspector.id,
                name: inspector.name,
                email: inspector.email,
            },
            schedules,
            period: { startDate, endDate },
        };
    }
    async getStats(id) {
        const inspector = await this.findOne(id);
        const totalSchedules = await this.scheduleRepository.count({
            where: { inspectorId: id },
        });
        const completedInspections = await this.scheduleRepository.count({
            where: { inspectorId: id, available: false },
        });
        const upcomingSchedules = await this.scheduleRepository.count({
            where: {
                inspectorId: id,
                date: (0, typeorm_2.Between)(new Date().toISOString().split('T')[0], '2099-12-31'),
            },
        });
        const avgRating = inspector.rating || 0;
        const monthlyStats = await this.getMonthlyStats(id);
        return {
            inspector: {
                id: inspector.id,
                name: inspector.name,
                rating: avgRating,
                completedInspections: inspector.completedInspections,
            },
            statistics: {
                totalSchedules,
                completedInspections,
                upcomingSchedules,
                averageRating: avgRating,
                monthlyStats,
            },
        };
    }
    async updateAvailability(id, isAvailable) {
        const inspector = await this.inspectorRepository.findOne({ where: { id } });
        if (!inspector) {
            throw new common_1.NotFoundException('Inspector not found');
        }
        await this.inspectorRepository.update(id, { isAvailable });
        return {
            message: `Inspector availability updated to ${isAvailable ? 'available' : 'unavailable'}`,
        };
    }
    async updateRating(id, rating) {
        const inspector = await this.inspectorRepository.findOne({ where: { id } });
        if (!inspector) {
            throw new common_1.NotFoundException('Inspector not found');
        }
        const totalRatings = inspector.completedInspections || 1;
        const currentTotal = (inspector.rating || 0) * totalRatings;
        const newAverage = (currentTotal + rating) / (totalRatings + 1);
        await this.inspectorRepository.update(id, {
            rating: Math.round(newAverage * 100) / 100,
        });
        return {
            message: 'Inspector rating updated successfully',
            newRating: newAverage,
        };
    }
    async getSpecializations() {
        const inspectors = await this.inspectorRepository.find({
            select: ['specializations'],
            where: { isActive: true },
        });
        const allSpecializations = inspectors
            .flatMap(inspector => inspector.specializations || [])
            .filter((spec, index, array) => array.indexOf(spec) === index)
            .sort();
        return allSpecializations;
    }
    async getMonthlyStats(inspectorId) {
        const monthlyData = [];
        const currentDate = new Date();
        for (let i = 11; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const startDate = date.toISOString().split('T')[0];
            const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
                .toISOString()
                .split('T')[0];
            const monthlySchedules = await this.scheduleRepository.count({
                where: {
                    inspectorId,
                    date: (0, typeorm_2.Between)(startDate, endDate),
                },
            });
            const monthlyCompletions = await this.scheduleRepository.count({
                where: {
                    inspectorId,
                    date: (0, typeorm_2.Between)(startDate, endDate),
                    available: false,
                },
            });
            monthlyData.push({
                month: date.toISOString().substring(0, 7),
                schedules: monthlySchedules,
                completions: monthlyCompletions,
            });
        }
        return monthlyData;
    }
};
exports.InspectorsService = InspectorsService;
exports.InspectorsService = InspectorsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(inspector_entity_1.Inspector)),
    __param(1, (0, typeorm_1.InjectRepository)(schedule_entity_1.Schedule)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], InspectorsService);
//# sourceMappingURL=inspectors.service.js.map