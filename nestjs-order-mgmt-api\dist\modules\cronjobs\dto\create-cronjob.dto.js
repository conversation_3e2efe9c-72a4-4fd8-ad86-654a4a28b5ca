"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCronjobDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const cronjob_entity_1 = require("../entities/cronjob.entity");
class CreateCronjobDto {
    constructor() {
        this.isActive = true;
    }
}
exports.CreateCronjobDto = CreateCronjobDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job name',
        example: 'Daily Email Reminders',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCronjobDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job description',
        example: 'Send email reminders for upcoming inspections',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCronjobDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cron schedule expression',
        example: '0 9 * * *',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Matches)(/^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/, {
        message: 'Invalid cron expression',
    }),
    __metadata("design:type", String)
], CreateCronjobDto.prototype, "schedule", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job type',
        enum: cronjob_entity_1.JobType,
        example: cronjob_entity_1.JobType.EMAIL_REMINDER,
    }),
    (0, class_validator_1.IsEnum)(cronjob_entity_1.JobType),
    __metadata("design:type", typeof (_a = typeof cronjob_entity_1.JobType !== "undefined" && cronjob_entity_1.JobType) === "function" ? _a : Object)
], CreateCronjobDto.prototype, "jobType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job configuration parameters',
        example: {
            reminderHours: 24,
            emailTemplate: 'inspection-reminder',
        },
        required: false,
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateCronjobDto.prototype, "configuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is job active',
        default: true,
        required: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCronjobDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job timeout in seconds',
        example: 300,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCronjobDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Maximum retry attempts',
        example: 3,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCronjobDto.prototype, "maxRetries", void 0);
//# sourceMappingURL=create-cronjob.dto.js.map