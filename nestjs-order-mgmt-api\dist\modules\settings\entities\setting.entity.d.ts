export declare enum SettingType {
    STRING = "string",
    NUMBER = "number",
    BOOLEAN = "boolean",
    JSON = "json",
    EMAIL = "email",
    URL = "url",
    PASSWORD = "password",
    FILE = "file"
}
export declare enum SettingCategory {
    GENERAL = "general",
    EMAIL = "email",
    NOTIFICATION = "notification",
    PAYMENT = "payment",
    SECURITY = "security",
    INTEGRATION = "integration",
    APPEARANCE = "appearance",
    BUSINESS = "business"
}
export declare class Setting {
    id: number;
    key: string;
    name: string;
    description: string;
    type: SettingType;
    category: SettingCategory;
    value: string;
    defaultValue: string;
    options: {
        min?: number;
        max?: number;
        choices?: {
            value: string;
            label: string;
        }[];
        placeholder?: string;
        helpText?: string;
        validation?: string;
        encrypted?: boolean;
    };
    isRequired: boolean;
    isSecret: boolean;
    isEditable: boolean;
    isVisible: boolean;
    sortOrder: number;
    group: string;
    createdAt: Date;
    updatedAt: Date;
}
