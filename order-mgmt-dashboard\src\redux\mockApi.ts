import mockOrders, { getMockOrdersResponse } from '@/data/mockOrders';
import { Order } from '@/types/order';
import { User } from '@/types/user';

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Mock users for authentication
const mockUsers = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
  },
  {
    id: 2,
    name: 'Inspector User',
    email: '<EMAIL>',
    password: 'inspector123',
    role: 'inspector',
  },
  {
    id: 3,
    name: 'Client User',
    email: '<EMAIL>',
    password: 'client123',
    role: 'client',
  }
];

// Mock API functions for Redux thunks
export const mockApi = {
  // Orders
  getOrders: async (page: number = 1, pageSize: number = 10) => {
    await delay();
    return getMockOrdersResponse(page, pageSize);
  },

  getOrderById: async (id: number) => {
    await delay();

    const order = mockOrders.find(o => o.id === id);

    if (!order) {
      throw new Error('Order not found');
    }

    return {
      success: true,
      data: {
        order
      }
    };
  },

  addOrder: async (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => {
    await delay();

    const newOrder: Order = {
      ...order,
      id: Math.max(...mockOrders.map(o => o.id), 0) + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockOrders.push(newOrder);

    return {
      success: true,
      data: {
        order: newOrder
      }
    };
  },

  updateOrder: async (id: number, orderData: Partial<Order>) => {
    await delay();

    const orderIndex = mockOrders.findIndex(o => o.id === id);

    if (orderIndex === -1) {
      throw new Error('Order not found');
    }

    const updatedOrder = {
      ...mockOrders[orderIndex],
      ...orderData,
      updatedAt: new Date().toISOString()
    };

    mockOrders[orderIndex] = updatedOrder;

    return {
      success: true,
      data: {
        order: updatedOrder
      }
    };
  },

  deleteOrder: async (id: number) => {
    await delay();

    const orderIndex = mockOrders.findIndex(o => o.id === id);

    if (orderIndex === -1) {
      throw new Error('Order not found');
    }

    mockOrders.splice(orderIndex, 1);

    return {
      success: true
    };
  },

  // Users
  getCurrentUser: async () => {
    await delay();

    // Try to get user from localStorage first
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        return {
          success: true,
          data: {
            user
          }
        };
      } catch (error) {
        console.error('Failed to parse stored user:', error);
      }
    }

    // Fallback to mock user
    const user = mockUsers[0];

    return {
      success: true,
      data: {
        user: {
          id: user.id.toString(),
          name: user.name,
          email: user.email,
          role: user.role,
          avatar: null
        }
      }
    };
  },

  login: async (email: string, password: string) => {
    await delay();

    const user = mockUsers.find(u => u.email === email && u.password === password);

    if (!user) {
      throw new Error('Invalid credentials');
    }

    return {
      success: true,
      data: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        user: {
          id: user.id.toString(),
          name: user.name,
          email: user.email,
          role: user.role,
        }
      }
    };
  },

  register: async (email: string, password: string, name: string) => {
    await delay();

    if (mockUsers.some(u => u.email === email)) {
      throw new Error('Email already in use');
    }

    const newUser = {
      id: mockUsers.length + 1,
      name,
      email,
      password,
      role: 'client', // Default role for new users
    };

    mockUsers.push(newUser);

    return {
      success: true,
      data: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        user: {
          id: newUser.id.toString(),
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
        }
      }
    };
  },

  updateUserProfile: async (userData: Partial<User>) => {
    await delay();

    // Simulate updating the current user (first user in the array)
    const userIndex = 0;
    const updatedUser = {
      ...mockUsers[userIndex],
      ...userData,
    };

    mockUsers[userIndex] = updatedUser;

    return {
      success: true,
      data: {
        user: {
          id: updatedUser.id.toString(),
          name: updatedUser.name,
          email: updatedUser.email,
          role: updatedUser.role,
        }
      }
    };
  },

  // Refresh token
  refreshToken: async (refreshToken: string) => {
    await delay();

    // Simulate token refresh
    if (!refreshToken || refreshToken !== 'mock-refresh-token') {
      throw new Error('Invalid refresh token');
    }

    return {
      success: true,
      data: {
        access_token: 'new-mock-access-token',
        refresh_token: 'new-mock-refresh-token',
      }
    };
  }
};
