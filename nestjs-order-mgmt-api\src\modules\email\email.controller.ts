import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { EmailService } from './email.service';
import { SendEmailDto } from './dto/send-email.dto';
import { Auth } from '../../common/decorators/auth.decorator';

@ApiTags('Email')
@Controller('email')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Post('send')
  @Auth('admin')
  @ApiOperation({ summary: 'Send email' })
  @ApiResponse({ status: 200, description: 'Email queued successfully' })
  async sendEmail(@Body() sendEmailDto: SendEmailDto) {
    return this.emailService.sendEmail(sendEmailDto);
  }

  @Post('test')
  @Auth('admin')
  @ApiOperation({ summary: 'Send test email' })
  @ApiResponse({ status: 200, description: 'Test email sent' })
  async sendTestEmail(@Body() testData: { to: string; subject?: string; content?: string }) {
    return this.emailService.sendEmail({
      to: testData.to,
      subject: testData.subject || 'Test Email',
      text: testData.content || 'This is a test email',
    });
  }

  @Get('templates')
  @Auth('admin')
  @ApiOperation({ summary: 'Get all email templates' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates() {
    return {
      templates: ['order-confirmation', 'inspection-reminder', 'inspection-completed']
    };
  }

  @Get('queue/status')
  @Auth('admin')
  @ApiOperation({ summary: 'Get email queue status' })
  @ApiResponse({ status: 200, description: 'Queue status retrieved' })
  async getQueueStatus() {
    return this.emailService.getQueueStatus();
  }

  @Post('template/:templateName')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Send template email' })
  @ApiResponse({ status: 200, description: 'Template email sent' })
  async sendTemplateEmail(
    @Param('templateName') templateName: string,
    @Body() templateData: { to: string; variables: Record<string, any> },
  ) {
    return this.emailService.sendTemplateEmail(
      templateName,
      templateData.to,
      templateData.variables,
    );
  }
}
