import { Order } from '../../orders/entities/order.entity';
import { PropertyTag } from './property-tag.entity';
export declare enum PropertyType {
    SINGLE_FAMILY = "single_family",
    CONDO = "condo",
    TOWNHOUSE = "townhouse",
    MULTI_FAMILY = "multi_family",
    COMMERCIAL = "commercial",
    LAND = "land"
}
export declare enum PropertyStatus {
    ACTIVE = "active",
    SOLD = "sold",
    PENDING = "pending",
    WITHDRAWN = "withdrawn"
}
export declare class Property {
    id: number;
    addressLine1: string;
    addressLine2: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    propertyType: PropertyType;
    status: PropertyStatus;
    yearBuilt: number;
    squareFootage: number;
    lotSize: number;
    bedrooms: number;
    bathrooms: number;
    floors: number;
    foundationType: string;
    hasGarage: boolean;
    hasPool: boolean;
    hasFireplace: boolean;
    hasBasement: boolean;
    hasAttic: boolean;
    hasDeck: boolean;
    hasPatio: boolean;
    hasElectricity: boolean;
    hasWater: boolean;
    hasGas: boolean;
    hasSewer: boolean;
    hasInternet: boolean;
    heatingType: string;
    coolingType: string;
    gateCode: string;
    lockboxCode: string;
    alarmCode: string;
    mlsNumber: string;
    description: string;
    notes: string;
    images: string[];
    customFields: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    orders: Order[];
    tags: PropertyTag[];
}
