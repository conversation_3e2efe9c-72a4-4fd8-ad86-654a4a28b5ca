# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/da67dae8-8fdf-4676-8c71-45cd102b2d9a

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/da67dae8-8fdf-4676-8c71-45cd102b2d9a) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- Redux & Redux Toolkit
- shadcn-ui
- Tailwind CSS

## Redux Implementation

This project uses Redux and Redux Toolkit for state management. The Redux implementation includes:

- Centralized store configuration in `src/redux/store.ts`
- Type-safe hooks in `src/redux/hooks.ts`
- Slices for different data domains:
  - `ordersSlice.ts`: Manages orders with pagination support
  - `userSlice.ts`: Manages user authentication and profile

The Redux store is configured with the following features:
- Pagination support for orders
- Async thunks for API calls
- Type-safe state and actions

You can see a demo of the Redux implementation at `/redux-example`.

For more details, see the [Redux README](src/redux/README.md).

## API Client with Axios

This project uses Axios instead of fetch for HTTP requests, with the following features:

- **Axios-based HTTP client** with automatic JWT token management
- **Request/Response interceptors** for seamless authentication
- **Automatic token refresh** when receiving 401 responses
- **Queue management** for concurrent requests during token refresh
- **TypeScript support** with proper type definitions

### Key Features:

1. **Automatic Token Management**: Tokens are automatically added to requests
2. **Token Refresh**: Automatically refreshes expired tokens using refresh tokens
3. **Error Handling**: Comprehensive error handling with user-friendly messages
4. **Queue System**: Handles multiple concurrent requests during token refresh
5. **Security**: Automatic logout and redirect when refresh fails

### Usage:

```typescript
import { apiClient } from '@/lib/api-client';

// Simple GET request
const data = await apiClient<ResponseType>('endpoint');

// POST request with data
const result = await apiClient<ResponseType>('endpoint', {
  method: 'POST',
  data: { key: 'value' }
});
```

For more details, see the [API Client README](src/lib/README.md).

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/da67dae8-8fdf-4676-8c71-45cd102b2d9a) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes it is!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
