
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  font-weight: 600;
}

/* Button hover effects */
button, .btn {
  transition: all 0.2s ease-in-out;
}

button:hover, .btn:hover {
  transform: translateY(-2px);
}

/* Card styling */
.card {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-4px);
}

/* Navy and teal color theme */
.bg-navy {
  background-color: #1a365d;
}

.text-navy {
  color: #1a365d;
}

.bg-teal {
  background-color: #2c7a7b;
}

.text-teal {
  color: #2c7a7b;
}

.bg-gold {
  background-color: #ecc94b;
}

.text-gold {
  color: #ecc94b;
}

.bg-neutral {
  background-color: #f7fafc;
}

.border-neutral {
  border-color: #e2e8f0;
}
