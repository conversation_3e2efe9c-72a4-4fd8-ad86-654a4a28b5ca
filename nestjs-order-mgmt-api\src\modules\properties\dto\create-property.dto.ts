import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsEnum,
  IsArray,
  IsObject,
  IsNotEmpty,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PropertyType, PropertyStatus } from '../entities/property.entity';

export class CreatePropertyDto {
  // Address Information
  @ApiProperty({ description: 'Property address line 1', example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @ApiProperty({ description: 'Property address line 2', required: false })
  @IsString()
  @IsOptional()
  addressLine2?: string;

  @ApiProperty({ description: 'City', example: 'New York' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ description: 'State', example: 'NY' })
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty({ description: 'Zip code', example: '10001' })
  @IsString()
  @IsNotEmpty()
  zipCode: string;

  @ApiProperty({ description: 'Country', example: 'USA', required: false })
  @IsString()
  @IsOptional()
  country?: string;

  // Property Details
  @ApiProperty({ 
    description: 'Property type', 
    enum: PropertyType,
    example: PropertyType.SINGLE_FAMILY 
  })
  @IsEnum(PropertyType)
  propertyType: PropertyType;

  @ApiProperty({ 
    description: 'Property status', 
    enum: PropertyStatus,
    required: false,
    default: PropertyStatus.ACTIVE 
  })
  @IsEnum(PropertyStatus)
  @IsOptional()
  status?: PropertyStatus = PropertyStatus.ACTIVE;

  @ApiProperty({ description: 'Year built', example: 2020, required: false })
  @IsNumber()
  @IsOptional()
  @Min(1800)
  @Max(new Date().getFullYear() + 1)
  @Type(() => Number)
  yearBuilt?: number;

  @ApiProperty({ description: 'Square footage', example: 2500.5, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  squareFootage?: number;

  @ApiProperty({ description: 'Lot size in acres', example: 0.25, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  lotSize?: number;

  @ApiProperty({ description: 'Number of bedrooms', example: 3, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  bedrooms?: number;

  @ApiProperty({ description: 'Number of bathrooms', example: 2.5, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  bathrooms?: number;

  @ApiProperty({ description: 'Number of floors', example: 2, required: false })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Type(() => Number)
  floors?: number;

  @ApiProperty({ description: 'Foundation type', example: 'Concrete', required: false })
  @IsString()
  @IsOptional()
  foundationType?: string;

  // Property Features
  @ApiProperty({ description: 'Has garage', default: false })
  @IsBoolean()
  @IsOptional()
  hasGarage?: boolean = false;

  @ApiProperty({ description: 'Has pool', default: false })
  @IsBoolean()
  @IsOptional()
  hasPool?: boolean = false;

  @ApiProperty({ description: 'Has fireplace', default: false })
  @IsBoolean()
  @IsOptional()
  hasFireplace?: boolean = false;

  @ApiProperty({ description: 'Has basement', default: false })
  @IsBoolean()
  @IsOptional()
  hasBasement?: boolean = false;

  @ApiProperty({ description: 'Has attic', default: false })
  @IsBoolean()
  @IsOptional()
  hasAttic?: boolean = false;

  @ApiProperty({ description: 'Has deck', default: false })
  @IsBoolean()
  @IsOptional()
  hasDeck?: boolean = false;

  @ApiProperty({ description: 'Has patio', default: false })
  @IsBoolean()
  @IsOptional()
  hasPatio?: boolean = false;

  // Utilities & Systems
  @ApiProperty({ description: 'Has electricity', default: false })
  @IsBoolean()
  @IsOptional()
  hasElectricity?: boolean = false;

  @ApiProperty({ description: 'Has water', default: false })
  @IsBoolean()
  @IsOptional()
  hasWater?: boolean = false;

  @ApiProperty({ description: 'Has gas', default: false })
  @IsBoolean()
  @IsOptional()
  hasGas?: boolean = false;

  @ApiProperty({ description: 'Has sewer', default: false })
  @IsBoolean()
  @IsOptional()
  hasSewer?: boolean = false;

  @ApiProperty({ description: 'Has internet', default: false })
  @IsBoolean()
  @IsOptional()
  hasInternet?: boolean = false;

  @ApiProperty({ description: 'Heating type', example: 'Central Air', required: false })
  @IsString()
  @IsOptional()
  heatingType?: string;

  @ApiProperty({ description: 'Cooling type', example: 'Central AC', required: false })
  @IsString()
  @IsOptional()
  coolingType?: string;

  // Access Information
  @ApiProperty({ description: 'Gate code', required: false })
  @IsString()
  @IsOptional()
  gateCode?: string;

  @ApiProperty({ description: 'Lockbox code', required: false })
  @IsString()
  @IsOptional()
  lockboxCode?: string;

  @ApiProperty({ description: 'Alarm code', required: false })
  @IsString()
  @IsOptional()
  alarmCode?: string;

  @ApiProperty({ description: 'MLS number', required: false })
  @IsString()
  @IsOptional()
  mlsNumber?: string;

  // Additional Information
  @ApiProperty({ description: 'Property description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Additional notes', required: false })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({ 
    description: 'Property images URLs', 
    type: [String], 
    required: false 
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[];

  @ApiProperty({ 
    description: 'Custom fields', 
    type: Object, 
    required: false 
  })
  @IsObject()
  @IsOptional()
  customFields?: Record<string, any>;
}
