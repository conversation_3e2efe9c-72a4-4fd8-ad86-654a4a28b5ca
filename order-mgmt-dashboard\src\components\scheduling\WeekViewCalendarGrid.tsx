
import React, { useState } from 'react';
import WeekViewCalendarEvent from './WeekViewCalendarEvent';
import { getWeekViewDragEventData } from './weekViewUtils';

interface CalendarEvent {
  id: string;
  title: string;
  start: number;
  end: number;
  inspector: string;
  color: string;
}

interface EventPosition {
  width: number;
  left: number;
  zIndex?: number;
}

function calculateEventPositions(events: CalendarEvent[]): (CalendarEvent & { position: EventPosition })[] {
  if (events.length <= 1) {
    return events.map(event => ({
      ...event,
      position: { width: 96, left: 2 }
    }));
  }

  const eventsByInspector: Record<string, CalendarEvent[]> = {};
  events.forEach(event => {
    if (!eventsByInspector[event.inspector]) {
      eventsByInspector[event.inspector] = [];
    }
    eventsByInspector[event.inspector].push(event);
  });

  const inspectors = Object.keys(eventsByInspector);
  const inspectorCount = inspectors.length;

  const inspectorWidth = Math.max(96 / inspectorCount, 20);
  const result: (CalendarEvent & { position: EventPosition })[] = [];

  inspectors.forEach((inspector, index) => {
    const inspectorEvents = eventsByInspector[inspector];
    const inspectorLeft = 2 + (index * inspectorWidth);

    inspectorEvents.forEach(event => {
      result.push({
        ...event,
        position: {
          width: inspectorWidth - 1,
          left: inspectorLeft,
          zIndex: index + 1
        }
      });
    });
  });

  return result;
}

interface WeekViewCalendarGridProps {
  days: Date[];
  hours: number[];
  events: CalendarEvent[];
  handleDrop: (e: React.DragEvent, day: Date, hour: number) => void;
  handleDragStart: (e: React.DragEvent, eventId: string) => void;
}

const WeekViewCalendarGrid: React.FC<WeekViewCalendarGridProps> = ({
  days,
  hours,
  events,
  handleDrop,
  handleDragStart,
}) => {
  const [dragOverCell, setDragOverCell] = useState<string | null>(null);
  const [draggedInspector, setDraggedInspector] = useState<string | null>(null);

  const hasInspectorEvent = (day: Date, hour: number, inspectorName: string) => {
    if (!inspectorName) return false;

    const slotStart = new Date(day);
    slotStart.setHours(hour, 0, 0, 0);
    const slotEnd = new Date(day);
    slotEnd.setHours(hour + 1, 0, 0, 0);

    return events.some(event =>
      event.inspector === inspectorName &&
      ((event.start >= slotStart.getTime() && event.start < slotEnd.getTime()) ||
       (event.end > slotStart.getTime() && event.end <= slotEnd.getTime()) ||
       (event.start <= slotStart.getTime() && event.end >= slotEnd.getTime()))
    );
  };

  return (
    <>
      {hours.map(hour => (
        <div key={hour} className="grid grid-cols-[100px_repeat(7,1fr)] border-b hover:bg-gray-50/50 transition-colors">
          <div className="p-2 text-sm font-medium flex items-center justify-center">
            <span className={`rounded-full w-10 h-10 flex items-center justify-center ${hour === new Date().getHours() ? 'bg-blue-100 text-blue-800' : 'text-gray-600'}`}>
              {`${hour === 12 ? 12 : hour % 12 || 12}${hour < 12 ? 'am' : 'pm'}`}
            </span>
          </div>
          {days.map(day => {
            const slotStart = new Date(day);
            slotStart.setHours(hour, 0, 0, 0);
            const slotEnd = new Date(day);
            slotEnd.setHours(hour + 1, 0, 0, 0);
            const cellId = `${day.toISOString()}-${hour}`;
            const isBeingDraggedOver = dragOverCell === cellId;

            const hasConflict = draggedInspector && hasInspectorEvent(day, hour, draggedInspector);

            const slotEvents = events.filter(
              event =>
                event.start >= slotStart.getTime() && event.start < slotEnd.getTime()
            );

            const eventsWithPositions = calculateEventPositions(slotEvents);

            let cellClass = 'border-l p-1 min-h-[60px] relative transition-colors duration-150';

            cellClass += ' hover:bg-gray-50';

            const currentHour = new Date().getHours();
            if (hour === currentHour) {
              cellClass += ' bg-blue-50/40';
            }

            if (isBeingDraggedOver) {
              cellClass += hasConflict ? ' bg-red-100/80' : ' bg-emerald-100/80';
            }

            return (
              <div
                key={cellId}
                className={cellClass}
                onDragOver={e => {
                  e.preventDefault();
                  setDragOverCell(cellId);

                  try {
                    const data = getWeekViewDragEventData(e);
                    if (data && data.type === 'week') {
                      const draggedEvent = events.find(ev => ev.id === data.eventId);
                      if (draggedEvent) {
                        setDraggedInspector(draggedEvent.inspector);
                      }
                    }
                  } catch (err) {
                    console.error('Error getting drag data:', err);
                  }
                }}
                onDragLeave={() => {
                  if (dragOverCell === cellId) {
                    setDragOverCell(null);
                  }
                }}
                onDrop={e => {
                  setDragOverCell(null);
                  setDraggedInspector(null);
                  handleDrop(e, new Date(day), hour);
                }}
                style={{ minHeight: 48 }}
              >
                {eventsWithPositions.map(event => (
                  <WeekViewCalendarEvent
                    key={event.id}
                    event={event}
                    position={event.position}
                    onDragStart={(e, eventId) => {
                      handleDragStart(e, eventId);
                      const draggedEvent = events.find(ev => ev.id === eventId);
                      if (draggedEvent) {
                        setDraggedInspector(draggedEvent.inspector);
                      }
                    }}
                  />
                ))}
              </div>
            );
          })}
        </div>
      ))}
    </>
  );
};

export default WeekViewCalendarGrid;
