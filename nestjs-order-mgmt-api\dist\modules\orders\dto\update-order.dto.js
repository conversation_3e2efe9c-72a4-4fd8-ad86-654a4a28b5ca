"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_order_dto_1 = require("./create-order.dto");
const swagger_2 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const order_entity_1 = require("../entities/order.entity");
class UpdateOrderDto extends (0, swagger_1.PartialType)(create_order_dto_1.CreateOrderDto) {
}
exports.UpdateOrderDto = UpdateOrderDto;
__decorate([
    (0, swagger_2.ApiProperty)({
        description: 'Order status',
        enum: order_entity_1.OrderStatus,
        required: false
    }),
    (0, class_validator_1.IsEnum)(order_entity_1.OrderStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({
        description: 'Assigned inspector IDs',
        type: [Number],
        required: false
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateOrderDto.prototype, "assignedInspectorIds", void 0);
__decorate([
    (0, swagger_2.ApiProperty)({
        description: 'Inspection date',
        type: Date,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateOrderDto.prototype, "inspectionDate", void 0);
//# sourceMappingURL=update-order.dto.js.map