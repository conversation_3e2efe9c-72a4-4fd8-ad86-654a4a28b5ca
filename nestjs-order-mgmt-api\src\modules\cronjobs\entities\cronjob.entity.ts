import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum JobType {
  EMAIL_REMINDER = 'email_reminder',
  CLEANUP_LOGS = 'cleanup_logs',
  GENERATE_REPORTS = 'generate_reports',
  SYNC_DATA = 'sync_data',
  BACKUP_DATABASE = 'backup_database',
  UPDATE_SCHEDULES = 'update_schedules',
  SEND_NOTIFICATIONS = 'send_notifications',
  CUSTOM = 'custom',
}

@Entity('cronjobs')
export class Cronjob {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 100 })
  schedule: string;

  @Column({
    type: 'enum',
    enum: JobType,
  })
  jobType: JobType;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.PENDING,
  })
  status: JobStatus;

  @Column({ type: 'jsonb', nullable: true })
  configuration: any;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  timeout: number;

  @Column({ default: 3 })
  maxRetries: number;

  @Column({ nullable: true })
  lastRunAt: Date;

  @Column({ nullable: true })
  lastCompletedAt: Date;

  @Column({ default: 0 })
  runCount: number;

  @Column({ default: 0 })
  failureCount: number;

  @Column({ type: 'text', nullable: true })
  lastError: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
