import { Repository } from 'typeorm';
import { User, UserRole } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserQueryDto } from './dto/user-query.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class UsersService {
    private readonly userRepository;
    constructor(userRepository: Repository<User>);
    create(createUserDto: CreateUserDto): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    findAll(query: UserQueryDto): Promise<{
        users: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            orders: import("../orders/entities/order.entity").Order[];
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<{
        id: number;
        name: string;
        email: string;
        phone: string;
        role: UserRole;
        isActive: boolean;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
        orders: import("../orders/entities/order.entity").Order[];
    }>;
    findByEmail(email: string): Promise<User>;
    update(id: number, updateUserDto: UpdateUserDto): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    updateStatus(id: number, isActive: boolean): Promise<{
        message: string;
    }>;
    changePassword(id: number, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getUserOrders(id: number, query: any): Promise<{
        orders: import("../orders/entities/order.entity").Order[];
        total: number;
    }>;
    getUserActivity(id: number): Promise<{
        userId: number;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getUserStats(): Promise<{
        totalUsers: number;
        activeUsers: number;
        inactiveUsers: number;
        usersByRole: any[];
    }>;
}
