import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum TemplateType {
  EMAIL = 'email',
  SMS = 'sms',
  NOTIFICATION = 'notification',
  REPORT = 'report',
  DOCUMENT = 'document',
}

export enum TemplateCategory {
  ORDER_CONFIRMATION = 'order_confirmation',
  SCHEDULE_NOTIFICATION = 'schedule_notification',
  INSPECTION_REMINDER = 'inspection_reminder',
  COMPLETION_NOTICE = 'completion_notice',
  CANCELLATION_NOTICE = 'cancellation_notice',
  PAYMENT_REMINDER = 'payment_reminder',
  WELCOME_MESSAGE = 'welcome_message',
  CUSTOM = 'custom',
}

@Entity('templates')
export class Template {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, length: 100 })
  name: string;

  @Column({ length: 255 })
  subject: string;

  @Column({
    type: 'enum',
    enum: TemplateType,
  })
  type: TemplateType;

  @Column({
    type: 'enum',
    enum: TemplateCategory,
  })
  category: TemplateCategory;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'text', nullable: true })
  htmlContent: string;

  @Column({ type: 'jsonb', nullable: true })
  variables: {
    name: string;
    description: string;
    required: boolean;
    defaultValue?: string;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    description?: string;
    tags?: string[];
    version?: string;
    author?: string;
  };

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isDefault: boolean;

  @Column({ nullable: true, length: 50 })
  language: string;

  @Column({ type: 'text', nullable: true })
  previewText: string;

  @Column({ type: 'jsonb', nullable: true })
  styling: {
    backgroundColor?: string;
    textColor?: string;
    fontFamily?: string;
    fontSize?: string;
    customCss?: string;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
