import { Schedule } from '../../schedules/entities/schedule.entity';
export declare enum OrderStatus {
    PENDING = "pending",
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare enum PropertyType {
    SINGLE_FAMILY = "single_family",
    CONDO = "condo",
    TOWNHOUSE = "townhouse",
    MULTI_FAMILY = "multi_family",
    COMMERCIAL = "commercial"
}
export declare class Order {
    id: number;
    orderNumber: string;
    status: OrderStatus;
    clientName: string;
    clientEmail: string;
    clientPhone: string;
    addressLine1: string;
    city: string;
    zipCode: string;
    state: string;
    propertyType: PropertyType;
    yearBuilt: number;
    foundationType: string;
    gateCode: string;
    lockboxCode: string;
    alarmCode: string;
    mlsNumber: string;
    note: string;
    isClientAttending: boolean;
    isOccupied: boolean;
    hasUtilities: boolean;
    hasAlarm: boolean;
    services: {
        flexfund?: boolean;
        mold?: boolean;
        [key: string]: boolean;
    };
    agentName: string;
    agentEmail: string;
    agentPhone: string;
    isSeller: boolean;
    isBuyer: boolean;
    inspectionFee: number;
    thirdPartyFee: number;
    discountFee: number;
    processingFee: number;
    inspectionDate: Date;
    scheduledDate: string;
    scheduledTime: string;
    assignedInspectorIds: number[];
    completedAt: Date;
    cancelledAt: Date;
    cancellationReason: string;
    inspectionReport: string;
    inspectionNotes: string;
    createdAt: Date;
    updatedAt: Date;
    clientIds: number[];
    schedules: Schedule[];
    property: any;
}
