# API Client with Axios and JWT Interceptors

This document describes the axios-based API client implementation with automatic JWT token refresh functionality.

## Features

- **Axios-based HTTP client** replacing the original fetch implementation
- **Automatic JWT token refresh** when receiving 401 responses
- **Request/Response interceptors** for seamless token management
- **Queue management** for concurrent requests during token refresh
- **Automatic redirect** to login page when refresh fails
- **TypeScript support** with proper type definitions

## Architecture

### Core Components

1. **axiosInstance**: Main axios instance with base configuration
2. **Request Interceptor**: Automatically adds JWT tokens to requests
3. **Response Interceptor**: Handles 401 responses and token refresh
4. **apiClient Function**: Wrapper function for making API calls
5. **Queue Management**: Handles concurrent requests during token refresh

### Token Refresh Flow

```
1. API Request → 401 Response
2. Check if already refreshing
   - If yes: Queue the request
   - If no: Start refresh process
3. Attempt token refresh with refresh_token
4. On success:
   - Update tokens in localStorage
   - Retry original request
   - Process queued requests
5. On failure:
   - Clear all tokens
   - Redirect to login page
   - Reject all queued requests
```

## Usage

### Basic API Calls

```typescript
import { apiClient } from '@/lib/api-client';

// GET request
const orders = await apiClient<OrdersResponse>('orders?page=1&pageSize=10');

// POST request
const newOrder = await apiClient<OrderResponse>('orders', {
  method: 'POST',
  data: { /* order data */ }
});

// PUT/PATCH request
const updatedOrder = await apiClient<OrderResponse>(`orders/${id}`, {
  method: 'PATCH',
  data: { /* updated data */ }
});

// DELETE request
await apiClient(`orders/${id}`, {
  method: 'DELETE'
});
```

### Direct Axios Usage

```typescript
import { jwtAxios } from '@/lib/api-client';

// Direct axios calls (with interceptors)
const response = await jwtAxios.get('orders');
const data = await jwtAxios.post('orders', orderData);
```

## Configuration

### Base Configuration

```typescript
const axiosInstance = axios.create({
  baseURL: `${BASE_URL}/api/${API_VERSION}`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});
```

### Environment Variables

- `BASE_URL`: API base URL (currently hardcoded)

## Token Management

### Storage

Tokens are stored in localStorage:
- `access_token`: JWT access token
- `refresh_token`: JWT refresh token
- `user`: User information (JSON string)

### Automatic Refresh

The interceptor automatically:
1. Detects 401 responses
2. Attempts token refresh using the refresh token
3. Updates stored tokens on success
4. Retries the original request
5. Handles concurrent requests during refresh

### Security Considerations

- Tokens are automatically cleared on refresh failure
- User is redirected to login page when authentication fails
- Request queue prevents multiple refresh attempts
- Sensitive operations are protected by token validation

## Error Handling

### HTTP Errors

- **401 Unauthorized**: Triggers automatic token refresh
- **Other 4xx/5xx**: Shows error toast and returns null
- **Network Errors**: Shows network error toast

### Token Refresh Errors

- Invalid refresh token: Clears tokens and redirects to login
- Network errors during refresh: Same as above
- Server errors: Same as above

## Testing

Use the `AxiosTestComponent` to test the implementation:

1. Navigate to `/redux-example`
2. Use the "Axios & JWT Interceptor Test" section
3. Test various scenarios:
   - Normal API calls
   - Direct axios calls
   - Token refresh simulation
   - Token management

## Migration from Fetch

### Changes Made

1. **Replaced fetch with axios**
2. **Updated request format**:
   - `body: JSON.stringify(data)` → `data: data`
   - `headers` → axios headers format
3. **Updated response handling**:
   - `response.json()` → `response.data`
   - Automatic JSON parsing
4. **Added interceptors** for token management
5. **Updated error handling** for axios errors

### Breaking Changes

- API client function signature changed from `RequestInit` to `AxiosRequestConfig`
- Response format is now axios response format
- Error handling uses axios error types

## Best Practices

1. **Use apiClient function** for most API calls
2. **Use jwtAxios directly** only when you need axios-specific features
3. **Handle errors gracefully** - apiClient returns null on errors
4. **Check for null responses** before using data
5. **Use TypeScript types** for better type safety
6. **Test token refresh scenarios** in development

## Troubleshooting

### Common Issues

1. **401 loops**: Check refresh token endpoint and format
2. **CORS errors**: Verify server CORS configuration
3. **Token format**: Ensure tokens are in correct format
4. **Base URL**: Verify API base URL configuration

### Debug Tips

1. Check browser console for interceptor logs
2. Monitor Network tab for request/response details
3. Verify localStorage token values
4. Use the test component for isolated testing
