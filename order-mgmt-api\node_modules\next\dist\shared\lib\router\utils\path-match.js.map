{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/path-match.ts"], "sourcesContent": ["import type { Key } from 'next/dist/compiled/path-to-regexp'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { regexpToFunction } from 'next/dist/compiled/path-to-regexp'\n\ninterface Options {\n  /**\n   * A transformer function that will be applied to the regexp generated\n   * from the provided path and path-to-regexp.\n   */\n  regexModifier?: (regex: string) => string\n  /**\n   * When true the function will remove all unnamed parameters\n   * from the matched parameters.\n   */\n  removeUnnamedParams?: boolean\n  /**\n   * When true the regexp won't allow an optional trailing delimiter\n   * to match.\n   */\n  strict?: boolean\n\n  /**\n   * When true the matcher will be case-sensitive, defaults to false\n   */\n  sensitive?: boolean\n}\n\nexport type PatchMatcher = (\n  pathname: string,\n  params?: Record<string, any>\n) => Record<string, any> | false\n\n/**\n * Generates a path matcher function for a given path and options based on\n * path-to-regexp. By default the match will be case insensitive, non strict\n * and delimited by `/`.\n */\nexport function getPathMatch(path: string, options?: Options): PatchMatcher {\n  const keys: Key[] = []\n  const regexp = pathToRegexp(path, keys, {\n    delimiter: '/',\n    sensitive:\n      typeof options?.sensitive === 'boolean' ? options.sensitive : false,\n    strict: options?.strict,\n  })\n\n  const matcher = regexpToFunction<Record<string, any>>(\n    options?.regexModifier\n      ? new RegExp(options.regexModifier(regexp.source), regexp.flags)\n      : regexp,\n    keys\n  )\n\n  /**\n   * A matcher function that will check if a given pathname matches the path\n   * given in the builder function. When the path does not match it will return\n   * `false` but if it does it will return an object with the matched params\n   * merged with the params provided in the second argument.\n   */\n  return (pathname, params) => {\n    // If no pathname is provided it's not a match.\n    if (typeof pathname !== 'string') return false\n\n    const match = matcher(pathname)\n\n    // If the path did not match `false` will be returned.\n    if (!match) return false\n\n    /**\n     * If unnamed params are not allowed they must be removed from\n     * the matched parameters. path-to-regexp uses \"string\" for named and\n     * \"number\" for unnamed parameters.\n     */\n    if (options?.removeUnnamedParams) {\n      for (const key of keys) {\n        if (typeof key.name === 'number') {\n          delete match.params[key.name]\n        }\n      }\n    }\n\n    return { ...params, ...match.params }\n  }\n}\n"], "names": ["getPathMatch", "path", "options", "keys", "regexp", "pathToRegexp", "delimiter", "sensitive", "strict", "matcher", "regexpToFunction", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "match", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": ";;;;+BAqCgBA;;;eAAAA;;;8BApCa;AAoCtB,SAASA,aAAaC,IAAY,EAAEC,OAAiB;IAC1D,MAAMC,OAAc,EAAE;IACtB,MAAMC,SAASC,IAAAA,0BAAY,EAACJ,MAAME,MAAM;QACtCG,WAAW;QACXC,WACE,QAAOL,2BAAAA,QAASK,SAAS,MAAK,YAAYL,QAAQK,SAAS,GAAG;QAChEC,MAAM,EAAEN,2BAAAA,QAASM,MAAM;IACzB;IAEA,MAAMC,UAAUC,IAAAA,8BAAgB,EAC9BR,CAAAA,2BAAAA,QAASS,aAAa,IAClB,IAAIC,OAAOV,QAAQS,aAAa,CAACP,OAAOS,MAAM,GAAGT,OAAOU,KAAK,IAC7DV,QACJD;IAGF;;;;;GAKC,GACD,OAAO,CAACY,UAAUC;QAChB,+CAA+C;QAC/C,IAAI,OAAOD,aAAa,UAAU,OAAO;QAEzC,MAAME,QAAQR,QAAQM;QAEtB,sDAAsD;QACtD,IAAI,CAACE,OAAO,OAAO;QAEnB;;;;KAIC,GACD,IAAIf,2BAAAA,QAASgB,mBAAmB,EAAE;YAChC,KAAK,MAAMC,OAAOhB,KAAM;gBACtB,IAAI,OAAOgB,IAAIC,IAAI,KAAK,UAAU;oBAChC,OAAOH,MAAMD,MAAM,CAACG,IAAIC,IAAI,CAAC;gBAC/B;YACF;QACF;QAEA,OAAO;YAAE,GAAGJ,MAAM;YAAE,GAAGC,MAAMD,MAAM;QAAC;IACtC;AACF"}