import { PropertiesService } from './properties.service';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { PropertyQueryDto } from './dto/property-query.dto';
import { CreatePropertyTagDto } from './dto/create-property-tag.dto';
export declare class PropertiesController {
    private readonly propertiesService;
    constructor(propertiesService: PropertiesService);
    create(createPropertyDto: CreatePropertyDto, user: any): Promise<{
        property: import("./entities/property.entity").Property;
        message: string;
    }>;
    findAll(query: PropertyQueryDto): Promise<{
        properties: import("./entities/property.entity").Property[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    search(searchTerm: string): Promise<import("./entities/property.entity").Property[]>;
    findNearby(latitude: number, longitude: number, radius?: number): Promise<{
        properties: import("./entities/property.entity").Property[];
        center: {
            latitude: number;
            longitude: number;
        };
        radius: number;
    }>;
    findOne(id: number): Promise<import("./entities/property.entity").Property>;
    update(id: number, updatePropertyDto: UpdatePropertyDto): Promise<{
        property: import("./entities/property.entity").Property;
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getPropertyOrders(id: number): Promise<{
        propertyId: number;
        orders: import("../orders/entities/order.entity").Order[];
        totalOrders: number;
    }>;
    createTag(createTagDto: CreatePropertyTagDto): Promise<{
        tag: import("./entities/property-tag.entity").PropertyTag;
        message: string;
    }>;
    getTags(): Promise<import("./entities/property-tag.entity").PropertyTag[]>;
    updatePropertyTags(id: number, tagIds: {
        tagIds: number[];
    }): Promise<{
        message: string;
        tags: import("./entities/property-tag.entity").PropertyTag[];
    }>;
}
