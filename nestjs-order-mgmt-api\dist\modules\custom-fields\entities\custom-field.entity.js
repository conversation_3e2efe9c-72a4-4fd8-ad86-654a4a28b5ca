"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomField = exports.CustomFieldEntity = exports.CustomFieldType = void 0;
const typeorm_1 = require("typeorm");
const custom_field_value_entity_1 = require("./custom-field-value.entity");
var CustomFieldType;
(function (CustomFieldType) {
    CustomFieldType["TEXT"] = "text";
    CustomFieldType["TEXTAREA"] = "textarea";
    CustomFieldType["NUMBER"] = "number";
    CustomFieldType["EMAIL"] = "email";
    CustomFieldType["PHONE"] = "phone";
    CustomFieldType["URL"] = "url";
    CustomFieldType["DATE"] = "date";
    CustomFieldType["DATETIME"] = "datetime";
    CustomFieldType["BOOLEAN"] = "boolean";
    CustomFieldType["SELECT"] = "select";
    CustomFieldType["MULTISELECT"] = "multiselect";
    CustomFieldType["RADIO"] = "radio";
    CustomFieldType["CHECKBOX"] = "checkbox";
    CustomFieldType["FILE"] = "file";
    CustomFieldType["IMAGE"] = "image";
    CustomFieldType["JSON"] = "json";
})(CustomFieldType || (exports.CustomFieldType = CustomFieldType = {}));
var CustomFieldEntity;
(function (CustomFieldEntity) {
    CustomFieldEntity["ORDER"] = "order";
    CustomFieldEntity["PROPERTY"] = "property";
    CustomFieldEntity["INSPECTOR"] = "inspector";
    CustomFieldEntity["USER"] = "user";
    CustomFieldEntity["SCHEDULE"] = "schedule";
})(CustomFieldEntity || (exports.CustomFieldEntity = CustomFieldEntity = {}));
let CustomField = class CustomField {
};
exports.CustomField = CustomField;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CustomField.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], CustomField.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 100 }),
    __metadata("design:type", String)
], CustomField.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomField.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CustomFieldType,
    }),
    __metadata("design:type", String)
], CustomField.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CustomFieldEntity,
    }),
    __metadata("design:type", String)
], CustomField.prototype, "entityType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], CustomField.prototype, "options", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CustomField.prototype, "defaultValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isVisible", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CustomField.prototype, "isSearchable", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], CustomField.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 100 }),
    __metadata("design:type", String)
], CustomField.prototype, "group", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    __metadata("design:type", Array)
], CustomField.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CustomField.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CustomField.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => custom_field_value_entity_1.CustomFieldValue, (value) => value.customField),
    __metadata("design:type", Array)
], CustomField.prototype, "values", void 0);
exports.CustomField = CustomField = __decorate([
    (0, typeorm_1.Entity)('custom_fields')
], CustomField);
//# sourceMappingURL=custom-field.entity.js.map