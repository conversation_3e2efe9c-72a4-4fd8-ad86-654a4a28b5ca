"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const schedules_service_1 = require("./schedules.service");
const create_schedule_dto_1 = require("./dto/create-schedule.dto");
const update_schedule_dto_1 = require("./dto/update-schedule.dto");
const schedule_query_dto_1 = require("./dto/schedule-query.dto");
const check_conflict_dto_1 = require("./dto/check-conflict.dto");
const bulk_create_schedule_dto_1 = require("./dto/bulk-create-schedule.dto");
const auth_decorator_1 = require("../../common/decorators/auth.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let SchedulesController = class SchedulesController {
    constructor(schedulesService) {
        this.schedulesService = schedulesService;
    }
    async create(createScheduleDto, user) {
        return this.schedulesService.create(createScheduleDto, user);
    }
    async bulkCreate(bulkCreateDto, user) {
        return this.schedulesService.bulkCreate(bulkCreateDto, user);
    }
    async findAll(query, user) {
        return this.schedulesService.findAll(query, user);
    }
    async checkConflicts(checkConflictDto) {
        return this.schedulesService.checkConflicts(checkConflictDto);
    }
    async getAvailability(inspectorId, startDate, endDate) {
        return this.schedulesService.getInspectorAvailability(inspectorId, startDate, endDate);
    }
    async getCalendar(inspectorId, month, year) {
        return this.schedulesService.getCalendarView(inspectorId, month, year);
    }
    async findOne(id, user) {
        return this.schedulesService.findOne(id, user);
    }
    async update(id, updateScheduleDto, user) {
        return this.schedulesService.update(id, updateScheduleDto, user);
    }
    async assignOrder(id, assignData, user) {
        return this.schedulesService.assignOrder(id, assignData.orderId, user);
    }
    async unassignOrder(id, user) {
        return this.schedulesService.unassignOrder(id, user);
    }
    async remove(id, user) {
        return this.schedulesService.remove(id, user);
    }
    async getStats() {
        return this.schedulesService.getScheduleStats();
    }
    async createRecurring(recurringData, user) {
        return this.schedulesService.createRecurringSchedules(recurringData, user);
    }
};
exports.SchedulesController = SchedulesController;
__decorate([
    (0, common_1.Post)(),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new schedule' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Schedule successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Schedule conflict' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_schedule_dto_1.CreateScheduleDto, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Create multiple schedules' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Schedules successfully created' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bulk_create_schedule_dto_1.BulkCreateScheduleDto, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "bulkCreate", null);
__decorate([
    (0, common_1.Get)(),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all schedules with filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Schedules retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'inspectorId', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'date', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'available', required: false, type: Boolean }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [schedule_query_dto_1.ScheduleQueryDto, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('conflicts'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Check for schedule conflicts' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conflict check completed' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [check_conflict_dto_1.CheckConflictDto]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "checkConflicts", null);
__decorate([
    (0, common_1.Get)('availability/:inspectorId'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get inspector availability' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Availability retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: true, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: true, type: String }),
    __param(0, (0, common_1.Param)('inspectorId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "getAvailability", null);
__decorate([
    (0, common_1.Get)('calendar/:inspectorId'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Get inspector calendar view' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Calendar data retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'month', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'year', required: false, type: String }),
    __param(0, (0, common_1.Param)('inspectorId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('month')),
    __param(2, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "getCalendar", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, auth_decorator_1.Auth)('admin', 'inspector', 'client'),
    (0, swagger_1.ApiOperation)({ summary: 'Get schedule by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Schedule retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Schedule not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Update schedule' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Schedule updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Schedule not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Schedule conflict' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_schedule_dto_1.UpdateScheduleDto, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/assign-order'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign order to schedule' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Order assigned successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "assignOrder", null);
__decorate([
    (0, common_1.Patch)(':id/unassign-order'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Unassign order from schedule' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Order unassigned successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "unassignOrder", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete schedule' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Schedule deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Schedule not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('stats/overview'),
    (0, auth_decorator_1.Auth)('admin'),
    (0, swagger_1.ApiOperation)({ summary: 'Get schedule statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "getStats", null);
__decorate([
    (0, common_1.Post)('recurring'),
    (0, auth_decorator_1.Auth)('admin', 'inspector'),
    (0, swagger_1.ApiOperation)({ summary: 'Create recurring schedules' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Recurring schedules created' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SchedulesController.prototype, "createRecurring", null);
exports.SchedulesController = SchedulesController = __decorate([
    (0, swagger_1.ApiTags)('Schedules'),
    (0, common_1.Controller)('schedules'),
    __metadata("design:paramtypes", [schedules_service_1.SchedulesService])
], SchedulesController);
//# sourceMappingURL=schedules.controller.js.map