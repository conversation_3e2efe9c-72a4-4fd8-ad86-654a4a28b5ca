import { Order } from '../../orders/entities/order.entity';
export declare enum UserRole {
    ADMIN = "admin",
    INSPECTOR = "inspector",
    CLIENT = "client"
}
export declare class User {
    id: number;
    name: string;
    email: string;
    password: string;
    phone: string;
    role: UserRole;
    isActive: boolean;
    lastLoginAt: Date;
    createdAt: Date;
    updatedAt: Date;
    orders: Order[];
}
