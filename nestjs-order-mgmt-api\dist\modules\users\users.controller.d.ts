import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserQueryDto } from './dto/user-query.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: import("./entities/user.entity").UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            notes: string;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    findAll(query: UserQueryDto): Promise<{
        users: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: import("./entities/user.entity").UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            notes: string;
            orders: import("../orders/entities/order.entity").Order[];
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getProfile(user: any): Promise<{
        id: number;
        name: string;
        email: string;
        phone: string;
        role: import("./entities/user.entity").UserRole;
        isActive: boolean;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
        notes: string;
        orders: import("../orders/entities/order.entity").Order[];
    }>;
    findOne(id: number): Promise<{
        id: number;
        name: string;
        email: string;
        phone: string;
        role: import("./entities/user.entity").UserRole;
        isActive: boolean;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
        notes: string;
        orders: import("../orders/entities/order.entity").Order[];
    }>;
    updateProfile(updateUserDto: UpdateUserDto, user: any): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: import("./entities/user.entity").UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            notes: string;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    update(id: number, updateUserDto: UpdateUserDto): Promise<{
        user: {
            id: number;
            name: string;
            email: string;
            phone: string;
            role: import("./entities/user.entity").UserRole;
            isActive: boolean;
            lastLoginAt: Date;
            createdAt: Date;
            updatedAt: Date;
            notes: string;
            orders: import("../orders/entities/order.entity").Order[];
        };
        message: string;
    }>;
    activate(id: number): Promise<{
        message: string;
    }>;
    deactivate(id: number): Promise<{
        message: string;
    }>;
    changePassword(changePasswordDto: ChangePasswordDto, user: any): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getUserOrders(id: number, query: any): Promise<{
        orders: import("../orders/entities/order.entity").Order[];
        total: number;
        message: string;
    }>;
    getUserActivity(id: number): Promise<{
        userId: number;
        lastLoginAt: Date;
        createdAt: Date;
        updatedAt: Date;
    }>;
}
