import { TemplateType, TemplateCategory } from '../entities/template.entity';
declare class TemplateVariableDto {
    name: string;
    description: string;
    required: boolean;
    defaultValue?: string;
}
declare class TemplateMetadataDto {
    description?: string;
    tags?: string[];
    version?: string;
    author?: string;
}
declare class TemplateStylingDto {
    backgroundColor?: string;
    textColor?: string;
    fontFamily?: string;
    fontSize?: string;
    customCss?: string;
}
export declare class CreateTemplateDto {
    name: string;
    subject: string;
    type: TemplateType;
    category: TemplateCategory;
    content: string;
    htmlContent?: string;
    variables?: TemplateVariableDto[];
    metadata?: TemplateMetadataDto;
    isActive?: boolean;
    isDefault?: boolean;
    language?: string;
    previewText?: string;
    styling?: TemplateStylingDto;
}
export {};
