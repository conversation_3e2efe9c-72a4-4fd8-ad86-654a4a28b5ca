import DashboardHeader from "@/components/DashboardHeader";
import OrderDetailsModal from "@/components/OrderDetailsModal";
import OrdersControlsBar from "@/components/orders/OrdersControlsBar";
import OrdersMobileCards from "@/components/orders/OrdersMobileCards";
import OrdersPagination from "@/components/orders/OrdersPagination";
import OrdersTable from "@/components/orders/OrdersTable";
import ScheduleOrderModal from "@/components/ScheduleOrderModal";
import Sidebar from "@/components/Sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useToast } from "@/hooks/use-toast";
import { apiClient } from "@/lib/api-client";
import { useAppDispatch } from "@/redux/hooks";
import { fetchOrders, updateOrder, addOrder } from "@/redux/slices/ordersSlice";
import { scheduleService } from "@/services/api/schedules.service";
import { Order as ApiOrder, OrderFormData, StatusType, PropertyType } from "@/types/order";
import { useEffect, useState } from "react";

const generateSampleOrders = (count: number) => {
  const statuses = [
    "pending",
    "scheduled",
    "in progress",
    "inspected",
    "reported",
    "completed",
    "cancelled",
  ] as const;

  const today = new Date();

  return Array.from({ length: count }, (_, idx) => {
    const status = statuses[idx % statuses.length];
    const day = (idx % 28) + 1;
    const date = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() + day
    );
    return {
      id: (idx + 1).toString(),
      orderNumber: `ORD-${(10001 + idx).toString().padStart(5, "0")}`,
      propertyAddress: `${100 + idx} Elm St, City${idx % 10}`,
      inspectorName: [
        "Alice Johnson",
        "Bob Smith",
        "Carlos Lee",
        "Dana Kim",
        "Evan Turner",
      ][idx % 5],
      inspectionDate: date.toISOString().slice(0, 10),
      status: status,
      cost: status === "cancelled" ? 0 : 250 + (idx % 5) * 10,
    };
  });
};

type Activity = {
  id: string;
  date: string;
  type: string;
  description: string;
};

type InspectionOrder = {
  id: string;
  orderNumber: string;
  propertyAddress: string;
  inspectorName: string;
  inspectionDate: string;
  status:
    | "pending"
    | "cancelled"
    | "scheduled"
    | "in progress"
    | "inspected"
    | "reported"
    | "completed";
  cost: number;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  clientTags?: string[];
  activities?: Activity[];
};

const statusColors: Record<InspectionOrder["status"], string> = {
  pending: "bg-yellow-200 text-yellow-800",
  cancelled: "bg-red-100 text-red-600",
  scheduled: "bg-blue-100 text-blue-700",
  "in progress": "bg-indigo-100 text-indigo-700",
  inspected: "bg-green-100 text-green-700",
  reported: "bg-gray-100 text-gray-700",
  completed: "bg-green-200 text-green-900",
};

const ORDERS_PER_PAGE = 10;

export default function Orders() {
  const isMobile = useIsMobile();
  const [search, setSearch] = useState("");
  const [orders, setOrders] = useState<InspectionOrder[]>([]);
  const { toast } = useToast();
  const [page, setPage] = useState(1);
  const [selectedOrder, setSelectedOrder] = useState<InspectionOrder | null>(
    null
  );
  const [modalMode, setModalMode] = useState<"view" | "edit" | "add">("view");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalAnchorPoint, setModalAnchorPoint] = useState<
    { x: number; y: number } | undefined
  >();
  const [layout, setLayout] = useState<"grid" | "list">("list");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  const [selectedOrderForSchedule, setSelectedOrderForSchedule] =
    useState<InspectionOrder | null>(null);
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false);

  const mapApiOrderToInspectionOrder = (
    apiOrder: ApiOrder
  ): InspectionOrder => {
    return {
      id: apiOrder.id.toString(),
      orderNumber: apiOrder.inspectionOrderId,
      propertyAddress: apiOrder.propertyAddress,
      inspectorName: "Assigned Inspector",
      inspectionDate: apiOrder.inspectionDate.split("T")[0],
      status: apiOrder.status,
      cost: apiOrder.inspectionFees,
      clientName: apiOrder.clientName,
      clientEmail: apiOrder.clientEmail,
      clientPhone: apiOrder.clientPhone,
    };
  };

  const dispatch = useAppDispatch();

  useEffect(() => {
    setIsLoading(true);

    // TODO: Update orders from API response result, remove dummy
    dispatch(fetchOrders({ page: 1, pageSize: ORDERS_PER_PAGE }))
      .unwrap()
      .then((result) => {
        if (result && result.orders && result.orders.length > 0) {
          const mappedOrders = result.orders.map(mapApiOrderToInspectionOrder);
          setOrders(mappedOrders);
        } else {
          toast({
            title: "Using sample data",
            description: "No orders found in API.",
            duration: 3000,
          });
        }
      })
      .catch((error) => {
        toast({
          title: "Error fetching orders",
          description:
            "Could not fetch orders from API.",
          duration: 3000,
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [dispatch, toast]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(1);
  };

  const handleAddClick = () => {
    setModalMode("add");
    setSelectedOrder(null);
    setIsModalOpen(true);
  };

  const handleView = (order: InspectionOrder, e?: React.MouseEvent) => {
    setModalMode("view");
    setSelectedOrder({
      ...order,
      clientName: order.clientName || "John Doe",
      clientEmail: order.clientEmail || "<EMAIL>",
      clientPhone: order.clientPhone || "(*************",
      clientTags: ["walk-ins", "call-first"],
      activities: [
        {
          id: "1",
          date: new Date().toLocaleDateString(),
          type: "Status Change",
          description: `Status changed to ${order.status}`,
        },
        {
          id: "2",
          date: new Date(
            new Date().setDate(new Date().getDate() - 1)
          ).toLocaleDateString(),
          type: "Order Created",
          description: "Inspection order was created",
        },
      ],
    });
    setModalAnchorPoint(e ? { x: e.clientX, y: e.clientY } : undefined);
    setIsModalOpen(true);
  };

  const handleEdit = (order: InspectionOrder) => {
    setModalMode("edit");
    setSelectedOrder(order);
    setIsModalOpen(true);
  };

  const createNewOrder = async (formData: OrderFormData) => {
    try {
      const inspectionFee = formData.inspectionFee ? parseFloat(formData.inspectionFee) : 0;
      const thirdPartyFee = formData.thirdPartyFee ? parseFloat(formData.thirdPartyFee) : 0;
      const discountFee = formData.discountFee ? parseFloat(formData.discountFee) : 0;
      const processingFee = formData.processingFee ? parseFloat(formData.processingFee) : 0;

      const totalFees = inspectionFee + thirdPartyFee - discountFee + processingFee;

      const newOrderData = {
        inspectionOrderId: `ORD-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`,
        clientName: formData.clientName,
        clientEmail: formData.clientEmail,
        clientPhone: formData.clientPhone,
        propertyAddress: formData.propertyType || "123 Main St",
        propertyType: (formData.propertyType || "single") as PropertyType,
        status: formData.status as StatusType,
        inspectionDate: new Date().toISOString(),
        inspectionFees: totalFees,
        assignedInspectorIds: [],
      };

      const result = await dispatch(addOrder(newOrderData)).unwrap();

      if (result) {
        const newOrder: InspectionOrder = {
          id: result.id.toString(),
          orderNumber: result.inspectionOrderId || `ORD-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`,
          propertyAddress: result.propertyAddress,
          inspectorName: "Unassigned",
          inspectionDate: result.inspectionDate.split('T')[0],
          status: result.status,
          cost: result.inspectionFees,
          clientName: result.clientName,
          clientEmail: result.clientEmail,
          clientPhone: result.clientPhone,
        };

        setOrders(prev => [newOrder, ...prev]);

        toast({
          title: "Order Created",
          description: `New inspection order has been created successfully.`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Error creating order:", error);

      const tempOrder: InspectionOrder = {
        id: `temp-${Date.now()}`,
        orderNumber: `ORD-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`,
        propertyAddress: formData.propertyType || "123 Main St",
        inspectorName: "Unassigned",
        inspectionDate: new Date().toISOString().split('T')[0],
        status: formData.status as StatusType,
        cost: formData.inspectionFee ? parseFloat(formData.inspectionFee) : 0,
        clientName: formData.clientName,
        clientEmail: formData.clientEmail,
        clientPhone: formData.clientPhone,
      };

      setOrders(prev => [tempOrder, ...prev]);

      toast({
        title: "Order Created Locally",
        description: "New order created locally only. API creation failed.",
        duration: 3000,
      });
    }
  }

  const handleSaveOrder = async (orderId: string, formData: OrderFormData) => {
    if (orderId === "new") {
      createNewOrder(formData);
      return;
    }

    try {
      const orderData = {
        clientName: formData.clientName,
        clientEmail: formData.clientEmail,
        clientPhone: formData.clientPhone,
        status: formData.status as StatusType,
      };

      const result = await dispatch(
        updateOrder({
          id: parseInt(orderId),
          order: orderData,
        })
      ).unwrap();

      // TODO: Update orders from API response result, remove dummy
      if (result) {
        const updatedOrders = orders.map((order) =>
          order.id === orderId
            ? {
                ...order,
                clientName: formData.clientName,
                clientEmail: formData.clientEmail,
                clientPhone: formData.clientPhone,
                status: formData.status as StatusType,
              }
            : order);
        setOrders(updatedOrders);

        toast({
          title: "Order Updated",
          description: `Order #${orderId} has been updated successfully.`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Error updating order:", error);

      const updatedOrders = orders.map((order) =>
        order.id === orderId
          ? {
              ...order,
              clientName: formData.clientName,
              clientEmail: formData.clientEmail,
              clientPhone: formData.clientPhone,
              status: formData.status as StatusType,
            }
          : order);
      setOrders(updatedOrders);

      toast({
        title: "Order Updated Locally",
        description: `Order #${orderId} updated locally only. API update failed.`,
        duration: 3000,
      });
    }
  };

  const handleChangeStatus = async (
    orderId: string,
    newStatus: InspectionOrder["status"]
  ) => {
    const orderToUpdate = orders.find((o) => o.id === orderId);
    if (!orderToUpdate) return;

    try {
      const result = await dispatch(
        updateOrder({
          id: parseInt(orderId),
          order: { status: newStatus },
        })
      ).unwrap();

      if (result) {
        setOrders((prev) =>
          prev.map((order) =>
            order.id === orderId ? { ...order, status: newStatus } : order
          )
        );
        toast({
          title: "Status Changed",
          description: `Order #${
            orderToUpdate.orderNumber
          } is now "${newStatus.replace(/^\w/, (c) => c.toUpperCase())}".`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      setOrders((prev) =>
        prev.map((order) =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );
      toast({
        title: "Status Changed Locally",
        description: `Order #${orderToUpdate.orderNumber} status updated locally only. API update failed.`,
        duration: 3000,
      });
    }
  };

  const handleSchedule = (order: InspectionOrder) => {
    setSelectedOrderForSchedule(order);
    setIsScheduleModalOpen(true);
  };

  const handleScheduleConfirm = async (
    orderId: string,
    date: string,
    startTime: string,
    endTime: string,
    inspectorId: string
  ) => {
    const orderToUpdate = orders.find((o) => o.id === orderId);
    if (!orderToUpdate) return;

    const inspectionDateTime = `${date}T${startTime}:00.000Z`;

    try {
      const scheduleResponse = await scheduleService.createSchedule({
        inspectorId: parseInt(inspectorId),
        date: date,
        startTime: startTime,
        endTime: endTime,
        available: false,
        inspectionOrderId: parseInt(orderId),
      });

      const orderResponse = await dispatch(
        updateOrder({
          id: parseInt(orderId),
          order: {
            inspectionDate: inspectionDateTime,
            status: "scheduled",
          },
        })
      ).unwrap();

      if ((scheduleResponse && scheduleResponse.success) || orderResponse) {
        setOrders((prev) =>
          prev.map((order) =>
            order.id === orderId
              ? {
                  ...order,
                  inspectionDate: date,
                  status: "scheduled",
                }
              : order
          )
        );
        toast({
          title: "Inspection Scheduled",
          description: `Order #${orderToUpdate.orderNumber} has been scheduled for ${date} at ${startTime}.`,
          duration: 2000,
        });
      } else {
        setOrders((prev) =>
          prev.map((order) =>
            order.id === orderId
              ? {
                  ...order,
                  inspectionDate: date,
                  status: "scheduled",
                }
              : order
          )
        );
        toast({
          title: "Scheduled Locally",
          description: `Order #${orderToUpdate.orderNumber} scheduled locally only. Changes may not persist.`,
          duration: 3000,
        });
      }
    } catch (error) {
      console.error("Error scheduling order:", error);
      setOrders((prev) =>
        prev.map((order) =>
          order.id === orderId
            ? {
                ...order,
                inspectionDate: date,
                status: "scheduled",
              }
            : order
        )
      );
      toast({
        title: "Scheduled Locally",
        description: `Order #${orderToUpdate.orderNumber} scheduled locally only. API update failed.`,
        duration: 3000,
      });
    }

    setIsScheduleModalOpen(false);
  };

  const filteredOrders = orders.filter((order) => {
    const q = search.toLowerCase();
    const matchesSearch =
      order.orderNumber.toLowerCase().includes(q) ||
      order.propertyAddress.toLowerCase().includes(q) ||
      order.inspectorName.toLowerCase().includes(q) ||
      order.inspectionDate.toLowerCase().includes(q) ||
      order.status.toLowerCase().includes(q);
    return (
      matchesSearch && (statusFilter === "all" || order.status === statusFilter)
    );
  });

  const totalPages = Math.ceil(filteredOrders.length / ORDERS_PER_PAGE);
  const startIdx = (page - 1) * ORDERS_PER_PAGE;
  const endIdx = startIdx + ORDERS_PER_PAGE;
  const paginatedOrders = filteredOrders.slice(startIdx, endIdx);

  if (page > totalPages && totalPages > 0) setPage(totalPages);

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar isMobile={isMobile} />
      <div
        className={`flex-1 ${
          isMobile ? "" : "ml-[var(--sidebar-width,256px)]"
        } transition-all duration-300`}
      >
        <DashboardHeader />
        <main className="p-6">
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">
              Inspection Orders
            </h1>
            <p className="text-gray-600">
              Manage and track your inspection orders.
            </p>
          </div>
          <OrdersControlsBar
            search={search}
            setSearch={setSearch}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            layout={layout}
            setLayout={setLayout}
            handleAddClick={handleAddClick}
          />

          {isLoading ? (
            <div>
              {isMobile ? (
                Array.from({ length: 12 }).map((_, idx) => (
                  <div
                    className="bg-white rounded-lg p-4 shadow flex flex-col gap-3 animate-pulse mb-2"
                    key={idx}
                  >
                    <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
                    <div className="h-3 w-2/3 bg-gray-100 rounded"></div>
                    <div className="flex gap-2">
                      <div className="h-4 w-10 bg-violet-200 rounded"></div>
                      <div className="h-4 w-10 bg-orange-200 rounded"></div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="rounded-md border bg-white shadow p-0 overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody>
                      {Array.from({ length: 12 }).map((_, rowIdx) => (
                        <tr key={rowIdx}>
                          {Array.from({ length: 7 }).map((_, colIdx) => (
                            <td key={colIdx} className="px-6 py-4">
                              <div className="h-4 w-full bg-gray-100 rounded"></div>
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          ) : isMobile ? (
            <OrdersMobileCards
              paginatedOrders={paginatedOrders}
              isLoading={isLoading}
              statusColors={statusColors}
              handleView={(order: InspectionOrder, e?: React.MouseEvent) =>
                handleView(order, e)
              }
            />
          ) : (
            <OrdersTable
              paginatedOrders={paginatedOrders}
              isLoading={isLoading}
              statusColors={statusColors}
              handleView={(order: InspectionOrder, e?: React.MouseEvent) =>
                handleView(order, e)
              }
              handleEdit={handleEdit}
              handleSchedule={handleSchedule}
              handleChangeStatus={handleChangeStatus}
            />
          )}

          <OrdersPagination
            page={page}
            totalPages={totalPages}
            setPage={setPage}
          />
        </main>
      </div>
      <OrderDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        mode={modalMode}
        order={selectedOrder || undefined}
        anchorPoint={modalAnchorPoint}
        onSave={handleSaveOrder}
      />
      <ScheduleOrderModal
        isOpen={isScheduleModalOpen}
        onClose={() => setIsScheduleModalOpen(false)}
        order={selectedOrderForSchedule || undefined}
        onSchedule={handleScheduleConfirm}
      />
    </div>
  );
}
