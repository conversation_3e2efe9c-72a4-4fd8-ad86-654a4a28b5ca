"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const orders_controller_1 = require("./orders.controller");
const orders_service_1 = require("./orders.service");
const order_entity_1 = require("./entities/order.entity");
const property_entity_1 = require("../properties/entities/property.entity");
const inspector_entity_1 = require("../inspectors/entities/inspector.entity");
const schedule_entity_1 = require("../schedules/entities/schedule.entity");
describe('Orders Integration Tests', () => {
    let app;
    let controller;
    let service;
    let orderRepository;
    const mockOrderRepository = {
        create: jest.fn(),
        save: jest.fn(),
        find: jest.fn(),
        findOne: jest.fn(),
        update: jest.fn(),
        remove: jest.fn(),
        count: jest.fn(),
        createQueryBuilder: jest.fn(() => ({
            leftJoinAndSelect: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            andWhere: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis(),
            addOrderBy: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn(),
            getMany: jest.fn(),
        })),
        manager: {
            find: jest.fn(),
            findOne: jest.fn(),
        },
    };
    const mockPropertyRepository = {
        findOne: jest.fn(),
    };
    const mockInspectorRepository = {
        find: jest.fn(),
        findOne: jest.fn(),
        increment: jest.fn(),
    };
    const mockScheduleRepository = {
        findOne: jest.fn(),
        update: jest.fn(),
    };
    beforeEach(async () => {
        app = await testing_1.Test.createTestingModule({
            controllers: [orders_controller_1.OrdersController],
            providers: [
                orders_service_1.OrdersService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(order_entity_1.Order),
                    useValue: mockOrderRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(property_entity_1.Property),
                    useValue: mockPropertyRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(inspector_entity_1.Inspector),
                    useValue: mockInspectorRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(schedule_entity_1.Schedule),
                    useValue: mockScheduleRepository,
                },
            ],
        }).compile();
        controller = app.get(orders_controller_1.OrdersController);
        service = app.get(orders_service_1.OrdersService);
        orderRepository = app.get((0, typeorm_1.getRepositoryToken)(order_entity_1.Order));
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('Complete Order Workflow', () => {
        it('should handle complete order lifecycle', async () => {
            const user = { role: 'admin', userId: 1 };
            const createOrderDto = {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
                clientIds: [1, 2],
            };
            const mockOrder = {
                id: 1,
                orderNumber: 'ORD-2024-0001',
                status: order_entity_1.OrderStatus.PENDING,
                clientIds: [1, 2],
                assignedInspectorIds: null,
                ...createOrderDto,
            };
            mockOrderRepository.manager.find.mockResolvedValue([
                { id: 1, role: 'client' },
                { id: 2, role: 'client' },
            ]);
            mockOrderRepository.create.mockReturnValue(mockOrder);
            mockOrderRepository.save.mockResolvedValue(mockOrder);
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const createResult = await controller.create(createOrderDto, user);
            expect(createResult.order.status).toBe(order_entity_1.OrderStatus.PENDING);
            const assignData = { inspectorIds: [1, 2] };
            const assignedOrder = { ...mockOrder, assignedInspectorIds: [1, 2], status: order_entity_1.OrderStatus.ASSIGNED };
            mockInspectorRepository.find.mockResolvedValue([
                { id: 1, isActive: true, isAvailable: true },
                { id: 2, isActive: true, isAvailable: true },
            ]);
            mockOrderRepository.findOne.mockResolvedValue(assignedOrder);
            mockOrderRepository.update.mockResolvedValue({ affected: 1 });
            const assignResult = await controller.assignInspectors(1, assignData, user);
            expect(assignResult.message).toBe('Inspectors assigned successfully');
            const scheduleData = { scheduleId: 1 };
            const scheduledOrder = { ...assignedOrder, status: order_entity_1.OrderStatus.SCHEDULED };
            mockScheduleRepository.findOne.mockResolvedValue({
                id: 1,
                inspectorId: 1,
                available: true,
                date: '2024-01-15',
                startTime: '10:00',
            });
            mockOrderRepository.findOne.mockResolvedValue(scheduledOrder);
            const scheduleResult = await controller.schedule(1, scheduleData, user);
            expect(scheduleResult.message).toBe('Order scheduled successfully');
            const completionData = {
                report: 'Inspection completed successfully',
                notes: 'All systems checked',
            };
            const completedOrder = { ...scheduledOrder, status: order_entity_1.OrderStatus.COMPLETED };
            mockOrderRepository.findOne.mockResolvedValue(completedOrder);
            const completeResult = await controller.complete(1, completionData, user);
            expect(completeResult.message).toBe('Inspection completed successfully');
        });
        it('should handle order cancellation workflow', async () => {
            const user = { role: 'admin', userId: 1 };
            const orderId = 1;
            const cancelData = { reason: 'Client requested cancellation' };
            const mockOrder = {
                id: orderId,
                status: order_entity_1.OrderStatus.PENDING,
                clientIds: [1],
            };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            mockOrderRepository.update.mockResolvedValue({ affected: 1 });
            const result = await controller.cancel(orderId, cancelData, user);
            expect(result.message).toBe('Order cancelled successfully');
            expect(mockOrderRepository.update).toHaveBeenCalledWith(orderId, {
                status: order_entity_1.OrderStatus.CANCELLED,
                cancellationReason: cancelData.reason,
                cancelledAt: expect.any(Date),
            });
        });
    });
    describe('Multi-Client Management', () => {
        it('should handle adding and removing clients', async () => {
            const user = { role: 'admin', userId: 1 };
            const orderId = 1;
            const mockOrder = {
                id: orderId,
                clientIds: [1],
            };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const addClientData = { clientId: 2 };
            mockOrderRepository.manager.findOne.mockResolvedValue({ id: 2, role: 'client' });
            mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1, 2] });
            const addResult = await controller.addClient(orderId, addClientData, user);
            expect(addResult.message).toBe('Client added to order successfully');
            mockOrder.clientIds = [1, 2];
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1] });
            const removeResult = await controller.removeClient(orderId, 2, user);
            expect(removeResult.message).toBe('Client removed from order successfully');
            const updateData = { clientIds: [1, 3, 4] };
            mockOrderRepository.manager.find.mockResolvedValue([
                { id: 1, role: 'client' },
                { id: 3, role: 'client' },
                { id: 4, role: 'client' },
            ]);
            mockOrderRepository.save.mockResolvedValue({ ...mockOrder, clientIds: [1, 3, 4] });
            const updateResult = await controller.updateClients(orderId, updateData, user);
            expect(updateResult.message).toBe('Order clients updated successfully');
        });
        it('should query orders for multiple clients', async () => {
            const query = { clientIds: '1,2,3', page: 1, limit: 10 };
            const expectedResult = {
                orders: [
                    { id: 1, clientIds: [1, 2] },
                    { id: 2, clientIds: [2, 3] },
                ],
                pagination: { page: 1, limit: 10, total: 2, totalPages: 1 },
            };
            const mockQueryBuilder = {
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([expectedResult.orders, 2]),
            };
            mockOrderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            const result = await controller.getOrdersForClients(query);
            expect(result.orders).toHaveLength(2);
            expect(mockQueryBuilder.where).toHaveBeenCalledWith('order.clientIds && :clientIds', {
                clientIds: [1, 2, 3],
            });
        });
    });
    describe('Permission-based Access', () => {
        it('should allow client to view only their orders', async () => {
            const clientUser = { role: 'client', userId: 1 };
            const query = { page: 1, limit: 10 };
            const mockQueryBuilder = {
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([
                    [{ id: 1, clientIds: [1] }],
                    1,
                ]),
            };
            mockOrderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            await controller.findAll(query, clientUser);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(':userId = ANY(order.clientIds)', { userId: 1 });
        });
        it('should allow inspector to view only assigned orders', async () => {
            const inspectorUser = { role: 'inspector', userId: 1 };
            const query = { page: 1, limit: 10 };
            const mockQueryBuilder = {
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([
                    [{ id: 1, assignedInspectorIds: [1] }],
                    1,
                ]),
            };
            mockOrderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            await controller.findAll(query, inspectorUser);
            expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(':userId = ANY(order.assignedInspectorIds)', { userId: 1 });
        });
        it('should allow admin to view all orders', async () => {
            const adminUser = { role: 'admin', userId: 1 };
            const query = { page: 1, limit: 10 };
            const mockQueryBuilder = {
                leftJoinAndSelect: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                take: jest.fn().mockReturnThis(),
                getManyAndCount: jest.fn().mockResolvedValue([
                    [
                        { id: 1, clientIds: [1] },
                        { id: 2, clientIds: [2] },
                    ],
                    2,
                ]),
            };
            mockOrderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
            const result = await controller.findAll(query, adminUser);
            expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(expect.stringContaining('userId'), expect.any(Object));
        });
    });
    describe('Error Handling', () => {
        it('should handle repository errors gracefully', async () => {
            const user = { role: 'admin', userId: 1 };
            const createOrderDto = {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
            };
            mockOrderRepository.create.mockImplementation(() => {
                throw new Error('Database connection failed');
            });
            await expect(controller.create(createOrderDto, user)).rejects.toThrow('Database connection failed');
        });
        it('should validate business rules', async () => {
            const user = { role: 'admin', userId: 1 };
            const orderId = 1;
            const mockOrder = {
                id: orderId,
                status: order_entity_1.OrderStatus.COMPLETED,
                clientIds: [1],
            };
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            await expect(controller.assignInspectors(orderId, { inspectorIds: [1] }, user)).rejects.toThrow('Can only assign inspectors to pending orders');
        });
    });
    describe('Data Consistency', () => {
        it('should maintain data consistency across operations', async () => {
            const user = { role: 'admin', userId: 1 };
            const orderId = 1;
            jest.spyOn(service, 'generateOrderNumber').mockResolvedValue('ORD-2024-0001');
            const createOrderDto = {
                propertyType: 'residential',
                addressLine1: '123 Main St',
                city: 'New York',
                state: 'NY',
                zipCode: '10001',
            };
            const mockOrder = {
                id: orderId,
                orderNumber: 'ORD-2024-0001',
                status: order_entity_1.OrderStatus.PENDING,
                clientIds: [user.userId],
                ...createOrderDto,
            };
            mockOrderRepository.create.mockReturnValue(mockOrder);
            mockOrderRepository.save.mockResolvedValue(mockOrder);
            mockOrderRepository.findOne.mockResolvedValue(mockOrder);
            const result = await controller.create(createOrderDto, user);
            expect(result.order.orderNumber).toBe('ORD-2024-0001');
            expect(result.order.clientIds).toContain(user.userId);
            expect(mockOrderRepository.save).toHaveBeenCalledWith(expect.objectContaining({
                orderNumber: 'ORD-2024-0001',
                status: order_entity_1.OrderStatus.PENDING,
            }));
        });
    });
});
//# sourceMappingURL=orders.integration.spec.js.map