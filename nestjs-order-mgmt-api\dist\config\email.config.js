"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailConfig = void 0;
const emailConfig = () => ({
    smtp: {
        host: process.env.EMAIL_SMTP_HOST,
        port: parseInt(process.env.EMAIL_SMTP_PORT) || 587,
        secure: false,
        auth: {
            user: process.env.EMAIL_USERNAME,
            pass: process.env.EMAIL_PASSWORD,
        },
    },
    from: {
        name: process.env.EMAIL_FROM_NAME || 'Inspection System',
        address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
    },
});
exports.emailConfig = emailConfig;
//# sourceMappingURL=email.config.js.map