import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsEnum,
  IsObject,
  IsNotEmpty,
  IsArray,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PropertyType } from '../entities/order.entity';

export class CreateOrderDto {
  // Client Information
  @ApiProperty({ description: 'Client full name', example: '<PERSON>' })
  @IsString()
  @IsNotEmpty()
  clientName: string;

  @ApiProperty({ description: 'Client email', example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  clientEmail: string;

  @ApiProperty({ description: 'Client phone', example: '+1234567890', required: false })
  @IsString()
  @IsOptional()
  clientPhone?: string;

  // Property Information
  @ApiProperty({ description: 'Property address line 1', example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @ApiProperty({ description: 'City', example: 'New York' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ description: 'Zip code', example: '10001' })
  @IsString()
  @IsNotEmpty()
  zipCode: string;

  @ApiProperty({ description: 'State', example: 'NY' })
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty({ 
    description: 'Property type', 
    enum: PropertyType, 
    required: false 
  })
  @IsEnum(PropertyType)
  @IsOptional()
  propertyType?: PropertyType;

  @ApiProperty({ description: 'Year built', example: 2020, required: false })
  @IsNumber()
  @IsOptional()
  @Min(1800)
  @Max(new Date().getFullYear() + 1)
  @Type(() => Number)
  yearBuilt?: number;

  @ApiProperty({ description: 'Foundation type', example: 'Concrete', required: false })
  @IsString()
  @IsOptional()
  foundationType?: string;

  // Access Information
  @ApiProperty({ description: 'Gate code', required: false })
  @IsString()
  @IsOptional()
  gateCode?: string;

  @ApiProperty({ description: 'Lockbox code', required: false })
  @IsString()
  @IsOptional()
  lockboxCode?: string;

  @ApiProperty({ description: 'Alarm code', required: false })
  @IsString()
  @IsOptional()
  alarmCode?: string;

  @ApiProperty({ description: 'MLS number', required: false })
  @IsString()
  @IsOptional()
  mlsNumber?: string;

  @ApiProperty({ description: 'Additional notes', required: false })
  @IsString()
  @IsOptional()
  note?: string;

  // Property Conditions
  @ApiProperty({ description: 'Is client attending', default: false })
  @IsBoolean()
  @IsOptional()
  isClientAttending?: boolean = false;

  @ApiProperty({ description: 'Is property occupied', default: false })
  @IsBoolean()
  @IsOptional()
  isOccupied?: boolean = false;

  @ApiProperty({ description: 'Has utilities', default: false })
  @IsBoolean()
  @IsOptional()
  hasUtilities?: boolean = false;

  @ApiProperty({ description: 'Has alarm system', default: false })
  @IsBoolean()
  @IsOptional()
  hasAlarm?: boolean = false;

  // Services
  @ApiProperty({ 
    description: 'Additional services', 
    example: { flexfund: true, mold: false },
    required: false 
  })
  @IsObject()
  @IsOptional()
  services?: { [key: string]: boolean };

  // Agent Information
  @ApiProperty({ description: 'Agent name', required: false })
  @IsString()
  @IsOptional()
  agentName?: string;

  @ApiProperty({ description: 'Agent email', required: false })
  @IsEmail()
  @IsOptional()
  agentEmail?: string;

  @ApiProperty({ description: 'Agent phone', required: false })
  @IsString()
  @IsOptional()
  agentPhone?: string;

  @ApiProperty({ description: 'Is seller agent', default: false })
  @IsBoolean()
  @IsOptional()
  isSeller?: boolean = false;

  @ApiProperty({ description: 'Is buyer agent', default: false })
  @IsBoolean()
  @IsOptional()
  isBuyer?: boolean = false;

  // Pricing
  @ApiProperty({ description: 'Inspection fee', example: 500.00 })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  inspectionFee: number;

  @ApiProperty({ description: 'Third party fee', default: 0, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  thirdPartyFee?: number = 0;

  @ApiProperty({ description: 'Discount fee', default: 0, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  discountFee?: number = 0;

  @ApiProperty({ description: 'Processing fee', default: 0, required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Type(() => Number)
  processingFee?: number = 0;

  // Assignment and Scheduling
  @ApiProperty({
    description: 'Assigned inspector IDs',
    type: [Number],
    example: [1, 2],
    required: false
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  assignedInspectorIds?: number[];

  @ApiProperty({ description: 'Property ID if linking to existing property', required: false })
  @IsNumber()
  @IsOptional()
  propertyId?: number;

  @ApiProperty({
    description: 'Client IDs if admin is creating order for multiple clients',
    type: [Number],
    example: [1, 2],
    required: false
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  clientIds?: number[];

  // Legacy support for single client
  @ApiProperty({ description: 'Single client ID (legacy)', required: false })
  @IsNumber()
  @IsOptional()
  clientId?: number;
}
