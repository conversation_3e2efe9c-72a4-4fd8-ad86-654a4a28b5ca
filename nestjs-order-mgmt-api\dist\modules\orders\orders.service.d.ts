import { Repository } from 'typeorm';
import { Order } from './entities/order.entity';
import { Property } from '../properties/entities/property.entity';
import { Inspector } from '../inspectors/entities/inspector.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
export declare class OrdersService {
    private readonly orderRepository;
    private readonly propertyRepository;
    private readonly inspectorRepository;
    private readonly scheduleRepository;
    constructor(orderRepository: Repository<Order>, propertyRepository: Repository<Property>, inspectorRepository: Repository<Inspector>, scheduleRepository: Repository<Schedule>);
    create(createOrderDto: CreateOrderDto, user: any): Promise<{
        order: Order;
        message: string;
    }>;
    findAll(query: OrderQueryDto, user: any): Promise<{
        orders: Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number, user: any): Promise<Order>;
    update(id: number, updateOrderDto: UpdateOrderDto, user: any): Promise<{
        order: Order;
        message: string;
    }>;
    remove(id: number, user: any): Promise<{
        message: string;
    }>;
    assignInspectors(id: number, inspectorIds: number[], user: any): Promise<{
        message: string;
        order: Order;
    }>;
    assignInspector(id: number, inspectorId: number, user: any): Promise<{
        message: string;
        order: Order;
    }>;
    scheduleInspection(id: number, scheduleData: any, user: any): Promise<{
        message: string;
        order: Order;
    }>;
    completeInspection(id: number, completionData: any, user: any): Promise<{
        message: string;
        order: Order;
    }>;
    cancelOrder(id: number, reason: string, user: any): Promise<{
        message: string;
        order: Order;
    }>;
    getOrderStats(): Promise<{
        totalOrders: number;
        ordersByStatus: any[];
        recentOrders: Order[];
    }>;
    getClientOrders(clientId: number, query: any): Promise<{
        orders: Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getOrdersForClients(clientIds: number[], query: any): Promise<{
        orders: Order[];
        pagination: {
            page: any;
            limit: any;
            total: number;
            totalPages: number;
        };
    }>;
    getInspectorOrders(inspectorId: number, query: any): Promise<{
        orders: Order[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getOrdersByInspector(inspectorId: number): Promise<Order[]>;
    addClientToOrder(orderId: number, clientId: number, user: any): Promise<{
        message: string;
        order: Order;
    }>;
    removeClientFromOrder(orderId: number, clientId: number, user: any): Promise<{
        message: string;
        order: Order;
    }>;
    updateOrderClients(orderId: number, clientIds: number[], user: any): Promise<{
        message: string;
        order: Order;
    }>;
    private generateOrderNumber;
    private validateStatusTransition;
}
