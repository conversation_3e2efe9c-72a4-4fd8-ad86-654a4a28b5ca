import { Inspector } from '../../inspectors/entities/inspector.entity';
import { Order } from '../../orders/entities/order.entity';
export declare class Schedule {
    id: number;
    inspectorId: number;
    date: string;
    startTime: string;
    endTime: string;
    available: boolean;
    inspectionOrderId: number;
    notes: string;
    isRecurring: boolean;
    recurringPattern: {
        frequency?: 'daily' | 'weekly' | 'monthly';
        interval?: number;
        daysOfWeek?: number[];
        endDate?: string;
    };
    createdAt: Date;
    updatedAt: Date;
    inspector: Inspector;
    order: Order;
}
