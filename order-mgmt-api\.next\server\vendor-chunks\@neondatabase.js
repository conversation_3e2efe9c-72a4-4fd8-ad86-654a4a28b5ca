"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@neondatabase";
exports.ids = ["vendor-chunks/@neondatabase"];
exports.modules = {

/***/ "(rsc)/./node_modules/@neondatabase/serverless/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@neondatabase/serverless/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ ut),\n/* harmony export */   DatabaseError: () => (/* binding */ export_DatabaseError),\n/* harmony export */   NeonDbError: () => (/* binding */ we),\n/* harmony export */   NeonQueryPromise: () => (/* binding */ Ae),\n/* harmony export */   Pool: () => (/* binding */ Ln),\n/* harmony export */   SqlTemplate: () => (/* binding */ $e),\n/* harmony export */   UnsafeRawSql: () => (/* binding */ Ge),\n/* harmony export */   _bundleExt: () => (/* binding */ _p),\n/* harmony export */   defaults: () => (/* binding */ export_defaults),\n/* harmony export */   neon: () => (/* binding */ as),\n/* harmony export */   neonConfig: () => (/* binding */ ge),\n/* harmony export */   types: () => (/* binding */ export_types)\n/* harmony export */ });\nvar vo=Object.create;var Te=Object.defineProperty;var xo=Object.getOwnPropertyDescriptor;var So=Object.getOwnPropertyNames;var Eo=Object.getPrototypeOf,Ao=Object.prototype.hasOwnProperty;var Co=(r,e,t)=>e in r?Te(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var a=(r,e)=>Te(r,\"name\",{value:e,configurable:!0});var z=(r,e)=>()=>(r&&(e=r(r=0)),e);var I=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ne=(r,e)=>{for(var t in e)Te(r,t,{get:e[t],\nenumerable:!0})},Mn=(r,e,t,n)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let i of So(e))!Ao.\ncall(r,i)&&i!==t&&Te(r,i,{get:()=>e[i],enumerable:!(n=xo(e,i))||n.enumerable});return r};var xe=(r,e,t)=>(t=r!=null?vo(Eo(r)):{},Mn(e||!r||!r.__esModule?Te(t,\"default\",{value:r,enumerable:!0}):\nt,r)),D=r=>Mn(Te({},\"__esModule\",{value:!0}),r);var E=(r,e,t)=>Co(r,typeof e!=\"symbol\"?e+\"\":e,t);var On=I(ct=>{\"use strict\";p();ct.byteLength=To;ct.toByteArray=Po;ct.fromByteArray=Lo;var ue=[],ee=[],\n_o=typeof Uint8Array<\"u\"?Uint8Array:Array,Ut=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz01\\\n23456789+/\";for(Se=0,Un=Ut.length;Se<Un;++Se)ue[Se]=Ut[Se],ee[Ut.charCodeAt(Se)]=Se;var Se,Un;ee[45]=\n62;ee[95]=63;function Dn(r){var e=r.length;if(e%4>0)throw new Error(\"Invalid string. Length must be \\\na multiple of 4\");var t=r.indexOf(\"=\");t===-1&&(t=e);var n=t===e?0:4-t%4;return[t,n]}a(Dn,\"getLens\");\nfunction To(r){var e=Dn(r),t=e[0],n=e[1];return(t+n)*3/4-n}a(To,\"byteLength\");function Io(r,e,t){return(e+\nt)*3/4-t}a(Io,\"_byteLength\");function Po(r){var e,t=Dn(r),n=t[0],i=t[1],s=new _o(Io(r,n,i)),o=0,u=i>\n0?n-4:n,c;for(c=0;c<u;c+=4)e=ee[r.charCodeAt(c)]<<18|ee[r.charCodeAt(c+1)]<<12|ee[r.charCodeAt(c+2)]<<\n6|ee[r.charCodeAt(c+3)],s[o++]=e>>16&255,s[o++]=e>>8&255,s[o++]=e&255;return i===2&&(e=ee[r.charCodeAt(\nc)]<<2|ee[r.charCodeAt(c+1)]>>4,s[o++]=e&255),i===1&&(e=ee[r.charCodeAt(c)]<<10|ee[r.charCodeAt(c+1)]<<\n4|ee[r.charCodeAt(c+2)]>>2,s[o++]=e>>8&255,s[o++]=e&255),s}a(Po,\"toByteArray\");function Ro(r){return ue[r>>\n18&63]+ue[r>>12&63]+ue[r>>6&63]+ue[r&63]}a(Ro,\"tripletToBase64\");function Bo(r,e,t){for(var n,i=[],s=e;s<\nt;s+=3)n=(r[s]<<16&16711680)+(r[s+1]<<8&65280)+(r[s+2]&255),i.push(Ro(n));return i.join(\"\")}a(Bo,\"en\\\ncodeChunk\");function Lo(r){for(var e,t=r.length,n=t%3,i=[],s=16383,o=0,u=t-n;o<u;o+=s)i.push(Bo(r,o,\no+s>u?u:o+s));return n===1?(e=r[t-1],i.push(ue[e>>2]+ue[e<<4&63]+\"==\")):n===2&&(e=(r[t-2]<<8)+r[t-1],\ni.push(ue[e>>10]+ue[e>>4&63]+ue[e<<2&63]+\"=\")),i.join(\"\")}a(Lo,\"fromByteArray\")});var qn=I(Dt=>{p();Dt.read=function(r,e,t,n,i){var s,o,u=i*8-n-1,c=(1<<u)-1,l=c>>1,f=-7,y=t?i-1:0,g=t?\n-1:1,A=r[e+y];for(y+=g,s=A&(1<<-f)-1,A>>=-f,f+=u;f>0;s=s*256+r[e+y],y+=g,f-=8);for(o=s&(1<<-f)-1,s>>=\n-f,f+=n;f>0;o=o*256+r[e+y],y+=g,f-=8);if(s===0)s=1-l;else{if(s===c)return o?NaN:(A?-1:1)*(1/0);o=o+Math.\npow(2,n),s=s-l}return(A?-1:1)*o*Math.pow(2,s-n)};Dt.write=function(r,e,t,n,i,s){var o,u,c,l=s*8-i-1,\nf=(1<<l)-1,y=f>>1,g=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,A=n?0:s-1,C=n?1:-1,Q=e<0||e===0&&1/e<0?\n1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,o=f):(o=Math.floor(Math.log(e)/Math.LN2),e*(c=\nMath.pow(2,-o))<1&&(o--,c*=2),o+y>=1?e+=g/c:e+=g*Math.pow(2,1-y),e*c>=2&&(o++,c/=2),o+y>=f?(u=0,o=f):\no+y>=1?(u=(e*c-1)*Math.pow(2,i),o=o+y):(u=e*Math.pow(2,y-1)*Math.pow(2,i),o=0));i>=8;r[t+A]=u&255,A+=\nC,u/=256,i-=8);for(o=o<<i|u,l+=i;l>0;r[t+A]=o&255,A+=C,o/=256,l-=8);r[t+A-C]|=Q*128}});var ri=I(Be=>{\"use strict\";p();var Ot=On(),Pe=qn(),Qn=typeof Symbol==\"function\"&&typeof Symbol.for==\n\"function\"?Symbol.for(\"nodejs.util.inspect.custom\"):null;Be.Buffer=h;Be.SlowBuffer=Oo;Be.INSPECT_MAX_BYTES=\n50;var lt=2147483647;Be.kMaxLength=lt;h.TYPED_ARRAY_SUPPORT=Fo();!h.TYPED_ARRAY_SUPPORT&&typeof console<\n\"u\"&&typeof console.error==\"function\"&&console.error(\"This browser lacks typed array (Uint8Array) su\\\npport which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.\");function Fo(){\ntry{let r=new Uint8Array(1),e={foo:a(function(){return 42},\"foo\")};return Object.setPrototypeOf(e,Uint8Array.\nprototype),Object.setPrototypeOf(r,e),r.foo()===42}catch{return!1}}a(Fo,\"typedArraySupport\");Object.\ndefineProperty(h.prototype,\"parent\",{enumerable:!0,get:a(function(){if(h.isBuffer(this))return this.\nbuffer},\"get\")});Object.defineProperty(h.prototype,\"offset\",{enumerable:!0,get:a(function(){if(h.isBuffer(\nthis))return this.byteOffset},\"get\")});function pe(r){if(r>lt)throw new RangeError('The value \"'+r+'\\\n\" is invalid for option \"size\"');let e=new Uint8Array(r);return Object.setPrototypeOf(e,h.prototype),\ne}a(pe,\"createBuffer\");function h(r,e,t){if(typeof r==\"number\"){if(typeof e==\"string\")throw new TypeError(\n'The \"string\" argument must be of type string. Received type number');return jt(r)}return Hn(r,e,t)}\na(h,\"Buffer\");h.poolSize=8192;function Hn(r,e,t){if(typeof r==\"string\")return Mo(r,e);if(ArrayBuffer.\nisView(r))return Uo(r);if(r==null)throw new TypeError(\"The first argument must be one of type string\\\n, Buffer, ArrayBuffer, Array, or Array-like Object. Received type \"+typeof r);if(ce(r,ArrayBuffer)||\nr&&ce(r.buffer,ArrayBuffer)||typeof SharedArrayBuffer<\"u\"&&(ce(r,SharedArrayBuffer)||r&&ce(r.buffer,\nSharedArrayBuffer)))return Qt(r,e,t);if(typeof r==\"number\")throw new TypeError('The \"value\" argument\\\n must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();if(n!=null&&n!==r)return h.\nfrom(n,e,t);let i=Do(r);if(i)return i;if(typeof Symbol<\"u\"&&Symbol.toPrimitive!=null&&typeof r[Symbol.\ntoPrimitive]==\"function\")return h.from(r[Symbol.toPrimitive](\"string\"),e,t);throw new TypeError(\"The\\\n first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Receiv\\\ned type \"+typeof r)}a(Hn,\"from\");h.from=function(r,e,t){return Hn(r,e,t)};Object.setPrototypeOf(h.prototype,\nUint8Array.prototype);Object.setPrototypeOf(h,Uint8Array);function $n(r){if(typeof r!=\"number\")throw new TypeError(\n'\"size\" argument must be of type number');if(r<0)throw new RangeError('The value \"'+r+'\" is invalid \\\nfor option \"size\"')}a($n,\"assertSize\");function ko(r,e,t){return $n(r),r<=0?pe(r):e!==void 0?typeof t==\n\"string\"?pe(r).fill(e,t):pe(r).fill(e):pe(r)}a(ko,\"alloc\");h.alloc=function(r,e,t){return ko(r,e,t)};\nfunction jt(r){return $n(r),pe(r<0?0:Wt(r)|0)}a(jt,\"allocUnsafe\");h.allocUnsafe=function(r){return jt(\nr)};h.allocUnsafeSlow=function(r){return jt(r)};function Mo(r,e){if((typeof e!=\"string\"||e===\"\")&&(e=\n\"utf8\"),!h.isEncoding(e))throw new TypeError(\"Unknown encoding: \"+e);let t=Gn(r,e)|0,n=pe(t),i=n.write(\nr,e);return i!==t&&(n=n.slice(0,i)),n}a(Mo,\"fromString\");function qt(r){let e=r.length<0?0:Wt(r.length)|\n0,t=pe(e);for(let n=0;n<e;n+=1)t[n]=r[n]&255;return t}a(qt,\"fromArrayLike\");function Uo(r){if(ce(r,Uint8Array)){\nlet e=new Uint8Array(r);return Qt(e.buffer,e.byteOffset,e.byteLength)}return qt(r)}a(Uo,\"fromArrayVi\\\new\");function Qt(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('\"offset\" is outside of buffer b\\\nounds');if(r.byteLength<e+(t||0))throw new RangeError('\"length\" is outside of buffer bounds');let n;\nreturn e===void 0&&t===void 0?n=new Uint8Array(r):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(\nr,e,t),Object.setPrototypeOf(n,h.prototype),n}a(Qt,\"fromArrayBuffer\");function Do(r){if(h.isBuffer(r)){\nlet e=Wt(r.length)|0,t=pe(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)return typeof r.\nlength!=\"number\"||$t(r.length)?pe(0):qt(r);if(r.type===\"Buffer\"&&Array.isArray(r.data))return qt(r.data)}\na(Do,\"fromObject\");function Wt(r){if(r>=lt)throw new RangeError(\"Attempt to allocate Buffer larger t\\\nhan maximum size: 0x\"+lt.toString(16)+\" bytes\");return r|0}a(Wt,\"checked\");function Oo(r){return+r!=\nr&&(r=0),h.alloc(+r)}a(Oo,\"SlowBuffer\");h.isBuffer=a(function(e){return e!=null&&e._isBuffer===!0&&e!==\nh.prototype},\"isBuffer\");h.compare=a(function(e,t){if(ce(e,Uint8Array)&&(e=h.from(e,e.offset,e.byteLength)),\nce(t,Uint8Array)&&(t=h.from(t,t.offset,t.byteLength)),!h.isBuffer(e)||!h.isBuffer(t))throw new TypeError(\n'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,\ni=t.length;for(let s=0,o=Math.min(n,i);s<o;++s)if(e[s]!==t[s]){n=e[s],i=t[s];break}return n<i?-1:i<n?\n1:0},\"compare\");h.isEncoding=a(function(e){switch(String(e).toLowerCase()){case\"hex\":case\"utf8\":case\"\\\nutf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"\\\nutf-16le\":return!0;default:return!1}},\"isEncoding\");h.concat=a(function(e,t){if(!Array.isArray(e))throw new TypeError(\n'\"list\" argument must be an Array of Buffers');if(e.length===0)return h.alloc(0);let n;if(t===void 0)\nfor(t=0,n=0;n<e.length;++n)t+=e[n].length;let i=h.allocUnsafe(t),s=0;for(n=0;n<e.length;++n){let o=e[n];\nif(ce(o,Uint8Array))s+o.length>i.length?(h.isBuffer(o)||(o=h.from(o)),o.copy(i,s)):Uint8Array.prototype.\nset.call(i,o,s);else if(h.isBuffer(o))o.copy(i,s);else throw new TypeError('\"list\" argument must be \\\nan Array of Buffers');s+=o.length}return i},\"concat\");function Gn(r,e){if(h.isBuffer(r))return r.length;\nif(ArrayBuffer.isView(r)||ce(r,ArrayBuffer))return r.byteLength;if(typeof r!=\"string\")throw new TypeError(\n'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof r);\nlet t=r.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&t===0)return 0;let i=!1;for(;;)switch(e){case\"\\\nascii\":case\"latin1\":case\"binary\":return t;case\"utf8\":case\"utf-8\":return Nt(r).length;case\"ucs2\":case\"\\\nucs-2\":case\"utf16le\":case\"utf-16le\":return t*2;case\"hex\":return t>>>1;case\"base64\":return ti(r).length;default:\nif(i)return n?-1:Nt(r).length;e=(\"\"+e).toLowerCase(),i=!0}}a(Gn,\"byteLength\");h.byteLength=Gn;function qo(r,e,t){\nlet n=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=\n0)||(t>>>=0,e>>>=0,t<=e))return\"\";for(r||(r=\"utf8\");;)switch(r){case\"hex\":return Ko(this,e,t);case\"u\\\ntf8\":case\"utf-8\":return zn(this,e,t);case\"ascii\":return Vo(this,e,t);case\"latin1\":case\"binary\":return zo(\nthis,e,t);case\"base64\":return $o(this,e,t);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return Yo(\nthis,e,t);default:if(n)throw new TypeError(\"Unknown encoding: \"+r);r=(r+\"\").toLowerCase(),n=!0}}a(qo,\n\"slowToString\");h.prototype._isBuffer=!0;function Ee(r,e,t){let n=r[e];r[e]=r[t],r[t]=n}a(Ee,\"swap\");\nh.prototype.swap16=a(function(){let e=this.length;if(e%2!==0)throw new RangeError(\"Buffer size must \\\nbe a multiple of 16-bits\");for(let t=0;t<e;t+=2)Ee(this,t,t+1);return this},\"swap16\");h.prototype.swap32=\na(function(){let e=this.length;if(e%4!==0)throw new RangeError(\"Buffer size must be a multiple of 32\\\n-bits\");for(let t=0;t<e;t+=4)Ee(this,t,t+3),Ee(this,t+1,t+2);return this},\"swap32\");h.prototype.swap64=\na(function(){let e=this.length;if(e%8!==0)throw new RangeError(\"Buffer size must be a multiple of 64\\\n-bits\");for(let t=0;t<e;t+=8)Ee(this,t,t+7),Ee(this,t+1,t+6),Ee(this,t+2,t+5),Ee(this,t+3,t+4);return this},\n\"swap64\");h.prototype.toString=a(function(){let e=this.length;return e===0?\"\":arguments.length===0?zn(\nthis,0,e):qo.apply(this,arguments)},\"toString\");h.prototype.toLocaleString=h.prototype.toString;h.prototype.\nequals=a(function(e){if(!h.isBuffer(e))throw new TypeError(\"Argument must be a Buffer\");return this===\ne?!0:h.compare(this,e)===0},\"equals\");h.prototype.inspect=a(function(){let e=\"\",t=Be.INSPECT_MAX_BYTES;\nreturn e=this.toString(\"hex\",0,t).replace(/(.{2})/g,\"$1 \").trim(),this.length>t&&(e+=\" ... \"),\"<Buff\\\ner \"+e+\">\"},\"inspect\");Qn&&(h.prototype[Qn]=h.prototype.inspect);h.prototype.compare=a(function(e,t,n,i,s){\nif(ce(e,Uint8Array)&&(e=h.from(e,e.offset,e.byteLength)),!h.isBuffer(e))throw new TypeError('The \"ta\\\nrget\" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=\n0),n===void 0&&(n=e?e.length:0),i===void 0&&(i=0),s===void 0&&(s=this.length),t<0||n>e.length||i<0||\ns>this.length)throw new RangeError(\"out of range index\");if(i>=s&&t>=n)return 0;if(i>=s)return-1;if(t>=\nn)return 1;if(t>>>=0,n>>>=0,i>>>=0,s>>>=0,this===e)return 0;let o=s-i,u=n-t,c=Math.min(o,u),l=this.slice(\ni,s),f=e.slice(t,n);for(let y=0;y<c;++y)if(l[y]!==f[y]){o=l[y],u=f[y];break}return o<u?-1:u<o?1:0},\"\\\ncompare\");function Vn(r,e,t,n,i){if(r.length===0)return-1;if(typeof t==\"string\"?(n=t,t=0):t>2147483647?\nt=2147483647:t<-2147483648&&(t=-2147483648),t=+t,$t(t)&&(t=i?0:r.length-1),t<0&&(t=r.length+t),t>=r.\nlength){if(i)return-1;t=r.length-1}else if(t<0)if(i)t=0;else return-1;if(typeof e==\"string\"&&(e=h.from(\ne,n)),h.isBuffer(e))return e.length===0?-1:Nn(r,e,t,n,i);if(typeof e==\"number\")return e=e&255,typeof Uint8Array.\nprototype.indexOf==\"function\"?i?Uint8Array.prototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.\ncall(r,e,t):Nn(r,[e],t,n,i);throw new TypeError(\"val must be string, number or Buffer\")}a(Vn,\"bidire\\\nctionalIndexOf\");function Nn(r,e,t,n,i){let s=1,o=r.length,u=e.length;if(n!==void 0&&(n=String(n).toLowerCase(),\nn===\"ucs2\"||n===\"ucs-2\"||n===\"utf16le\"||n===\"utf-16le\")){if(r.length<2||e.length<2)return-1;s=2,o/=2,\nu/=2,t/=2}function c(f,y){return s===1?f[y]:f.readUInt16BE(y*s)}a(c,\"read\");let l;if(i){let f=-1;for(l=\nt;l<o;l++)if(c(r,l)===c(e,f===-1?0:l-f)){if(f===-1&&(f=l),l-f+1===u)return f*s}else f!==-1&&(l-=l-f),\nf=-1}else for(t+u>o&&(t=o-u),l=t;l>=0;l--){let f=!0;for(let y=0;y<u;y++)if(c(r,l+y)!==c(e,y)){f=!1;break}\nif(f)return l}return-1}a(Nn,\"arrayIndexOf\");h.prototype.includes=a(function(e,t,n){return this.indexOf(\ne,t,n)!==-1},\"includes\");h.prototype.indexOf=a(function(e,t,n){return Vn(this,e,t,n,!0)},\"indexOf\");\nh.prototype.lastIndexOf=a(function(e,t,n){return Vn(this,e,t,n,!1)},\"lastIndexOf\");function Qo(r,e,t,n){\nt=Number(t)||0;let i=r.length-t;n?(n=Number(n),n>i&&(n=i)):n=i;let s=e.length;n>s/2&&(n=s/2);let o;for(o=\n0;o<n;++o){let u=parseInt(e.substr(o*2,2),16);if($t(u))return o;r[t+o]=u}return o}a(Qo,\"hexWrite\");function No(r,e,t,n){\nreturn ft(Nt(e,r.length-t),r,t,n)}a(No,\"utf8Write\");function jo(r,e,t,n){return ft(ea(e),r,t,n)}a(jo,\n\"asciiWrite\");function Wo(r,e,t,n){return ft(ti(e),r,t,n)}a(Wo,\"base64Write\");function Ho(r,e,t,n){return ft(\nta(e,r.length-t),r,t,n)}a(Ho,\"ucs2Write\");h.prototype.write=a(function(e,t,n,i){if(t===void 0)i=\"utf\\\n8\",n=this.length,t=0;else if(n===void 0&&typeof t==\"string\")i=t,n=this.length,t=0;else if(isFinite(t))\nt=t>>>0,isFinite(n)?(n=n>>>0,i===void 0&&(i=\"utf8\")):(i=n,n=void 0);else throw new Error(\"Buffer.wri\\\nte(string, encoding, offset[, length]) is no longer supported\");let s=this.length-t;if((n===void 0||\nn>s)&&(n=s),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError(\"Attempt to write outside buf\\\nfer bounds\");i||(i=\"utf8\");let o=!1;for(;;)switch(i){case\"hex\":return Qo(this,e,t,n);case\"utf8\":case\"\\\nutf-8\":return No(this,e,t,n);case\"ascii\":case\"latin1\":case\"binary\":return jo(this,e,t,n);case\"base64\":\nreturn Wo(this,e,t,n);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return Ho(this,e,t,n);default:\nif(o)throw new TypeError(\"Unknown encoding: \"+i);i=(\"\"+i).toLowerCase(),o=!0}},\"write\");h.prototype.\ntoJSON=a(function(){return{type:\"Buffer\",data:Array.prototype.slice.call(this._arr||this,0)}},\"toJSO\\\nN\");function $o(r,e,t){return e===0&&t===r.length?Ot.fromByteArray(r):Ot.fromByteArray(r.slice(e,t))}\na($o,\"base64Slice\");function zn(r,e,t){t=Math.min(r.length,t);let n=[],i=e;for(;i<t;){let s=r[i],o=null,\nu=s>239?4:s>223?3:s>191?2:1;if(i+u<=t){let c,l,f,y;switch(u){case 1:s<128&&(o=s);break;case 2:c=r[i+\n1],(c&192)===128&&(y=(s&31)<<6|c&63,y>127&&(o=y));break;case 3:c=r[i+1],l=r[i+2],(c&192)===128&&(l&192)===\n128&&(y=(s&15)<<12|(c&63)<<6|l&63,y>2047&&(y<55296||y>57343)&&(o=y));break;case 4:c=r[i+1],l=r[i+2],\nf=r[i+3],(c&192)===128&&(l&192)===128&&(f&192)===128&&(y=(s&15)<<18|(c&63)<<12|(l&63)<<6|f&63,y>65535&&\ny<1114112&&(o=y))}}o===null?(o=65533,u=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|o&1023),\nn.push(o),i+=u}return Go(n)}a(zn,\"utf8Slice\");var jn=4096;function Go(r){let e=r.length;if(e<=jn)return String.\nfromCharCode.apply(String,r);let t=\"\",n=0;for(;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=\njn));return t}a(Go,\"decodeCodePointsArray\");function Vo(r,e,t){let n=\"\";t=Math.min(r.length,t);for(let i=e;i<\nt;++i)n+=String.fromCharCode(r[i]&127);return n}a(Vo,\"asciiSlice\");function zo(r,e,t){let n=\"\";t=Math.\nmin(r.length,t);for(let i=e;i<t;++i)n+=String.fromCharCode(r[i]);return n}a(zo,\"latin1Slice\");function Ko(r,e,t){\nlet n=r.length;(!e||e<0)&&(e=0),(!t||t<0||t>n)&&(t=n);let i=\"\";for(let s=e;s<t;++s)i+=ra[r[s]];return i}\na(Ko,\"hexSlice\");function Yo(r,e,t){let n=r.slice(e,t),i=\"\";for(let s=0;s<n.length-1;s+=2)i+=String.\nfromCharCode(n[s]+n[s+1]*256);return i}a(Yo,\"utf16leSlice\");h.prototype.slice=a(function(e,t){let n=this.\nlength;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<\ne&&(t=e);let i=this.subarray(e,t);return Object.setPrototypeOf(i,h.prototype),i},\"slice\");function O(r,e,t){\nif(r%1!==0||r<0)throw new RangeError(\"offset is not uint\");if(r+e>t)throw new RangeError(\"Trying to \\\naccess beyond buffer length\")}a(O,\"checkOffset\");h.prototype.readUintLE=h.prototype.readUIntLE=a(function(e,t,n){\ne=e>>>0,t=t>>>0,n||O(e,t,this.length);let i=this[e],s=1,o=0;for(;++o<t&&(s*=256);)i+=this[e+o]*s;return i},\n\"readUIntLE\");h.prototype.readUintBE=h.prototype.readUIntBE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||O(e,\nt,this.length);let i=this[e+--t],s=1;for(;t>0&&(s*=256);)i+=this[e+--t]*s;return i},\"readUIntBE\");h.\nprototype.readUint8=h.prototype.readUInt8=a(function(e,t){return e=e>>>0,t||O(e,1,this.length),this[e]},\n\"readUInt8\");h.prototype.readUint16LE=h.prototype.readUInt16LE=a(function(e,t){return e=e>>>0,t||O(e,\n2,this.length),this[e]|this[e+1]<<8},\"readUInt16LE\");h.prototype.readUint16BE=h.prototype.readUInt16BE=\na(function(e,t){return e=e>>>0,t||O(e,2,this.length),this[e]<<8|this[e+1]},\"readUInt16BE\");h.prototype.\nreadUint32LE=h.prototype.readUInt32LE=a(function(e,t){return e=e>>>0,t||O(e,4,this.length),(this[e]|\nthis[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216},\"readUInt32LE\");h.prototype.readUint32BE=h.prototype.\nreadUInt32BE=a(function(e,t){return e=e>>>0,t||O(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+\n2]<<8|this[e+3])},\"readUInt32BE\");h.prototype.readBigUInt64LE=ye(a(function(e){e=e>>>0,Re(e,\"offset\");\nlet t=this[e],n=this[e+7];(t===void 0||n===void 0)&&je(e,this.length-8);let i=t+this[++e]*2**8+this[++e]*\n2**16+this[++e]*2**24,s=this[++e]+this[++e]*2**8+this[++e]*2**16+n*2**24;return BigInt(i)+(BigInt(s)<<\nBigInt(32))},\"readBigUInt64LE\"));h.prototype.readBigUInt64BE=ye(a(function(e){e=e>>>0,Re(e,\"offset\");\nlet t=this[e],n=this[e+7];(t===void 0||n===void 0)&&je(e,this.length-8);let i=t*2**24+this[++e]*2**16+\nthis[++e]*2**8+this[++e],s=this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n;return(BigInt(i)<<BigInt(\n32))+BigInt(s)},\"readBigUInt64BE\"));h.prototype.readIntLE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||O(e,t,\nthis.length);let i=this[e],s=1,o=0;for(;++o<t&&(s*=256);)i+=this[e+o]*s;return s*=128,i>=s&&(i-=Math.\npow(2,8*t)),i},\"readIntLE\");h.prototype.readIntBE=a(function(e,t,n){e=e>>>0,t=t>>>0,n||O(e,t,this.length);\nlet i=t,s=1,o=this[e+--i];for(;i>0&&(s*=256);)o+=this[e+--i]*s;return s*=128,o>=s&&(o-=Math.pow(2,8*\nt)),o},\"readIntBE\");h.prototype.readInt8=a(function(e,t){return e=e>>>0,t||O(e,1,this.length),this[e]&\n128?(255-this[e]+1)*-1:this[e]},\"readInt8\");h.prototype.readInt16LE=a(function(e,t){e=e>>>0,t||O(e,2,\nthis.length);let n=this[e]|this[e+1]<<8;return n&32768?n|4294901760:n},\"readInt16LE\");h.prototype.readInt16BE=\na(function(e,t){e=e>>>0,t||O(e,2,this.length);let n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:\nn},\"readInt16BE\");h.prototype.readInt32LE=a(function(e,t){return e=e>>>0,t||O(e,4,this.length),this[e]|\nthis[e+1]<<8|this[e+2]<<16|this[e+3]<<24},\"readInt32LE\");h.prototype.readInt32BE=a(function(e,t){return e=\ne>>>0,t||O(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},\"readInt32BE\");h.prototype.\nreadBigInt64LE=ye(a(function(e){e=e>>>0,Re(e,\"offset\");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&\nje(e,this.length-8);let i=this[e+4]+this[e+5]*2**8+this[e+6]*2**16+(n<<24);return(BigInt(i)<<BigInt(\n32))+BigInt(t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24)},\"readBigInt64LE\"));h.prototype.readBigInt64BE=\nye(a(function(e){e=e>>>0,Re(e,\"offset\");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&je(e,this.\nlength-8);let i=(t<<24)+this[++e]*2**16+this[++e]*2**8+this[++e];return(BigInt(i)<<BigInt(32))+BigInt(\nthis[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n)},\"readBigInt64BE\"));h.prototype.readFloatLE=a(function(e,t){\nreturn e=e>>>0,t||O(e,4,this.length),Pe.read(this,e,!0,23,4)},\"readFloatLE\");h.prototype.readFloatBE=\na(function(e,t){return e=e>>>0,t||O(e,4,this.length),Pe.read(this,e,!1,23,4)},\"readFloatBE\");h.prototype.\nreadDoubleLE=a(function(e,t){return e=e>>>0,t||O(e,8,this.length),Pe.read(this,e,!0,52,8)},\"readDoub\\\nleLE\");h.prototype.readDoubleBE=a(function(e,t){return e=e>>>0,t||O(e,8,this.length),Pe.read(this,e,\n!1,52,8)},\"readDoubleBE\");function K(r,e,t,n,i,s){if(!h.isBuffer(r))throw new TypeError('\"buffer\" ar\\\ngument must be a Buffer instance');if(e>i||e<s)throw new RangeError('\"value\" argument is out of boun\\\nds');if(t+n>r.length)throw new RangeError(\"Index out of range\")}a(K,\"checkInt\");h.prototype.writeUintLE=\nh.prototype.writeUIntLE=a(function(e,t,n,i){if(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.pow(2,8*n)-1;K(this,\ne,t,n,u,0)}let s=1,o=0;for(this[t]=e&255;++o<n&&(s*=256);)this[t+o]=e/s&255;return t+n},\"writeUIntLE\");\nh.prototype.writeUintBE=h.prototype.writeUIntBE=a(function(e,t,n,i){if(e=+e,t=t>>>0,n=n>>>0,!i){let u=Math.\npow(2,8*n)-1;K(this,e,t,n,u,0)}let s=n-1,o=1;for(this[t+s]=e&255;--s>=0&&(o*=256);)this[t+s]=e/o&255;\nreturn t+n},\"writeUIntBE\");h.prototype.writeUint8=h.prototype.writeUInt8=a(function(e,t,n){return e=\n+e,t=t>>>0,n||K(this,e,t,1,255,0),this[t]=e&255,t+1},\"writeUInt8\");h.prototype.writeUint16LE=h.prototype.\nwriteUInt16LE=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,e,t,2,65535,0),this[t]=e&255,this[t+1]=\ne>>>8,t+2},\"writeUInt16LE\");h.prototype.writeUint16BE=h.prototype.writeUInt16BE=a(function(e,t,n){return e=\n+e,t=t>>>0,n||K(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2},\"writeUInt16BE\");h.prototype.\nwriteUint32LE=h.prototype.writeUInt32LE=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,e,t,4,4294967295,\n0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+4},\"writeUInt32LE\");h.prototype.\nwriteUint32BE=h.prototype.writeUInt32BE=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,e,t,4,4294967295,\n0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4},\"writeUInt32BE\");function Kn(r,e,t,n,i){\nei(e,n,i,r,t,7);let s=Number(e&BigInt(4294967295));r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=\ns;let o=Number(e>>BigInt(32)&BigInt(4294967295));return r[t++]=o,o=o>>8,r[t++]=o,o=o>>8,r[t++]=o,o=o>>\n8,r[t++]=o,t}a(Kn,\"wrtBigUInt64LE\");function Yn(r,e,t,n,i){ei(e,n,i,r,t,7);let s=Number(e&BigInt(4294967295));\nr[t+7]=s,s=s>>8,r[t+6]=s,s=s>>8,r[t+5]=s,s=s>>8,r[t+4]=s;let o=Number(e>>BigInt(32)&BigInt(4294967295));\nreturn r[t+3]=o,o=o>>8,r[t+2]=o,o=o>>8,r[t+1]=o,o=o>>8,r[t]=o,t+8}a(Yn,\"wrtBigUInt64BE\");h.prototype.\nwriteBigUInt64LE=ye(a(function(e,t=0){return Kn(this,e,t,BigInt(0),BigInt(\"0xffffffffffffffff\"))},\"w\\\nriteBigUInt64LE\"));h.prototype.writeBigUInt64BE=ye(a(function(e,t=0){return Yn(this,e,t,BigInt(0),BigInt(\n\"0xffffffffffffffff\"))},\"writeBigUInt64BE\"));h.prototype.writeIntLE=a(function(e,t,n,i){if(e=+e,t=t>>>\n0,!i){let c=Math.pow(2,8*n-1);K(this,e,t,n,c-1,-c)}let s=0,o=1,u=0;for(this[t]=e&255;++s<n&&(o*=256);)\ne<0&&u===0&&this[t+s-1]!==0&&(u=1),this[t+s]=(e/o>>0)-u&255;return t+n},\"writeIntLE\");h.prototype.writeIntBE=\na(function(e,t,n,i){if(e=+e,t=t>>>0,!i){let c=Math.pow(2,8*n-1);K(this,e,t,n,c-1,-c)}let s=n-1,o=1,u=0;\nfor(this[t+s]=e&255;--s>=0&&(o*=256);)e<0&&u===0&&this[t+s+1]!==0&&(u=1),this[t+s]=(e/o>>0)-u&255;return t+\nn},\"writeIntBE\");h.prototype.writeInt8=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,e,t,1,127,-128),\ne<0&&(e=255+e+1),this[t]=e&255,t+1},\"writeInt8\");h.prototype.writeInt16LE=a(function(e,t,n){return e=\n+e,t=t>>>0,n||K(this,e,t,2,32767,-32768),this[t]=e&255,this[t+1]=e>>>8,t+2},\"writeInt16LE\");h.prototype.\nwriteInt16BE=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+\n1]=e&255,t+2},\"writeInt16BE\");h.prototype.writeInt32LE=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,\ne,t,4,2147483647,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},\"\\\nwriteInt32LE\");h.prototype.writeInt32BE=a(function(e,t,n){return e=+e,t=t>>>0,n||K(this,e,t,4,2147483647,\n-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,\nt+4},\"writeInt32BE\");h.prototype.writeBigInt64LE=ye(a(function(e,t=0){return Kn(this,e,t,-BigInt(\"0x\\\n8000000000000000\"),BigInt(\"0x7fffffffffffffff\"))},\"writeBigInt64LE\"));h.prototype.writeBigInt64BE=ye(\na(function(e,t=0){return Yn(this,e,t,-BigInt(\"0x8000000000000000\"),BigInt(\"0x7fffffffffffffff\"))},\"w\\\nriteBigInt64BE\"));function Zn(r,e,t,n,i,s){if(t+n>r.length)throw new RangeError(\"Index out of range\");\nif(t<0)throw new RangeError(\"Index out of range\")}a(Zn,\"checkIEEE754\");function Jn(r,e,t,n,i){return e=\n+e,t=t>>>0,i||Zn(r,e,t,4,34028234663852886e22,-34028234663852886e22),Pe.write(r,e,t,n,23,4),t+4}a(Jn,\n\"writeFloat\");h.prototype.writeFloatLE=a(function(e,t,n){return Jn(this,e,t,!0,n)},\"writeFloatLE\");h.\nprototype.writeFloatBE=a(function(e,t,n){return Jn(this,e,t,!1,n)},\"writeFloatBE\");function Xn(r,e,t,n,i){\nreturn e=+e,t=t>>>0,i||Zn(r,e,t,8,17976931348623157e292,-17976931348623157e292),Pe.write(r,e,t,n,52,\n8),t+8}a(Xn,\"writeDouble\");h.prototype.writeDoubleLE=a(function(e,t,n){return Xn(this,e,t,!0,n)},\"wr\\\niteDoubleLE\");h.prototype.writeDoubleBE=a(function(e,t,n){return Xn(this,e,t,!1,n)},\"writeDoubleBE\");\nh.prototype.copy=a(function(e,t,n,i){if(!h.isBuffer(e))throw new TypeError(\"argument should be a Buf\\\nfer\");if(n||(n=0),!i&&i!==0&&(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<n&&(i=n),i===\nn||e.length===0||this.length===0)return 0;if(t<0)throw new RangeError(\"targetStart out of bounds\");if(n<\n0||n>=this.length)throw new RangeError(\"Index out of range\");if(i<0)throw new RangeError(\"sourceEnd \\\nout of bounds\");i>this.length&&(i=this.length),e.length-t<i-n&&(i=e.length-t+n);let s=i-n;return this===\ne&&typeof Uint8Array.prototype.copyWithin==\"function\"?this.copyWithin(t,n,i):Uint8Array.prototype.set.\ncall(e,this.subarray(n,i),t),s},\"copy\");h.prototype.fill=a(function(e,t,n,i){if(typeof e==\"string\"){\nif(typeof t==\"string\"?(i=t,t=0,n=this.length):typeof n==\"string\"&&(i=n,n=this.length),i!==void 0&&typeof i!=\n\"string\")throw new TypeError(\"encoding must be a string\");if(typeof i==\"string\"&&!h.isEncoding(i))throw new TypeError(\n\"Unknown encoding: \"+i);if(e.length===1){let o=e.charCodeAt(0);(i===\"utf8\"&&o<128||i===\"latin1\")&&(e=\no)}}else typeof e==\"number\"?e=e&255:typeof e==\"boolean\"&&(e=Number(e));if(t<0||this.length<t||this.length<\nn)throw new RangeError(\"Out of range index\");if(n<=t)return this;t=t>>>0,n=n===void 0?this.length:n>>>\n0,e||(e=0);let s;if(typeof e==\"number\")for(s=t;s<n;++s)this[s]=e;else{let o=h.isBuffer(e)?e:h.from(e,\ni),u=o.length;if(u===0)throw new TypeError('The value \"'+e+'\" is invalid for argument \"value\"');for(s=\n0;s<n-t;++s)this[s+t]=o[s%u]}return this},\"fill\");var Ie={};function Ht(r,e,t){var n;Ie[r]=(n=class extends t{constructor(){\nsuper(),Object.defineProperty(this,\"message\",{value:e.apply(this,arguments),writable:!0,configurable:!0}),\nthis.name=`${this.name} [${r}]`,this.stack,delete this.name}get code(){return r}set code(s){Object.defineProperty(\nthis,\"code\",{configurable:!0,enumerable:!0,value:s,writable:!0})}toString(){return`${this.name} [${r}\\\n]: ${this.message}`}},a(n,\"NodeError\"),n)}a(Ht,\"E\");Ht(\"ERR_BUFFER_OUT_OF_BOUNDS\",function(r){return r?\n`${r} is outside of buffer bounds`:\"Attempt to access memory outside buffer bounds\"},RangeError);Ht(\n\"ERR_INVALID_ARG_TYPE\",function(r,e){return`The \"${r}\" argument must be of type number. Received typ\\\ne ${typeof e}`},TypeError);Ht(\"ERR_OUT_OF_RANGE\",function(r,e,t){let n=`The value of \"${r}\" is out o\\\nf range.`,i=t;return Number.isInteger(t)&&Math.abs(t)>2**32?i=Wn(String(t)):typeof t==\"bigint\"&&(i=String(\nt),(t>BigInt(2)**BigInt(32)||t<-(BigInt(2)**BigInt(32)))&&(i=Wn(i)),i+=\"n\"),n+=` It must be ${e}. Re\\\nceived ${i}`,n},RangeError);function Wn(r){let e=\"\",t=r.length,n=r[0]===\"-\"?1:0;for(;t>=n+4;t-=3)e=`\\\n_${r.slice(t-3,t)}${e}`;return`${r.slice(0,t)}${e}`}a(Wn,\"addNumericalSeparator\");function Zo(r,e,t){\nRe(e,\"offset\"),(r[e]===void 0||r[e+t]===void 0)&&je(e,r.length-(t+1))}a(Zo,\"checkBounds\");function ei(r,e,t,n,i,s){\nif(r>t||r<e){let o=typeof e==\"bigint\"?\"n\":\"\",u;throw s>3?e===0||e===BigInt(0)?u=`>= 0${o} and < 2${o}\\\n ** ${(s+1)*8}${o}`:u=`>= -(2${o} ** ${(s+1)*8-1}${o}) and < 2 ** ${(s+1)*8-1}${o}`:u=`>= ${e}${o} a\\\nnd <= ${t}${o}`,new Ie.ERR_OUT_OF_RANGE(\"value\",u,r)}Zo(n,i,s)}a(ei,\"checkIntBI\");function Re(r,e){if(typeof r!=\n\"number\")throw new Ie.ERR_INVALID_ARG_TYPE(e,\"number\",r)}a(Re,\"validateNumber\");function je(r,e,t){throw Math.\nfloor(r)!==r?(Re(r,t),new Ie.ERR_OUT_OF_RANGE(t||\"offset\",\"an integer\",r)):e<0?new Ie.ERR_BUFFER_OUT_OF_BOUNDS:\nnew Ie.ERR_OUT_OF_RANGE(t||\"offset\",`>= ${t?1:0} and <= ${e}`,r)}a(je,\"boundsError\");var Jo=/[^+/0-9A-Za-z-_]/g;\nfunction Xo(r){if(r=r.split(\"=\")[0],r=r.trim().replace(Jo,\"\"),r.length<2)return\"\";for(;r.length%4!==\n0;)r=r+\"=\";return r}a(Xo,\"base64clean\");function Nt(r,e){e=e||1/0;let t,n=r.length,i=null,s=[];for(let o=0;o<\nn;++o){if(t=r.charCodeAt(o),t>55295&&t<57344){if(!i){if(t>56319){(e-=3)>-1&&s.push(239,191,189);continue}else if(o+\n1===n){(e-=3)>-1&&s.push(239,191,189);continue}i=t;continue}if(t<56320){(e-=3)>-1&&s.push(239,191,189),\ni=t;continue}t=(i-55296<<10|t-56320)+65536}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,t<128){if((e-=\n1)<0)break;s.push(t)}else if(t<2048){if((e-=2)<0)break;s.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=\n3)<0)break;s.push(t>>12|224,t>>6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;s.push(t>>18|\n240,t>>12&63|128,t>>6&63|128,t&63|128)}else throw new Error(\"Invalid code point\")}return s}a(Nt,\"utf\\\n8ToBytes\");function ea(r){let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(t)&255);return e}a(\nea,\"asciiToBytes\");function ta(r,e){let t,n,i,s=[];for(let o=0;o<r.length&&!((e-=2)<0);++o)t=r.charCodeAt(\no),n=t>>8,i=t%256,s.push(i),s.push(n);return s}a(ta,\"utf16leToBytes\");function ti(r){return Ot.toByteArray(\nXo(r))}a(ti,\"base64ToBytes\");function ft(r,e,t,n){let i;for(i=0;i<n&&!(i+t>=e.length||i>=r.length);++i)\ne[i+t]=r[i];return i}a(ft,\"blitBuffer\");function ce(r,e){return r instanceof e||r!=null&&r.constructor!=\nnull&&r.constructor.name!=null&&r.constructor.name===e.name}a(ce,\"isInstance\");function $t(r){return r!==\nr}a($t,\"numberIsNaN\");var ra=function(){let r=\"0123456789abcdef\",e=new Array(256);for(let t=0;t<16;++t){\nlet n=t*16;for(let i=0;i<16;++i)e[n+i]=r[t]+r[i]}return e}();function ye(r){return typeof BigInt>\"u\"?\nna:r}a(ye,\"defineBigIntMethod\");function na(){throw new Error(\"BigInt not supported\")}a(na,\"BufferBi\\\ngIntNotDefined\")});var w,b,v,d,m,p=z(()=>{\"use strict\";w=globalThis,b=globalThis.setImmediate??(r=>setTimeout(r,0)),v=globalThis.\nclearImmediate??(r=>clearTimeout(r)),d=typeof globalThis.Buffer==\"function\"&&typeof globalThis.Buffer.\nallocUnsafe==\"function\"?globalThis.Buffer:ri().Buffer,m=globalThis.process??{};m.env??(m.env={});try{\nm.nextTick(()=>{})}catch{let e=Promise.resolve();m.nextTick=e.then.bind(e)}});var me=I((Pl,Gt)=>{\"use strict\";p();var Le=typeof Reflect==\"object\"?Reflect:null,ni=Le&&typeof Le.apply==\n\"function\"?Le.apply:a(function(e,t,n){return Function.prototype.apply.call(e,t,n)},\"ReflectApply\"),ht;\nLe&&typeof Le.ownKeys==\"function\"?ht=Le.ownKeys:Object.getOwnPropertySymbols?ht=a(function(e){return Object.\ngetOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))},\"ReflectOwnKeys\"):ht=a(function(e){return Object.\ngetOwnPropertyNames(e)},\"ReflectOwnKeys\");function ia(r){console&&console.warn&&console.warn(r)}a(ia,\n\"ProcessEmitWarning\");var si=Number.isNaN||a(function(e){return e!==e},\"NumberIsNaN\");function R(){R.\ninit.call(this)}a(R,\"EventEmitter\");Gt.exports=R;Gt.exports.once=ua;R.EventEmitter=R;R.prototype._events=\nvoid 0;R.prototype._eventsCount=0;R.prototype._maxListeners=void 0;var ii=10;function pt(r){if(typeof r!=\n\"function\")throw new TypeError('The \"listener\" argument must be of type Function. Received type '+typeof r)}\na(pt,\"checkListener\");Object.defineProperty(R,\"defaultMaxListeners\",{enumerable:!0,get:a(function(){\nreturn ii},\"get\"),set:a(function(r){if(typeof r!=\"number\"||r<0||si(r))throw new RangeError('The valu\\\ne of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received '+r+\".\");ii=r},\n\"set\")});R.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&\n(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};\nR.prototype.setMaxListeners=a(function(e){if(typeof e!=\"number\"||e<0||si(e))throw new RangeError('Th\\\ne value of \"n\" is out of range. It must be a non-negative number. Received '+e+\".\");return this._maxListeners=\ne,this},\"setMaxListeners\");function oi(r){return r._maxListeners===void 0?R.defaultMaxListeners:r._maxListeners}\na(oi,\"_getMaxListeners\");R.prototype.getMaxListeners=a(function(){return oi(this)},\"getMaxListeners\");\nR.prototype.emit=a(function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var i=e===\n\"error\",s=this._events;if(s!==void 0)i=i&&s.error===void 0;else if(!i)return!1;if(i){var o;if(t.length>\n0&&(o=t[0]),o instanceof Error)throw o;var u=new Error(\"Unhandled error.\"+(o?\" (\"+o.message+\")\":\"\"));\nthrow u.context=o,u}var c=s[e];if(c===void 0)return!1;if(typeof c==\"function\")ni(c,this,t);else for(var l=c.\nlength,f=fi(c,l),n=0;n<l;++n)ni(f[n],this,t);return!0},\"emit\");function ai(r,e,t,n){var i,s,o;if(pt(\nt),s=r._events,s===void 0?(s=r._events=Object.create(null),r._eventsCount=0):(s.newListener!==void 0&&\n(r.emit(\"newListener\",e,t.listener?t.listener:t),s=r._events),o=s[e]),o===void 0)o=s[e]=t,++r._eventsCount;else if(typeof o==\n\"function\"?o=s[e]=n?[t,o]:[o,t]:n?o.unshift(t):o.push(t),i=oi(r),i>0&&o.length>i&&!o.warned){o.warned=\n!0;var u=new Error(\"Possible EventEmitter memory leak detected. \"+o.length+\" \"+String(e)+\" listeners\\\n added. Use emitter.setMaxListeners() to increase limit\");u.name=\"MaxListenersExceededWarning\",u.emitter=\nr,u.type=e,u.count=o.length,ia(u)}return r}a(ai,\"_addListener\");R.prototype.addListener=a(function(e,t){\nreturn ai(this,e,t,!1)},\"addListener\");R.prototype.on=R.prototype.addListener;R.prototype.prependListener=\na(function(e,t){return ai(this,e,t,!0)},\"prependListener\");function sa(){if(!this.fired)return this.\ntarget.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.\ntarget):this.listener.apply(this.target,arguments)}a(sa,\"onceWrapper\");function ui(r,e,t){var n={fired:!1,\nwrapFn:void 0,target:r,type:e,listener:t},i=sa.bind(n);return i.listener=t,n.wrapFn=i,i}a(ui,\"_onceW\\\nrap\");R.prototype.once=a(function(e,t){return pt(t),this.on(e,ui(this,e,t)),this},\"once\");R.prototype.\nprependOnceListener=a(function(e,t){return pt(t),this.prependListener(e,ui(this,e,t)),this},\"prepend\\\nOnceListener\");R.prototype.removeListener=a(function(e,t){var n,i,s,o,u;if(pt(t),i=this._events,i===\nvoid 0)return this;if(n=i[e],n===void 0)return this;if(n===t||n.listener===t)--this._eventsCount===0?\nthis._events=Object.create(null):(delete i[e],i.removeListener&&this.emit(\"removeListener\",e,n.listener||\nt));else if(typeof n!=\"function\"){for(s=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){u=n[o].\nlistener,s=o;break}if(s<0)return this;s===0?n.shift():oa(n,s),n.length===1&&(i[e]=n[0]),i.removeListener!==\nvoid 0&&this.emit(\"removeListener\",e,u||t)}return this},\"removeListener\");R.prototype.off=R.prototype.\nremoveListener;R.prototype.removeAllListeners=a(function(e){var t,n,i;if(n=this._events,n===void 0)return this;\nif(n.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=\n0):n[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[e]),this;if(arguments.\nlength===0){var s=Object.keys(n),o;for(i=0;i<s.length;++i)o=s[i],o!==\"removeListener\"&&this.removeAllListeners(\no);return this.removeAllListeners(\"removeListener\"),this._events=Object.create(null),this._eventsCount=\n0,this}if(t=n[e],typeof t==\"function\")this.removeListener(e,t);else if(t!==void 0)for(i=t.length-1;i>=\n0;i--)this.removeListener(e,t[i]);return this},\"removeAllListeners\");function ci(r,e,t){var n=r._events;\nif(n===void 0)return[];var i=n[e];return i===void 0?[]:typeof i==\"function\"?t?[i.listener||i]:[i]:t?\naa(i):fi(i,i.length)}a(ci,\"_listeners\");R.prototype.listeners=a(function(e){return ci(this,e,!0)},\"l\\\nisteners\");R.prototype.rawListeners=a(function(e){return ci(this,e,!1)},\"rawListeners\");R.listenerCount=\nfunction(r,e){return typeof r.listenerCount==\"function\"?r.listenerCount(e):li.call(r,e)};R.prototype.\nlistenerCount=li;function li(r){var e=this._events;if(e!==void 0){var t=e[r];if(typeof t==\"function\")\nreturn 1;if(t!==void 0)return t.length}return 0}a(li,\"listenerCount\");R.prototype.eventNames=a(function(){\nreturn this._eventsCount>0?ht(this._events):[]},\"eventNames\");function fi(r,e){for(var t=new Array(e),\nn=0;n<e;++n)t[n]=r[n];return t}a(fi,\"arrayClone\");function oa(r,e){for(;e+1<r.length;e++)r[e]=r[e+1];\nr.pop()}a(oa,\"spliceOne\");function aa(r){for(var e=new Array(r.length),t=0;t<e.length;++t)e[t]=r[t].\nlistener||r[t];return e}a(aa,\"unwrapListeners\");function ua(r,e){return new Promise(function(t,n){function i(o){\nr.removeListener(e,s),n(o)}a(i,\"errorListener\");function s(){typeof r.removeListener==\"function\"&&r.\nremoveListener(\"error\",i),t([].slice.call(arguments))}a(s,\"resolver\"),hi(r,e,s,{once:!0}),e!==\"error\"&&\nca(r,i,{once:!0})})}a(ua,\"once\");function ca(r,e,t){typeof r.on==\"function\"&&hi(r,\"error\",e,t)}a(ca,\n\"addErrorHandlerIfEventEmitter\");function hi(r,e,t,n){if(typeof r.on==\"function\")n.once?r.once(e,t):\nr.on(e,t);else if(typeof r.addEventListener==\"function\")r.addEventListener(e,a(function i(s){n.once&&\nr.removeEventListener(e,i),t(s)},\"wrapListener\"));else throw new TypeError('The \"emitter\" argument m\\\nust be of type EventEmitter. Received type '+typeof r)}a(hi,\"eventTargetAgnosticAddListener\")});var yi={};ne(yi,{Socket:()=>ge,isIP:()=>la});function la(r){return 0}var di,pi,S,ge,We=z(()=>{\"use s\\\ntrict\";p();di=xe(me(),1);a(la,\"isIP\");pi=/^[^.]+\\./,S=class S extends di.EventEmitter{constructor(){\nsuper(...arguments);E(this,\"opts\",{});E(this,\"connecting\",!1);E(this,\"pending\",!0);E(this,\"writable\",\n!0);E(this,\"encrypted\",!1);E(this,\"authorized\",!1);E(this,\"destroyed\",!1);E(this,\"ws\",null);E(this,\"\\\nwriteBuffer\");E(this,\"tlsState\",0);E(this,\"tlsRead\");E(this,\"tlsWrite\")}static get poolQueryViaFetch(){\nreturn S.opts.poolQueryViaFetch??S.defaults.poolQueryViaFetch}static set poolQueryViaFetch(t){S.opts.\npoolQueryViaFetch=t}static get fetchEndpoint(){return S.opts.fetchEndpoint??S.defaults.fetchEndpoint}static set fetchEndpoint(t){\nS.opts.fetchEndpoint=t}static get fetchConnectionCache(){return!0}static set fetchConnectionCache(t){\nconsole.warn(\"The `fetchConnectionCache` option is deprecated (now always `true`)\")}static get fetchFunction(){\nreturn S.opts.fetchFunction??S.defaults.fetchFunction}static set fetchFunction(t){S.opts.fetchFunction=\nt}static get webSocketConstructor(){return S.opts.webSocketConstructor??S.defaults.webSocketConstructor}static set webSocketConstructor(t){\nS.opts.webSocketConstructor=t}get webSocketConstructor(){return this.opts.webSocketConstructor??S.webSocketConstructor}set webSocketConstructor(t){\nthis.opts.webSocketConstructor=t}static get wsProxy(){return S.opts.wsProxy??S.defaults.wsProxy}static set wsProxy(t){\nS.opts.wsProxy=t}get wsProxy(){return this.opts.wsProxy??S.wsProxy}set wsProxy(t){this.opts.wsProxy=\nt}static get coalesceWrites(){return S.opts.coalesceWrites??S.defaults.coalesceWrites}static set coalesceWrites(t){\nS.opts.coalesceWrites=t}get coalesceWrites(){return this.opts.coalesceWrites??S.coalesceWrites}set coalesceWrites(t){\nthis.opts.coalesceWrites=t}static get useSecureWebSocket(){return S.opts.useSecureWebSocket??S.defaults.\nuseSecureWebSocket}static set useSecureWebSocket(t){S.opts.useSecureWebSocket=t}get useSecureWebSocket(){\nreturn this.opts.useSecureWebSocket??S.useSecureWebSocket}set useSecureWebSocket(t){this.opts.useSecureWebSocket=\nt}static get forceDisablePgSSL(){return S.opts.forceDisablePgSSL??S.defaults.forceDisablePgSSL}static set forceDisablePgSSL(t){\nS.opts.forceDisablePgSSL=t}get forceDisablePgSSL(){return this.opts.forceDisablePgSSL??S.forceDisablePgSSL}set forceDisablePgSSL(t){\nthis.opts.forceDisablePgSSL=t}static get disableSNI(){return S.opts.disableSNI??S.defaults.disableSNI}static set disableSNI(t){\nS.opts.disableSNI=t}get disableSNI(){return this.opts.disableSNI??S.disableSNI}set disableSNI(t){this.\nopts.disableSNI=t}static get pipelineConnect(){return S.opts.pipelineConnect??S.defaults.pipelineConnect}static set pipelineConnect(t){\nS.opts.pipelineConnect=t}get pipelineConnect(){return this.opts.pipelineConnect??S.pipelineConnect}set pipelineConnect(t){\nthis.opts.pipelineConnect=t}static get subtls(){return S.opts.subtls??S.defaults.subtls}static set subtls(t){\nS.opts.subtls=t}get subtls(){return this.opts.subtls??S.subtls}set subtls(t){this.opts.subtls=t}static get pipelineTLS(){\nreturn S.opts.pipelineTLS??S.defaults.pipelineTLS}static set pipelineTLS(t){S.opts.pipelineTLS=t}get pipelineTLS(){\nreturn this.opts.pipelineTLS??S.pipelineTLS}set pipelineTLS(t){this.opts.pipelineTLS=t}static get rootCerts(){\nreturn S.opts.rootCerts??S.defaults.rootCerts}static set rootCerts(t){S.opts.rootCerts=t}get rootCerts(){\nreturn this.opts.rootCerts??S.rootCerts}set rootCerts(t){this.opts.rootCerts=t}wsProxyAddrForHost(t,n){\nlet i=this.wsProxy;if(i===void 0)throw new Error(\"No WebSocket proxy is configured. Please see https\\\n://github.com/neondatabase/serverless/blob/main/CONFIG.md#wsproxy-string--host-string-port-number--s\\\ntring--string\");return typeof i==\"function\"?i(t,n):`${i}?address=${t}:${n}`}setNoDelay(){return this}setKeepAlive(){\nreturn this}ref(){return this}unref(){return this}connect(t,n,i){this.connecting=!0,i&&this.once(\"co\\\nnnect\",i);let s=a(()=>{this.connecting=!1,this.pending=!1,this.emit(\"connect\"),this.emit(\"ready\")},\"\\\nhandleWebSocketOpen\"),o=a((c,l=!1)=>{c.binaryType=\"arraybuffer\",c.addEventListener(\"error\",f=>{this.\nemit(\"error\",f),this.emit(\"close\")}),c.addEventListener(\"message\",f=>{if(this.tlsState===0){let y=d.\nfrom(f.data);this.emit(\"data\",y)}}),c.addEventListener(\"close\",()=>{this.emit(\"close\")}),l?s():c.addEventListener(\n\"open\",s)},\"configureWebSocket\"),u;try{u=this.wsProxyAddrForHost(n,typeof t==\"string\"?parseInt(t,10):\nt)}catch(c){this.emit(\"error\",c),this.emit(\"close\");return}try{let l=(this.useSecureWebSocket?\"wss:\":\n\"ws:\")+\"//\"+u;if(this.webSocketConstructor!==void 0)this.ws=new this.webSocketConstructor(l),o(this.\nws);else try{this.ws=new WebSocket(l),o(this.ws)}catch{this.ws=new __unstable_WebSocket(l),o(this.ws)}}catch(c){\nlet f=(this.useSecureWebSocket?\"https:\":\"http:\")+\"//\"+u;fetch(f,{headers:{Upgrade:\"websocket\"}}).then(\ny=>{if(this.ws=y.webSocket,this.ws==null)throw c;this.ws.accept(),o(this.ws,!0)}).catch(y=>{this.emit(\n\"error\",new Error(`All attempts to open a WebSocket to connect to the database failed. Please refer \\\nto https://github.com/neondatabase/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websoc\\\nket--undefined. Details: ${y}`)),this.emit(\"close\")})}}async startTls(t){if(this.subtls===void 0)throw new Error(\n\"For Postgres SSL connections, you must set `neonConfig.subtls` to the subtls library. See https://g\\\nithub.com/neondatabase/serverless/blob/main/CONFIG.md for more information.\");this.tlsState=1;let n=await this.\nsubtls.TrustedCert.databaseFromPEM(this.rootCerts),i=new this.subtls.WebSocketReadQueue(this.ws),s=i.\nread.bind(i),o=this.rawWrite.bind(this),{read:u,write:c}=await this.subtls.startTls(t,n,s,o,{useSNI:!this.\ndisableSNI,expectPreData:this.pipelineTLS?new Uint8Array([83]):void 0});this.tlsRead=u,this.tlsWrite=\nc,this.tlsState=2,this.encrypted=!0,this.authorized=!0,this.emit(\"secureConnection\",this),this.tlsReadLoop()}async tlsReadLoop(){\nfor(;;){let t=await this.tlsRead();if(t===void 0)break;{let n=d.from(t);this.emit(\"data\",n)}}}rawWrite(t){\nif(!this.coalesceWrites){this.ws.send(t);return}if(this.writeBuffer===void 0)this.writeBuffer=t,setTimeout(\n()=>{this.ws.send(this.writeBuffer),this.writeBuffer=void 0},0);else{let n=new Uint8Array(this.writeBuffer.\nlength+t.length);n.set(this.writeBuffer),n.set(t,this.writeBuffer.length),this.writeBuffer=n}}write(t,n=\"\\\nutf8\",i=s=>{}){return t.length===0?(i(),!0):(typeof t==\"string\"&&(t=d.from(t,n)),this.tlsState===0?(this.\nrawWrite(t),i()):this.tlsState===1?this.once(\"secureConnection\",()=>{this.write(t,n,i)}):(this.tlsWrite(\nt),i()),!0)}end(t=d.alloc(0),n=\"utf8\",i=()=>{}){return this.write(t,n,()=>{this.ws.close(),i()}),this}destroy(){\nreturn this.destroyed=!0,this.end()}};a(S,\"Socket\"),E(S,\"defaults\",{poolQueryViaFetch:!1,fetchEndpoint:a(\n(t,n,i)=>{let s;return i?.jwtAuth?s=t.replace(pi,\"apiauth.\"):s=t.replace(pi,\"api.\"),\"https://\"+s+\"/s\\\nql\"},\"fetchEndpoint\"),fetchConnectionCache:!0,fetchFunction:void 0,webSocketConstructor:void 0,wsProxy:a(\nt=>t+\"/v2\",\"wsProxy\"),useSecureWebSocket:!0,forceDisablePgSSL:!0,coalesceWrites:!0,pipelineConnect:\"\\\npassword\",subtls:void 0,rootCerts:\"\",pipelineTLS:!1,disableSNI:!1}),E(S,\"opts\",{});ge=S});var mi={};ne(mi,{parse:()=>Vt});function Vt(r,e=!1){let{protocol:t}=new URL(r),n=\"http:\"+r.substring(\nt.length),{username:i,password:s,host:o,hostname:u,port:c,pathname:l,search:f,searchParams:y,hash:g}=new URL(\nn);s=decodeURIComponent(s),i=decodeURIComponent(i),l=decodeURIComponent(l);let A=i+\":\"+s,C=e?Object.\nfromEntries(y.entries()):f;return{href:r,protocol:t,auth:A,username:i,password:s,host:o,hostname:u,port:c,\npathname:l,search:f,query:C,hash:g}}var zt=z(()=>{\"use strict\";p();a(Vt,\"parse\")});var Jt=I(Si=>{\"use strict\";p();Si.parse=function(r,e){return new Zt(r,e).parse()};var wt=class wt{constructor(e,t){\nthis.source=e,this.transform=t||Ea,this.position=0,this.entries=[],this.recorded=[],this.dimension=0}isEof(){\nreturn this.position>=this.source.length}nextCharacter(){var e=this.source[this.position++];return e===\n\"\\\\\"?{value:this.source[this.position++],escaped:!0}:{value:e,escaped:!1}}record(e){this.recorded.push(\ne)}newEntry(e){var t;(this.recorded.length>0||e)&&(t=this.recorded.join(\"\"),t===\"NULL\"&&!e&&(t=null),\nt!==null&&(t=this.transform(t)),this.entries.push(t),this.recorded=[])}consumeDimensions(){if(this.source[0]===\n\"[\")for(;!this.isEof();){var e=this.nextCharacter();if(e.value===\"=\")break}}parse(e){var t,n,i;for(this.\nconsumeDimensions();!this.isEof();)if(t=this.nextCharacter(),t.value===\"{\"&&!i)this.dimension++,this.\ndimension>1&&(n=new wt(this.source.substr(this.position-1),this.transform),this.entries.push(n.parse(\n!0)),this.position+=n.position-2);else if(t.value===\"}\"&&!i){if(this.dimension--,!this.dimension&&(this.\nnewEntry(),e))return this.entries}else t.value==='\"'&&!t.escaped?(i&&this.newEntry(!0),i=!i):t.value===\n\",\"&&!i?this.newEntry():this.record(t.value);if(this.dimension!==0)throw new Error(\"array dimension \\\nnot balanced\");return this.entries}};a(wt,\"ArrayParser\");var Zt=wt;function Ea(r){return r}a(Ea,\"ide\\\nntity\")});var Xt=I((Gl,Ei)=>{p();var Aa=Jt();Ei.exports={create:a(function(r,e){return{parse:a(function(){return Aa.\nparse(r,e)},\"parse\")}},\"create\")}});var _i=I((Kl,Ci)=>{\"use strict\";p();var Ca=/(\\d{1,})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2})(\\.\\d{1,})?.*?( BC)?$/,\n_a=/^(\\d{1,})-(\\d{2})-(\\d{2})( BC)?$/,Ta=/([Z+-])(\\d{2})?:?(\\d{2})?:?(\\d{2})?/,Ia=/^-?infinity$/;Ci.\nexports=a(function(e){if(Ia.test(e))return Number(e.replace(\"i\",\"I\"));var t=Ca.exec(e);if(!t)return Pa(\ne)||null;var n=!!t[8],i=parseInt(t[1],10);n&&(i=Ai(i));var s=parseInt(t[2],10)-1,o=t[3],u=parseInt(t[4],\n10),c=parseInt(t[5],10),l=parseInt(t[6],10),f=t[7];f=f?1e3*parseFloat(f):0;var y,g=Ra(e);return g!=null?\n(y=new Date(Date.UTC(i,s,o,u,c,l,f)),er(i)&&y.setUTCFullYear(i),g!==0&&y.setTime(y.getTime()-g)):(y=\nnew Date(i,s,o,u,c,l,f),er(i)&&y.setFullYear(i)),y},\"parseDate\");function Pa(r){var e=_a.exec(r);if(e){\nvar t=parseInt(e[1],10),n=!!e[4];n&&(t=Ai(t));var i=parseInt(e[2],10)-1,s=e[3],o=new Date(t,i,s);return er(\nt)&&o.setFullYear(t),o}}a(Pa,\"getDate\");function Ra(r){if(r.endsWith(\"+00\"))return 0;var e=Ta.exec(r.\nsplit(\" \")[1]);if(e){var t=e[1];if(t===\"Z\")return 0;var n=t===\"-\"?-1:1,i=parseInt(e[2],10)*3600+parseInt(\ne[3]||0,10)*60+parseInt(e[4]||0,10);return i*n*1e3}}a(Ra,\"timeZoneOffset\");function Ai(r){return-(r-\n1)}a(Ai,\"bcYearToNegativeYear\");function er(r){return r>=0&&r<100}a(er,\"is0To99\")});var Ii=I((Jl,Ti)=>{p();Ti.exports=La;var Ba=Object.prototype.hasOwnProperty;function La(r){for(var e=1;e<\narguments.length;e++){var t=arguments[e];for(var n in t)Ba.call(t,n)&&(r[n]=t[n])}return r}a(La,\"ext\\\nend\")});var Bi=I((tf,Ri)=>{\"use strict\";p();var Fa=Ii();Ri.exports=Fe;function Fe(r){if(!(this instanceof Fe))\nreturn new Fe(r);Fa(this,$a(r))}a(Fe,\"PostgresInterval\");var ka=[\"seconds\",\"minutes\",\"hours\",\"days\",\n\"months\",\"years\"];Fe.prototype.toPostgres=function(){var r=ka.filter(this.hasOwnProperty,this);return this.\nmilliseconds&&r.indexOf(\"seconds\")<0&&r.push(\"seconds\"),r.length===0?\"0\":r.map(function(e){var t=this[e]||\n0;return e===\"seconds\"&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/\\.?0+$/,\n\"\")),t+\" \"+e},this).join(\" \")};var Ma={years:\"Y\",months:\"M\",days:\"D\",hours:\"H\",minutes:\"M\",seconds:\"\\\nS\"},Ua=[\"years\",\"months\",\"days\"],Da=[\"hours\",\"minutes\",\"seconds\"];Fe.prototype.toISOString=Fe.prototype.\ntoISO=function(){var r=Ua.map(t,this).join(\"\"),e=Da.map(t,this).join(\"\");return\"P\"+r+\"T\"+e;function t(n){\nvar i=this[n]||0;return n===\"seconds\"&&this.milliseconds&&(i=(i+this.milliseconds/1e3).toFixed(6).replace(\n/0+$/,\"\")),i+Ma[n]}};var tr=\"([+-]?\\\\d+)\",Oa=tr+\"\\\\s+years?\",qa=tr+\"\\\\s+mons?\",Qa=tr+\"\\\\s+days?\",Na=\"\\\n([+-])?([\\\\d]*):(\\\\d\\\\d):(\\\\d\\\\d)\\\\.?(\\\\d{1,6})?\",ja=new RegExp([Oa,qa,Qa,Na].map(function(r){return\"\\\n(\"+r+\")?\"}).join(\"\\\\s*\")),Pi={years:2,months:4,days:6,hours:9,minutes:10,seconds:11,milliseconds:12},\nWa=[\"hours\",\"minutes\",\"seconds\",\"milliseconds\"];function Ha(r){var e=r+\"000000\".slice(r.length);return parseInt(\ne,10)/1e3}a(Ha,\"parseMilliseconds\");function $a(r){if(!r)return{};var e=ja.exec(r),t=e[8]===\"-\";return Object.\nkeys(Pi).reduce(function(n,i){var s=Pi[i],o=e[s];return!o||(o=i===\"milliseconds\"?Ha(o):parseInt(o,10),\n!o)||(t&&~Wa.indexOf(i)&&(o*=-1),n[i]=o),n},{})}a($a,\"parse\")});var Fi=I((sf,Li)=>{\"use strict\";p();Li.exports=a(function(e){if(/^\\\\x/.test(e))return new d(e.substr(\n2),\"hex\");for(var t=\"\",n=0;n<e.length;)if(e[n]!==\"\\\\\")t+=e[n],++n;else if(/[0-7]{3}/.test(e.substr(n+\n1,3)))t+=String.fromCharCode(parseInt(e.substr(n+1,3),8)),n+=4;else{for(var i=1;n+i<e.length&&e[n+i]===\n\"\\\\\";)i++;for(var s=0;s<Math.floor(i/2);++s)t+=\"\\\\\";n+=Math.floor(i/2)*2}return new d(t,\"binary\")},\"\\\nparseBytea\")});var Qi=I((uf,qi)=>{p();var Ve=Jt(),ze=Xt(),bt=_i(),Mi=Bi(),Ui=Fi();function vt(r){return a(function(t){\nreturn t===null?t:r(t)},\"nullAllowed\")}a(vt,\"allowNull\");function Di(r){return r===null?r:r===\"TRUE\"||\nr===\"t\"||r===\"true\"||r===\"y\"||r===\"yes\"||r===\"on\"||r===\"1\"}a(Di,\"parseBool\");function Ga(r){return r?\nVe.parse(r,Di):null}a(Ga,\"parseBoolArray\");function Va(r){return parseInt(r,10)}a(Va,\"parseBaseTenIn\\\nt\");function rr(r){return r?Ve.parse(r,vt(Va)):null}a(rr,\"parseIntegerArray\");function za(r){return r?\nVe.parse(r,vt(function(e){return Oi(e).trim()})):null}a(za,\"parseBigIntegerArray\");var Ka=a(function(r){\nif(!r)return null;var e=ze.create(r,function(t){return t!==null&&(t=or(t)),t});return e.parse()},\"pa\\\nrsePointArray\"),nr=a(function(r){if(!r)return null;var e=ze.create(r,function(t){return t!==null&&(t=\nparseFloat(t)),t});return e.parse()},\"parseFloatArray\"),te=a(function(r){if(!r)return null;var e=ze.\ncreate(r);return e.parse()},\"parseStringArray\"),ir=a(function(r){if(!r)return null;var e=ze.create(r,\nfunction(t){return t!==null&&(t=bt(t)),t});return e.parse()},\"parseDateArray\"),Ya=a(function(r){if(!r)\nreturn null;var e=ze.create(r,function(t){return t!==null&&(t=Mi(t)),t});return e.parse()},\"parseInt\\\nervalArray\"),Za=a(function(r){return r?Ve.parse(r,vt(Ui)):null},\"parseByteAArray\"),sr=a(function(r){\nreturn parseInt(r,10)},\"parseInteger\"),Oi=a(function(r){var e=String(r);return/^\\d+$/.test(e)?e:r},\"\\\nparseBigInteger\"),ki=a(function(r){return r?Ve.parse(r,vt(JSON.parse)):null},\"parseJsonArray\"),or=a(\nfunction(r){return r[0]!==\"(\"?null:(r=r.substring(1,r.length-1).split(\",\"),{x:parseFloat(r[0]),y:parseFloat(\nr[1])})},\"parsePoint\"),Ja=a(function(r){if(r[0]!==\"<\"&&r[1]!==\"(\")return null;for(var e=\"(\",t=\"\",n=!1,\ni=2;i<r.length-1;i++){if(n||(e+=r[i]),r[i]===\")\"){n=!0;continue}else if(!n)continue;r[i]!==\",\"&&(t+=\nr[i])}var s=or(e);return s.radius=parseFloat(t),s},\"parseCircle\"),Xa=a(function(r){r(20,Oi),r(21,sr),\nr(23,sr),r(26,sr),r(700,parseFloat),r(701,parseFloat),r(16,Di),r(1082,bt),r(1114,bt),r(1184,bt),r(600,\nor),r(651,te),r(718,Ja),r(1e3,Ga),r(1001,Za),r(1005,rr),r(1007,rr),r(1028,rr),r(1016,za),r(1017,Ka),\nr(1021,nr),r(1022,nr),r(1231,nr),r(1014,te),r(1015,te),r(1008,te),r(1009,te),r(1040,te),r(1041,te),r(\n1115,ir),r(1182,ir),r(1185,ir),r(1186,Mi),r(1187,Ya),r(17,Ui),r(114,JSON.parse.bind(JSON)),r(3802,JSON.\nparse.bind(JSON)),r(199,ki),r(3807,ki),r(3907,te),r(2951,te),r(791,te),r(1183,te),r(1270,te)},\"init\");\nqi.exports={init:Xa}});var ji=I((ff,Ni)=>{\"use strict\";p();var Y=1e6;function eu(r){var e=r.readInt32BE(0),t=r.readUInt32BE(\n4),n=\"\";e<0&&(e=~e+(t===0),t=~t+1>>>0,n=\"-\");var i=\"\",s,o,u,c,l,f;{if(s=e%Y,e=e/Y>>>0,o=4294967296*s+\nt,t=o/Y>>>0,u=\"\"+(o-Y*t),t===0&&e===0)return n+u+i;for(c=\"\",l=6-u.length,f=0;f<l;f++)c+=\"0\";i=c+u+i}\n{if(s=e%Y,e=e/Y>>>0,o=4294967296*s+t,t=o/Y>>>0,u=\"\"+(o-Y*t),t===0&&e===0)return n+u+i;for(c=\"\",l=6-u.\nlength,f=0;f<l;f++)c+=\"0\";i=c+u+i}{if(s=e%Y,e=e/Y>>>0,o=4294967296*s+t,t=o/Y>>>0,u=\"\"+(o-Y*t),t===0&&\ne===0)return n+u+i;for(c=\"\",l=6-u.length,f=0;f<l;f++)c+=\"0\";i=c+u+i}return s=e%Y,o=4294967296*s+t,u=\n\"\"+o%Y,n+u+i}a(eu,\"readInt8\");Ni.exports=eu});var Vi=I((df,Gi)=>{p();var tu=ji(),F=a(function(r,e,t,n,i){t=t||0,n=n||!1,i=i||function(A,C,Q){return A*\nMath.pow(2,Q)+C};var s=t>>3,o=a(function(A){return n?~A&255:A},\"inv\"),u=255,c=8-t%8;e<c&&(u=255<<8-e&\n255,c=e),t&&(u=u>>t%8);var l=0;t%8+e>=8&&(l=i(0,o(r[s])&u,c));for(var f=e+t>>3,y=s+1;y<f;y++)l=i(l,o(\nr[y]),8);var g=(e+t)%8;return g>0&&(l=i(l,o(r[f])>>8-g,g)),l},\"parseBits\"),$i=a(function(r,e,t){var n=Math.\npow(2,t-1)-1,i=F(r,1),s=F(r,t,1);if(s===0)return 0;var o=1,u=a(function(l,f,y){l===0&&(l=1);for(var g=1;g<=\ny;g++)o/=2,(f&1<<y-g)>0&&(l+=o);return l},\"parsePrecisionBits\"),c=F(r,e,t+1,!1,u);return s==Math.pow(\n2,t+1)-1?c===0?i===0?1/0:-1/0:NaN:(i===0?1:-1)*Math.pow(2,s-n)*c},\"parseFloatFromBits\"),ru=a(function(r){\nreturn F(r,1)==1?-1*(F(r,15,1,!0)+1):F(r,15,1)},\"parseInt16\"),Wi=a(function(r){return F(r,1)==1?-1*(F(\nr,31,1,!0)+1):F(r,31,1)},\"parseInt32\"),nu=a(function(r){return $i(r,23,8)},\"parseFloat32\"),iu=a(function(r){\nreturn $i(r,52,11)},\"parseFloat64\"),su=a(function(r){var e=F(r,16,32);if(e==49152)return NaN;for(var t=Math.\npow(1e4,F(r,16,16)),n=0,i=[],s=F(r,16),o=0;o<s;o++)n+=F(r,16,64+16*o)*t,t/=1e4;var u=Math.pow(10,F(r,\n16,48));return(e===0?1:-1)*Math.round(n*u)/u},\"parseNumeric\"),Hi=a(function(r,e){var t=F(e,1),n=F(e,\n63,1),i=new Date((t===0?1:-1)*n/1e3+9466848e5);return r||i.setTime(i.getTime()+i.getTimezoneOffset()*\n6e4),i.usec=n%1e3,i.getMicroSeconds=function(){return this.usec},i.setMicroSeconds=function(s){this.\nusec=s},i.getUTCMicroSeconds=function(){return this.usec},i},\"parseDate\"),Ke=a(function(r){for(var e=F(\nr,32),t=F(r,32,32),n=F(r,32,64),i=96,s=[],o=0;o<e;o++)s[o]=F(r,32,i),i+=32,i+=32;var u=a(function(l){\nvar f=F(r,32,i);if(i+=32,f==4294967295)return null;var y;if(l==23||l==20)return y=F(r,f*8,i),i+=f*8,\ny;if(l==25)return y=r.toString(this.encoding,i>>3,(i+=f<<3)>>3),y;console.log(\"ERROR: ElementType no\\\nt implemented: \"+l)},\"parseElement\"),c=a(function(l,f){var y=[],g;if(l.length>1){var A=l.shift();for(g=\n0;g<A;g++)y[g]=c(l,f);l.unshift(A)}else for(g=0;g<l[0];g++)y[g]=u(f);return y},\"parse\");return c(s,n)},\n\"parseArray\"),ou=a(function(r){return r.toString(\"utf8\")},\"parseText\"),au=a(function(r){return r===null?\nnull:F(r,8)>0},\"parseBool\"),uu=a(function(r){r(20,tu),r(21,ru),r(23,Wi),r(26,Wi),r(1700,su),r(700,nu),\nr(701,iu),r(16,au),r(1114,Hi.bind(null,!1)),r(1184,Hi.bind(null,!0)),r(1e3,Ke),r(1007,Ke),r(1016,Ke),\nr(1008,Ke),r(1009,Ke),r(25,ou)},\"init\");Gi.exports={init:uu}});var Ki=I((gf,zi)=>{p();zi.exports={BOOL:16,BYTEA:17,CHAR:18,INT8:20,INT2:21,INT4:23,REGPROC:24,TEXT:25,\nOID:26,TID:27,XID:28,CID:29,JSON:114,XML:142,PG_NODE_TREE:194,SMGR:210,PATH:602,POLYGON:604,CIDR:650,\nFLOAT4:700,FLOAT8:701,ABSTIME:702,RELTIME:703,TINTERVAL:704,CIRCLE:718,MACADDR8:774,MONEY:790,MACADDR:829,\nINET:869,ACLITEM:1033,BPCHAR:1042,VARCHAR:1043,DATE:1082,TIME:1083,TIMESTAMP:1114,TIMESTAMPTZ:1184,INTERVAL:1186,\nTIMETZ:1266,BIT:1560,VARBIT:1562,NUMERIC:1700,REFCURSOR:1790,REGPROCEDURE:2202,REGOPER:2203,REGOPERATOR:2204,\nREGCLASS:2205,REGTYPE:2206,UUID:2950,TXID_SNAPSHOT:2970,PG_LSN:3220,PG_NDISTINCT:3361,PG_DEPENDENCIES:3402,\nTSVECTOR:3614,TSQUERY:3615,GTSVECTOR:3642,REGCONFIG:3734,REGDICTIONARY:3769,JSONB:3802,REGNAMESPACE:4089,\nREGROLE:4096}});var Je=I(Ze=>{p();var cu=Qi(),lu=Vi(),fu=Xt(),hu=Ki();Ze.getTypeParser=pu;Ze.setTypeParser=du;Ze.arrayParser=\nfu;Ze.builtins=hu;var Ye={text:{},binary:{}};function Yi(r){return String(r)}a(Yi,\"noParse\");function pu(r,e){\nreturn e=e||\"text\",Ye[e]&&Ye[e][r]||Yi}a(pu,\"getTypeParser\");function du(r,e,t){typeof e==\"function\"&&\n(t=e,e=\"text\"),Ye[e][r]=t}a(du,\"setTypeParser\");cu.init(function(r,e){Ye.text[r]=e});lu.init(function(r,e){\nYe.binary[r]=e})});var St=I((Sf,Zi)=>{\"use strict\";p();var yu=Je();function xt(r){this._types=r||yu,this.text={},this.binary=\n{}}a(xt,\"TypeOverrides\");xt.prototype.getOverrides=function(r){switch(r){case\"text\":return this.text;case\"\\\nbinary\":return this.binary;default:return{}}};xt.prototype.setTypeParser=function(r,e,t){typeof e==\"\\\nfunction\"&&(t=e,e=\"text\"),this.getOverrides(e)[r]=t};xt.prototype.getTypeParser=function(r,e){return e=\ne||\"text\",this.getOverrides(e)[r]||this._types.getTypeParser(r,e)};Zi.exports=xt});function Xe(r){let e=1779033703,t=3144134277,n=1013904242,i=2773480762,s=1359893119,o=2600822924,u=528734635,\nc=1541459225,l=0,f=0,y=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,\n2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,\n4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,\n3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,\n1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,\n275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,\n2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],g=a((_,x)=>_>>>x|_<<32-\nx,\"rrot\"),A=new Uint32Array(64),C=new Uint8Array(64),Q=a(()=>{for(let B=0,$=0;B<16;B++,$+=4)A[B]=C[$]<<\n24|C[$+1]<<16|C[$+2]<<8|C[$+3];for(let B=16;B<64;B++){let $=g(A[B-15],7)^g(A[B-15],18)^A[B-15]>>>3,fe=g(\nA[B-2],17)^g(A[B-2],19)^A[B-2]>>>10;A[B]=A[B-16]+$+A[B-7]+fe|0}let _=e,x=t,H=n,le=i,N=s,ie=o,se=u,oe=c;\nfor(let B=0;B<64;B++){let $=g(N,6)^g(N,11)^g(N,25),fe=N&ie^~N&se,Ce=oe+$+fe+y[B]+A[B]|0,he=g(_,2)^g(\n_,13)^g(_,22),_e=_&x^_&H^x&H,ae=he+_e|0;oe=se,se=ie,ie=N,N=le+Ce|0,le=H,H=x,x=_,_=Ce+ae|0}e=e+_|0,t=\nt+x|0,n=n+H|0,i=i+le|0,s=s+N|0,o=o+ie|0,u=u+se|0,c=c+oe|0,f=0},\"process\"),P=a(_=>{typeof _==\"string\"&&\n(_=new TextEncoder().encode(_));for(let x=0;x<_.length;x++)C[f++]=_[x],f===64&&Q();l+=_.length},\"add\"),\nL=a(()=>{if(C[f++]=128,f==64&&Q(),f+8>64){for(;f<64;)C[f++]=0;Q()}for(;f<58;)C[f++]=0;let _=l*8;C[f++]=\n_/1099511627776&255,C[f++]=_/4294967296&255,C[f++]=_>>>24,C[f++]=_>>>16&255,C[f++]=_>>>8&255,C[f++]=\n_&255,Q();let x=new Uint8Array(32);return x[0]=e>>>24,x[1]=e>>>16&255,x[2]=e>>>8&255,x[3]=e&255,x[4]=\nt>>>24,x[5]=t>>>16&255,x[6]=t>>>8&255,x[7]=t&255,x[8]=n>>>24,x[9]=n>>>16&255,x[10]=n>>>8&255,x[11]=n&\n255,x[12]=i>>>24,x[13]=i>>>16&255,x[14]=i>>>8&255,x[15]=i&255,x[16]=s>>>24,x[17]=s>>>16&255,x[18]=s>>>\n8&255,x[19]=s&255,x[20]=o>>>24,x[21]=o>>>16&255,x[22]=o>>>8&255,x[23]=o&255,x[24]=u>>>24,x[25]=u>>>16&\n255,x[26]=u>>>8&255,x[27]=u&255,x[28]=c>>>24,x[29]=c>>>16&255,x[30]=c>>>8&255,x[31]=c&255,x},\"digest\");\nreturn r===void 0?{add:P,digest:L}:(P(r),L())}var Ji=z(()=>{\"use strict\";p();a(Xe,\"sha256\")});var U,et,Xi=z(()=>{\"use strict\";p();U=class U{constructor(){E(this,\"_dataLength\",0);E(this,\"_bufferL\\\nength\",0);E(this,\"_state\",new Int32Array(4));E(this,\"_buffer\",new ArrayBuffer(68));E(this,\"_buffer8\");\nE(this,\"_buffer32\");this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this.\n_buffer,0,17),this.start()}static hashByteArray(e,t=!1){return this.onePassHasher.start().appendByteArray(\ne).end(t)}static hashStr(e,t=!1){return this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){\nreturn this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(e){let t=U.hexChars,n=U.hexOut,\ni,s,o,u;for(u=0;u<4;u+=1)for(s=u*8,i=e[u],o=0;o<8;o+=2)n[s+1+o]=t.charAt(i&15),i>>>=4,n[s+0+o]=t.charAt(\ni&15),i>>>=4;return n.join(\"\")}static _md5cycle(e,t){let n=e[0],i=e[1],s=e[2],o=e[3];n+=(i&s|~i&o)+t[0]-\n680876936|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[1]-389564586|0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&i)+t[2]+\n606105819|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[3]-1044525330|0,i=(i<<22|i>>>10)+s|0,n+=(i&s|~i&o)+\nt[4]-176418897|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[5]+1200080426|0,o=(o<<12|o>>>20)+n|0,s+=(o&n|~o&\ni)+t[6]-1473231341|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[7]-45705983|0,i=(i<<22|i>>>10)+s|0,n+=(i&s|\n~i&o)+t[8]+1770035416|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[9]-1958414417|0,o=(o<<12|o>>>20)+n|0,s+=\n(o&n|~o&i)+t[10]-42063|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[11]-1990404162|0,i=(i<<22|i>>>10)+s|0,\nn+=(i&s|~i&o)+t[12]+1804603682|0,n=(n<<7|n>>>25)+i|0,o+=(n&i|~n&s)+t[13]-40341101|0,o=(o<<12|o>>>20)+\nn|0,s+=(o&n|~o&i)+t[14]-1502002290|0,s=(s<<17|s>>>15)+o|0,i+=(s&o|~s&n)+t[15]+1236535329|0,i=(i<<22|\ni>>>10)+s|0,n+=(i&o|s&~o)+t[1]-165796510|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[6]-1069501632|0,o=(o<<\n9|o>>>23)+n|0,s+=(o&i|n&~i)+t[11]+643717713|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[0]-373897302|0,i=\n(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[5]-701558691|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[10]+38016083|0,\no=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[15]-660478335|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[4]-405537848|\n0,i=(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[9]+568446438|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[14]-1019803690|\n0,o=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[3]-187363961|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[8]+1163531501|\n0,i=(i<<20|i>>>12)+s|0,n+=(i&o|s&~o)+t[13]-1444681467|0,n=(n<<5|n>>>27)+i|0,o+=(n&s|i&~s)+t[2]-51403784|\n0,o=(o<<9|o>>>23)+n|0,s+=(o&i|n&~i)+t[7]+1735328473|0,s=(s<<14|s>>>18)+o|0,i+=(s&n|o&~n)+t[12]-1926607734|\n0,i=(i<<20|i>>>12)+s|0,n+=(i^s^o)+t[5]-378558|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[8]-2022574463|0,o=(o<<\n11|o>>>21)+n|0,s+=(o^n^i)+t[11]+1839030562|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[14]-35309556|0,i=(i<<\n23|i>>>9)+s|0,n+=(i^s^o)+t[1]-1530992060|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[4]+1272893353|0,o=(o<<11|\no>>>21)+n|0,s+=(o^n^i)+t[7]-155497632|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[10]-1094730640|0,i=(i<<23|\ni>>>9)+s|0,n+=(i^s^o)+t[13]+681279174|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[0]-358537222|0,o=(o<<11|o>>>\n21)+n|0,s+=(o^n^i)+t[3]-722521979|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[6]+76029189|0,i=(i<<23|i>>>9)+\ns|0,n+=(i^s^o)+t[9]-640364487|0,n=(n<<4|n>>>28)+i|0,o+=(n^i^s)+t[12]-421815835|0,o=(o<<11|o>>>21)+n|\n0,s+=(o^n^i)+t[15]+530742520|0,s=(s<<16|s>>>16)+o|0,i+=(s^o^n)+t[2]-995338651|0,i=(i<<23|i>>>9)+s|0,\nn+=(s^(i|~o))+t[0]-198630844|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[7]+1126891415|0,o=(o<<10|o>>>22)+\nn|0,s+=(n^(o|~i))+t[14]-1416354905|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[5]-57434055|0,i=(i<<21|i>>>\n11)+s|0,n+=(s^(i|~o))+t[12]+1700485571|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[3]-1894986606|0,o=(o<<10|\no>>>22)+n|0,s+=(n^(o|~i))+t[10]-1051523|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[1]-2054922799|0,i=(i<<\n21|i>>>11)+s|0,n+=(s^(i|~o))+t[8]+1873313359|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[15]-30611744|0,o=\n(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[6]-1560198380|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[13]+1309151649|\n0,i=(i<<21|i>>>11)+s|0,n+=(s^(i|~o))+t[4]-145523070|0,n=(n<<6|n>>>26)+i|0,o+=(i^(n|~s))+t[11]-1120210379|\n0,o=(o<<10|o>>>22)+n|0,s+=(n^(o|~i))+t[2]+718787259|0,s=(s<<15|s>>>17)+o|0,i+=(o^(s|~n))+t[9]-343485551|\n0,i=(i<<21|i>>>11)+s|0,e[0]=n+e[0]|0,e[1]=i+e[1]|0,e[2]=s+e[2]|0,e[3]=o+e[3]|0}start(){return this._dataLength=\n0,this._bufferLength=0,this._state.set(U.stateIdentity),this}appendStr(e){let t=this._buffer8,n=this.\n_buffer32,i=this._bufferLength,s,o;for(o=0;o<e.length;o+=1){if(s=e.charCodeAt(o),s<128)t[i++]=s;else if(s<\n2048)t[i++]=(s>>>6)+192,t[i++]=s&63|128;else if(s<55296||s>56319)t[i++]=(s>>>12)+224,t[i++]=s>>>6&63|\n128,t[i++]=s&63|128;else{if(s=(s-55296)*1024+(e.charCodeAt(++o)-56320)+65536,s>1114111)throw new Error(\n\"Unicode standard supports code points up to U+10FFFF\");t[i++]=(s>>>18)+240,t[i++]=s>>>12&63|128,t[i++]=\ns>>>6&63|128,t[i++]=s&63|128}i>=64&&(this._dataLength+=64,U._md5cycle(this._state,n),i-=64,n[0]=n[16])}\nreturn this._bufferLength=i,this}appendAsciiStr(e){let t=this._buffer8,n=this._buffer32,i=this._bufferLength,\ns,o=0;for(;;){for(s=Math.min(e.length-o,64-i);s--;)t[i++]=e.charCodeAt(o++);if(i<64)break;this._dataLength+=\n64,U._md5cycle(this._state,n),i=0}return this._bufferLength=i,this}appendByteArray(e){let t=this._buffer8,\nn=this._buffer32,i=this._bufferLength,s,o=0;for(;;){for(s=Math.min(e.length-o,64-i);s--;)t[i++]=e[o++];\nif(i<64)break;this._dataLength+=64,U._md5cycle(this._state,n),i=0}return this._bufferLength=i,this}getState(){\nlet e=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this.\n_bufferLength,length:this._dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){let t=e.buffer,n=e.state,\ni=this._state,s;for(this._dataLength=e.length,this._bufferLength=e.buflen,i[0]=n[0],i[1]=n[1],i[2]=n[2],\ni[3]=n[3],s=0;s<t.length;s+=1)this._buffer8[s]=t.charCodeAt(s)}end(e=!1){let t=this._bufferLength,n=this.\n_buffer8,i=this._buffer32,s=(t>>2)+1;this._dataLength+=t;let o=this._dataLength*8;if(n[t]=128,n[t+1]=\nn[t+2]=n[t+3]=0,i.set(U.buffer32Identity.subarray(s),s),t>55&&(U._md5cycle(this._state,i),i.set(U.buffer32Identity)),\no<=4294967295)i[14]=o;else{let u=o.toString(16).match(/(.*?)(.{0,8})$/);if(u===null)return;let c=parseInt(\nu[2],16),l=parseInt(u[1],16)||0;i[14]=c,i[15]=l}return U._md5cycle(this._state,i),e?this._state:U._hex(\nthis._state)}};a(U,\"Md5\"),E(U,\"stateIdentity\",new Int32Array([1732584193,-271733879,-1732584194,271733878])),\nE(U,\"buffer32Identity\",new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),E(U,\"hexChars\",\"0123456789\\\nabcdef\"),E(U,\"hexOut\",[]),E(U,\"onePassHasher\",new U);et=U});var ar={};ne(ar,{createHash:()=>gu,createHmac:()=>wu,randomBytes:()=>mu});function mu(r){return crypto.\ngetRandomValues(d.alloc(r))}function gu(r){if(r===\"sha256\")return{update:a(function(e){return{digest:a(\nfunction(){return d.from(Xe(e))},\"digest\")}},\"update\")};if(r===\"md5\")return{update:a(function(e){return{\ndigest:a(function(){return typeof e==\"string\"?et.hashStr(e):et.hashByteArray(e)},\"digest\")}},\"update\")};\nthrow new Error(`Hash type '${r}' not supported`)}function wu(r,e){if(r!==\"sha256\")throw new Error(`\\\nOnly sha256 is supported (requested: '${r}')`);return{update:a(function(t){return{digest:a(function(){\ntypeof e==\"string\"&&(e=new TextEncoder().encode(e)),typeof t==\"string\"&&(t=new TextEncoder().encode(\nt));let n=e.length;if(n>64)e=Xe(e);else if(n<64){let c=new Uint8Array(64);c.set(e),e=c}let i=new Uint8Array(\n64),s=new Uint8Array(64);for(let c=0;c<64;c++)i[c]=54^e[c],s[c]=92^e[c];let o=new Uint8Array(t.length+\n64);o.set(i,0),o.set(t,64);let u=new Uint8Array(96);return u.set(s,0),u.set(Xe(o),64),d.from(Xe(u))},\n\"digest\")}},\"update\")}}var ur=z(()=>{\"use strict\";p();Ji();Xi();a(mu,\"randomBytes\");a(gu,\"createHash\");\na(wu,\"createHmac\")});var tt=I((Mf,cr)=>{\"use strict\";p();cr.exports={host:\"localhost\",user:m.platform===\"win32\"?m.env.USERNAME:\nm.env.USER,database:void 0,password:null,connectionString:void 0,port:5432,rows:0,binary:!1,max:10,idleTimeoutMillis:3e4,\nclient_encoding:\"\",ssl:!1,application_name:void 0,fallback_application_name:void 0,options:void 0,parseInputDatesAsUTC:!1,\nstatement_timeout:!1,lock_timeout:!1,idle_in_transaction_session_timeout:!1,query_timeout:!1,connect_timeout:0,\nkeepalives:1,keepalives_idle:0};var ke=Je(),bu=ke.getTypeParser(20,\"text\"),vu=ke.getTypeParser(1016,\n\"text\");cr.exports.__defineSetter__(\"parseInt8\",function(r){ke.setTypeParser(20,\"text\",r?ke.getTypeParser(\n23,\"text\"):bu),ke.setTypeParser(1016,\"text\",r?ke.getTypeParser(1007,\"text\"):vu)})});var rt=I((Df,ts)=>{\"use strict\";p();var xu=(ur(),D(ar)),Su=tt();function Eu(r){var e=r.replace(/\\\\/g,\n\"\\\\\\\\\").replace(/\"/g,'\\\\\"');return'\"'+e+'\"'}a(Eu,\"escapeElement\");function es(r){for(var e=\"{\",t=0;t<\nr.length;t++)t>0&&(e=e+\",\"),r[t]===null||typeof r[t]>\"u\"?e=e+\"NULL\":Array.isArray(r[t])?e=e+es(r[t]):\nr[t]instanceof d?e+=\"\\\\\\\\x\"+r[t].toString(\"hex\"):e+=Eu(Et(r[t]));return e=e+\"}\",e}a(es,\"arrayString\");\nvar Et=a(function(r,e){if(r==null)return null;if(r instanceof d)return r;if(ArrayBuffer.isView(r)){var t=d.\nfrom(r.buffer,r.byteOffset,r.byteLength);return t.length===r.byteLength?t:t.slice(r.byteOffset,r.byteOffset+\nr.byteLength)}return r instanceof Date?Su.parseInputDatesAsUTC?_u(r):Cu(r):Array.isArray(r)?es(r):typeof r==\n\"object\"?Au(r,e):r.toString()},\"prepareValue\");function Au(r,e){if(r&&typeof r.toPostgres==\"function\"){\nif(e=e||[],e.indexOf(r)!==-1)throw new Error('circular reference detected while preparing \"'+r+'\" fo\\\nr query');return e.push(r),Et(r.toPostgres(Et),e)}return JSON.stringify(r)}a(Au,\"prepareObject\");function W(r,e){\nfor(r=\"\"+r;r.length<e;)r=\"0\"+r;return r}a(W,\"pad\");function Cu(r){var e=-r.getTimezoneOffset(),t=r.getFullYear(),\nn=t<1;n&&(t=Math.abs(t)+1);var i=W(t,4)+\"-\"+W(r.getMonth()+1,2)+\"-\"+W(r.getDate(),2)+\"T\"+W(r.getHours(),\n2)+\":\"+W(r.getMinutes(),2)+\":\"+W(r.getSeconds(),2)+\".\"+W(r.getMilliseconds(),3);return e<0?(i+=\"-\",e*=\n-1):i+=\"+\",i+=W(Math.floor(e/60),2)+\":\"+W(e%60,2),n&&(i+=\" BC\"),i}a(Cu,\"dateToString\");function _u(r){\nvar e=r.getUTCFullYear(),t=e<1;t&&(e=Math.abs(e)+1);var n=W(e,4)+\"-\"+W(r.getUTCMonth()+1,2)+\"-\"+W(r.\ngetUTCDate(),2)+\"T\"+W(r.getUTCHours(),2)+\":\"+W(r.getUTCMinutes(),2)+\":\"+W(r.getUTCSeconds(),2)+\".\"+W(\nr.getUTCMilliseconds(),3);return n+=\"+00:00\",t&&(n+=\" BC\"),n}a(_u,\"dateToStringUTC\");function Tu(r,e,t){\nreturn r=typeof r==\"string\"?{text:r}:r,e&&(typeof e==\"function\"?r.callback=e:r.values=e),t&&(r.callback=\nt),r}a(Tu,\"normalizeQueryConfig\");var lr=a(function(r){return xu.createHash(\"md5\").update(r,\"utf-8\").\ndigest(\"hex\")},\"md5\"),Iu=a(function(r,e,t){var n=lr(e+r),i=lr(d.concat([d.from(n),t]));return\"md5\"+i},\n\"postgresMd5PasswordHash\");ts.exports={prepareValue:a(function(e){return Et(e)},\"prepareValueWrapper\"),\nnormalizeQueryConfig:Tu,postgresMd5PasswordHash:Iu,md5:lr}});var nt={};ne(nt,{default:()=>Lu});var Lu,it=z(()=>{\"use strict\";p();Lu={}});var hs=I((zf,fs)=>{\"use strict\";p();var hr=(ur(),D(ar));function Fu(r){if(r.indexOf(\"SCRAM-SHA-256\")===\n-1)throw new Error(\"SASL: Only mechanism SCRAM-SHA-256 is currently supported\");let e=hr.randomBytes(\n18).toString(\"base64\");return{mechanism:\"SCRAM-SHA-256\",clientNonce:e,response:\"n,,n=*,r=\"+e,message:\"\\\nSASLInitialResponse\"}}a(Fu,\"startSession\");function ku(r,e,t){if(r.message!==\"SASLInitialResponse\")throw new Error(\n\"SASL: Last message was not SASLInitialResponse\");if(typeof e!=\"string\")throw new Error(\"SASL: SCRAM\\\n-SERVER-FIRST-MESSAGE: client password must be a string\");if(typeof t!=\"string\")throw new Error(\"SAS\\\nL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a string\");let n=Du(t);if(n.nonce.startsWith(r.clientNonce)){\nif(n.nonce.length===r.clientNonce.length)throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server n\\\nonce is too short\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not st\\\nart with client nonce\");var i=d.from(n.salt,\"base64\"),s=Qu(e,i,n.iteration),o=Me(s,\"Client Key\"),u=qu(\no),c=\"n=*,r=\"+r.clientNonce,l=\"r=\"+n.nonce+\",s=\"+n.salt+\",i=\"+n.iteration,f=\"c=biws,r=\"+n.nonce,y=c+\n\",\"+l+\",\"+f,g=Me(u,y),A=ls(o,g),C=A.toString(\"base64\"),Q=Me(s,\"Server Key\"),P=Me(Q,y);r.message=\"SAS\\\nLResponse\",r.serverSignature=P.toString(\"base64\"),r.response=f+\",p=\"+C}a(ku,\"continueSession\");function Mu(r,e){\nif(r.message!==\"SASLResponse\")throw new Error(\"SASL: Last message was not SASLResponse\");if(typeof e!=\n\"string\")throw new Error(\"SASL: SCRAM-SERVER-FINAL-MESSAGE: serverData must be a string\");let{serverSignature:t}=Ou(\ne);if(t!==r.serverSignature)throw new Error(\"SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature does\\\n not match\")}a(Mu,\"finalizeSession\");function Uu(r){if(typeof r!=\"string\")throw new TypeError(\"SASL:\\\n text must be a string\");return r.split(\"\").map((e,t)=>r.charCodeAt(t)).every(e=>e>=33&&e<=43||e>=45&&\ne<=126)}a(Uu,\"isPrintableChars\");function us(r){return/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.\ntest(r)}a(us,\"isBase64\");function cs(r){if(typeof r!=\"string\")throw new TypeError(\"SASL: attribute p\\\nairs text must be a string\");return new Map(r.split(\",\").map(e=>{if(!/^.=/.test(e))throw new Error(\"\\\nSASL: Invalid attribute pair entry\");let t=e[0],n=e.substring(2);return[t,n]}))}a(cs,\"parseAttribute\\\nPairs\");function Du(r){let e=cs(r),t=e.get(\"r\");if(t){if(!Uu(t))throw new Error(\"SASL: SCRAM-SERVER-\\\nFIRST-MESSAGE: nonce must only contain printable characters\")}else throw new Error(\"SASL: SCRAM-SERV\\\nER-FIRST-MESSAGE: nonce missing\");let n=e.get(\"s\");if(n){if(!us(n))throw new Error(\"SASL: SCRAM-SERV\\\nER-FIRST-MESSAGE: salt must be base64\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: salt\\\n missing\");let i=e.get(\"i\");if(i){if(!/^[1-9][0-9]*$/.test(i))throw new Error(\"SASL: SCRAM-SERVER-FI\\\nRST-MESSAGE: invalid iteration count\")}else throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: itera\\\ntion missing\");let s=parseInt(i,10);return{nonce:t,salt:n,iteration:s}}a(Du,\"parseServerFirstMessage\");\nfunction Ou(r){let t=cs(r).get(\"v\");if(t){if(!us(t))throw new Error(\"SASL: SCRAM-SERVER-FINAL-MESSAG\\\nE: server signature must be base64\")}else throw new Error(\"SASL: SCRAM-SERVER-FINAL-MESSAGE: server \\\nsignature is missing\");return{serverSignature:t}}a(Ou,\"parseServerFinalMessage\");function ls(r,e){if(!d.\nisBuffer(r))throw new TypeError(\"first argument must be a Buffer\");if(!d.isBuffer(e))throw new TypeError(\n\"second argument must be a Buffer\");if(r.length!==e.length)throw new Error(\"Buffer lengths must matc\\\nh\");if(r.length===0)throw new Error(\"Buffers cannot be empty\");return d.from(r.map((t,n)=>r[n]^e[n]))}\na(ls,\"xorBuffers\");function qu(r){return hr.createHash(\"sha256\").update(r).digest()}a(qu,\"sha256\");function Me(r,e){\nreturn hr.createHmac(\"sha256\",r).update(e).digest()}a(Me,\"hmacSha256\");function Qu(r,e,t){for(var n=Me(\nr,d.concat([e,d.from([0,0,0,1])])),i=n,s=0;s<t-1;s++)n=Me(r,n),i=ls(i,n);return i}a(Qu,\"Hi\");fs.exports=\n{startSession:Fu,continueSession:ku,finalizeSession:Mu}});var pr={};ne(pr,{join:()=>Nu});function Nu(...r){return r.join(\"/\")}var dr=z(()=>{\"use strict\";p();a(\nNu,\"join\")});var yr={};ne(yr,{stat:()=>ju});function ju(r,e){e(new Error(\"No filesystem\"))}var mr=z(()=>{\"use str\\\nict\";p();a(ju,\"stat\")});var gr={};ne(gr,{default:()=>Wu});var Wu,wr=z(()=>{\"use strict\";p();Wu={}});var ps={};ne(ps,{StringDecoder:()=>br});var vr,br,ds=z(()=>{\"use strict\";p();vr=class vr{constructor(e){\nE(this,\"td\");this.td=new TextDecoder(e)}write(e){return this.td.decode(e,{stream:!0})}end(e){return this.\ntd.decode(e)}};a(vr,\"StringDecoder\");br=vr});var ws=I((ih,gs)=>{\"use strict\";p();var{Transform:Hu}=(wr(),D(gr)),{StringDecoder:$u}=(ds(),D(ps)),be=Symbol(\n\"last\"),Ct=Symbol(\"decoder\");function Gu(r,e,t){let n;if(this.overflow){if(n=this[Ct].write(r).split(\nthis.matcher),n.length===1)return t();n.shift(),this.overflow=!1}else this[be]+=this[Ct].write(r),n=\nthis[be].split(this.matcher);this[be]=n.pop();for(let i=0;i<n.length;i++)try{ms(this,this.mapper(n[i]))}catch(s){\nreturn t(s)}if(this.overflow=this[be].length>this.maxLength,this.overflow&&!this.skipOverflow){t(new Error(\n\"maximum buffer reached\"));return}t()}a(Gu,\"transform\");function Vu(r){if(this[be]+=this[Ct].end(),this[be])\ntry{ms(this,this.mapper(this[be]))}catch(e){return r(e)}r()}a(Vu,\"flush\");function ms(r,e){e!==void 0&&\nr.push(e)}a(ms,\"push\");function ys(r){return r}a(ys,\"noop\");function zu(r,e,t){switch(r=r||/\\r?\\n/,e=\ne||ys,t=t||{},arguments.length){case 1:typeof r==\"function\"?(e=r,r=/\\r?\\n/):typeof r==\"object\"&&!(r instanceof\nRegExp)&&!r[Symbol.split]&&(t=r,r=/\\r?\\n/);break;case 2:typeof r==\"function\"?(t=e,e=r,r=/\\r?\\n/):typeof e==\n\"object\"&&(t=e,e=ys)}t=Object.assign({},t),t.autoDestroy=!0,t.transform=Gu,t.flush=Vu,t.readableObjectMode=\n!0;let n=new Hu(t);return n[be]=\"\",n[Ct]=new $u(\"utf8\"),n.matcher=r,n.mapper=e,n.maxLength=t.maxLength,\nn.skipOverflow=t.skipOverflow||!1,n.overflow=!1,n._destroy=function(i,s){this._writableState.errorEmitted=\n!1,s(i)},n}a(zu,\"split\");gs.exports=zu});var xs=I((ah,de)=>{\"use strict\";p();var bs=(dr(),D(pr)),Ku=(wr(),D(gr)).Stream,Yu=ws(),vs=(it(),D(nt)),\nZu=5432,_t=m.platform===\"win32\",st=m.stderr,Ju=56,Xu=7,ec=61440,tc=32768;function rc(r){return(r&ec)==\ntc}a(rc,\"isRegFile\");var Ue=[\"host\",\"port\",\"database\",\"user\",\"password\"],xr=Ue.length,nc=Ue[xr-1];function Sr(){\nvar r=st instanceof Ku&&st.writable===!0;if(r){var e=Array.prototype.slice.call(arguments).concat(`\n`);st.write(vs.format.apply(vs,e))}}a(Sr,\"warn\");Object.defineProperty(de.exports,\"isWin\",{get:a(function(){\nreturn _t},\"get\"),set:a(function(r){_t=r},\"set\")});de.exports.warnTo=function(r){var e=st;return st=\nr,e};de.exports.getFileName=function(r){var e=r||m.env,t=e.PGPASSFILE||(_t?bs.join(e.APPDATA||\"./\",\"\\\npostgresql\",\"pgpass.conf\"):bs.join(e.HOME||\"./\",\".pgpass\"));return t};de.exports.usePgPass=function(r,e){\nreturn Object.prototype.hasOwnProperty.call(m.env,\"PGPASSWORD\")?!1:_t?!0:(e=e||\"<unkn>\",rc(r.mode)?r.\nmode&(Ju|Xu)?(Sr('WARNING: password file \"%s\" has group or world access; permissions should be u=rw \\\n(0600) or less',e),!1):!0:(Sr('WARNING: password file \"%s\" is not a plain file',e),!1))};var ic=de.exports.\nmatch=function(r,e){return Ue.slice(0,-1).reduce(function(t,n,i){return i==1&&Number(r[n]||Zu)===Number(\ne[n])?t&&!0:t&&(e[n]===\"*\"||e[n]===r[n])},!0)};de.exports.getPassword=function(r,e,t){var n,i=e.pipe(\nYu());function s(c){var l=sc(c);l&&oc(l)&&ic(r,l)&&(n=l[nc],i.end())}a(s,\"onLine\");var o=a(function(){\ne.destroy(),t(n)},\"onEnd\"),u=a(function(c){e.destroy(),Sr(\"WARNING: error on reading file: %s\",c),t(\nvoid 0)},\"onErr\");e.on(\"error\",u),i.on(\"data\",s).on(\"end\",o).on(\"error\",u)};var sc=de.exports.parseLine=\nfunction(r){if(r.length<11||r.match(/^\\s+#/))return null;for(var e=\"\",t=\"\",n=0,i=0,s=0,o={},u=!1,c=a(\nfunction(f,y,g){var A=r.substring(y,g);Object.hasOwnProperty.call(m.env,\"PGPASS_NO_DEESCAPE\")||(A=A.\nreplace(/\\\\([:\\\\])/g,\"$1\")),o[Ue[f]]=A},\"addToObj\"),l=0;l<r.length-1;l+=1){if(e=r.charAt(l+1),t=r.charAt(\nl),u=n==xr-1,u){c(n,i);break}l>=0&&e==\":\"&&t!==\"\\\\\"&&(c(n,i,l+1),i=l+2,n+=1)}return o=Object.keys(o).\nlength===xr?o:null,o},oc=de.exports.isValidEntry=function(r){for(var e={0:function(o){return o.length>\n0},1:function(o){return o===\"*\"?!0:(o=Number(o),isFinite(o)&&o>0&&o<9007199254740992&&Math.floor(o)===\no)},2:function(o){return o.length>0},3:function(o){return o.length>0},4:function(o){return o.length>\n0}},t=0;t<Ue.length;t+=1){var n=e[t],i=r[Ue[t]]||\"\",s=n(i);if(!s)return!1}return!0}});var Es=I((fh,Er)=>{\"use strict\";p();var lh=(dr(),D(pr)),Ss=(mr(),D(yr)),Tt=xs();Er.exports=function(r,e){\nvar t=Tt.getFileName();Ss.stat(t,function(n,i){if(n||!Tt.usePgPass(i,t))return e(void 0);var s=Ss.createReadStream(\nt);Tt.getPassword(r,s,e)})};Er.exports.warnTo=Tt.warnTo});var As={};ne(As,{default:()=>ac});var ac,Cs=z(()=>{\"use strict\";p();ac={}});var Ts=I((dh,_s)=>{\"use strict\";p();var uc=(zt(),D(mi)),Ar=(mr(),D(yr));function Cr(r){if(r.charAt(0)===\n\"/\"){var t=r.split(\" \");return{host:t[0],database:t[1]}}var e=uc.parse(/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.\ntest(r)?encodeURI(r).replace(/\\%25(\\d\\d)/g,\"%$1\"):r,!0),t=e.query;for(var n in t)Array.isArray(t[n])&&\n(t[n]=t[n][t[n].length-1]);var i=(e.auth||\":\").split(\":\");if(t.user=i[0],t.password=i.splice(1).join(\n\":\"),t.port=e.port,e.protocol==\"socket:\")return t.host=decodeURI(e.pathname),t.database=e.query.db,t.\nclient_encoding=e.query.encoding,t;t.host||(t.host=e.hostname);var s=e.pathname;if(!t.host&&s&&/^%2f/i.\ntest(s)){var o=s.split(\"/\");t.host=decodeURIComponent(o[0]),s=o.splice(1).join(\"/\")}switch(s&&s.charAt(\n0)===\"/\"&&(s=s.slice(1)||null),t.database=s&&decodeURI(s),(t.ssl===\"true\"||t.ssl===\"1\")&&(t.ssl=!0),\nt.ssl===\"0\"&&(t.ssl=!1),(t.sslcert||t.sslkey||t.sslrootcert||t.sslmode)&&(t.ssl={}),t.sslcert&&(t.ssl.\ncert=Ar.readFileSync(t.sslcert).toString()),t.sslkey&&(t.ssl.key=Ar.readFileSync(t.sslkey).toString()),\nt.sslrootcert&&(t.ssl.ca=Ar.readFileSync(t.sslrootcert).toString()),t.sslmode){case\"disable\":{t.ssl=\n!1;break}case\"prefer\":case\"require\":case\"verify-ca\":case\"verify-full\":break;case\"no-verify\":{t.ssl.rejectUnauthorized=\n!1;break}}return t}a(Cr,\"parse\");_s.exports=Cr;Cr.parse=Cr});var It=I((gh,Rs)=>{\"use strict\";p();var cc=(Cs(),D(As)),Ps=tt(),Is=Ts().parse,G=a(function(r,e,t){return t===\nvoid 0?t=m.env[\"PG\"+r.toUpperCase()]:t===!1||(t=m.env[t]),e[r]||t||Ps[r]},\"val\"),lc=a(function(){switch(m.\nenv.PGSSLMODE){case\"disable\":return!1;case\"prefer\":case\"require\":case\"verify-ca\":case\"verify-full\":return!0;case\"\\\nno-verify\":return{rejectUnauthorized:!1}}return Ps.ssl},\"readSSLConfigFromEnvironment\"),De=a(function(r){\nreturn\"'\"+(\"\"+r).replace(/\\\\/g,\"\\\\\\\\\").replace(/'/g,\"\\\\'\")+\"'\"},\"quoteParamValue\"),re=a(function(r,e,t){\nvar n=e[t];n!=null&&r.push(t+\"=\"+De(n))},\"add\"),Tr=class Tr{constructor(e){e=typeof e==\"string\"?Is(e):\ne||{},e.connectionString&&(e=Object.assign({},e,Is(e.connectionString))),this.user=G(\"user\",e),this.\ndatabase=G(\"database\",e),this.database===void 0&&(this.database=this.user),this.port=parseInt(G(\"por\\\nt\",e),10),this.host=G(\"host\",e),Object.defineProperty(this,\"password\",{configurable:!0,enumerable:!1,\nwritable:!0,value:G(\"password\",e)}),this.binary=G(\"binary\",e),this.options=G(\"options\",e),this.ssl=typeof e.\nssl>\"u\"?lc():e.ssl,typeof this.ssl==\"string\"&&this.ssl===\"true\"&&(this.ssl=!0),this.ssl===\"no-verify\"&&\n(this.ssl={rejectUnauthorized:!1}),this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,\"key\",{enumerable:!1}),\nthis.client_encoding=G(\"client_encoding\",e),this.replication=G(\"replication\",e),this.isDomainSocket=\n!(this.host||\"\").indexOf(\"/\"),this.application_name=G(\"application_name\",e,\"PGAPPNAME\"),this.fallback_application_name=\nG(\"fallback_application_name\",e,!1),this.statement_timeout=G(\"statement_timeout\",e,!1),this.lock_timeout=\nG(\"lock_timeout\",e,!1),this.idle_in_transaction_session_timeout=G(\"idle_in_transaction_session_timeo\\\nut\",e,!1),this.query_timeout=G(\"query_timeout\",e,!1),e.connectionTimeoutMillis===void 0?this.connect_timeout=\nm.env.PGCONNECT_TIMEOUT||0:this.connect_timeout=Math.floor(e.connectionTimeoutMillis/1e3),e.keepAlive===\n!1?this.keepalives=0:e.keepAlive===!0&&(this.keepalives=1),typeof e.keepAliveInitialDelayMillis==\"nu\\\nmber\"&&(this.keepalives_idle=Math.floor(e.keepAliveInitialDelayMillis/1e3))}getLibpqConnectionString(e){\nvar t=[];re(t,this,\"user\"),re(t,this,\"password\"),re(t,this,\"port\"),re(t,this,\"application_name\"),re(\nt,this,\"fallback_application_name\"),re(t,this,\"connect_timeout\"),re(t,this,\"options\");var n=typeof this.\nssl==\"object\"?this.ssl:this.ssl?{sslmode:this.ssl}:{};if(re(t,n,\"sslmode\"),re(t,n,\"sslca\"),re(t,n,\"s\\\nslkey\"),re(t,n,\"sslcert\"),re(t,n,\"sslrootcert\"),this.database&&t.push(\"dbname=\"+De(this.database)),this.\nreplication&&t.push(\"replication=\"+De(this.replication)),this.host&&t.push(\"host=\"+De(this.host)),this.\nisDomainSocket)return e(null,t.join(\" \"));this.client_encoding&&t.push(\"client_encoding=\"+De(this.client_encoding)),\ncc.lookup(this.host,function(i,s){return i?e(i,null):(t.push(\"hostaddr=\"+De(s)),e(null,t.join(\" \")))})}};\na(Tr,\"ConnectionParameters\");var _r=Tr;Rs.exports=_r});var Fs=I((vh,Ls)=>{\"use strict\";p();var fc=Je(),Bs=/^([A-Za-z]+)(?: (\\d+))?(?: (\\d+))?/,Pr=class Pr{constructor(e,t){\nthis.command=null,this.rowCount=null,this.oid=null,this.rows=[],this.fields=[],this._parsers=void 0,\nthis._types=t,this.RowCtor=null,this.rowAsArray=e===\"array\",this.rowAsArray&&(this.parseRow=this._parseRowAsArray)}addCommandComplete(e){\nvar t;e.text?t=Bs.exec(e.text):t=Bs.exec(e.command),t&&(this.command=t[1],t[3]?(this.oid=parseInt(t[2],\n10),this.rowCount=parseInt(t[3],10)):t[2]&&(this.rowCount=parseInt(t[2],10)))}_parseRowAsArray(e){for(var t=new Array(\ne.length),n=0,i=e.length;n<i;n++){var s=e[n];s!==null?t[n]=this._parsers[n](s):t[n]=null}return t}parseRow(e){\nfor(var t={},n=0,i=e.length;n<i;n++){var s=e[n],o=this.fields[n].name;s!==null?t[o]=this._parsers[n](\ns):t[o]=null}return t}addRow(e){this.rows.push(e)}addFields(e){this.fields=e,this.fields.length&&(this.\n_parsers=new Array(e.length));for(var t=0;t<e.length;t++){var n=e[t];this._types?this._parsers[t]=this.\n_types.getTypeParser(n.dataTypeID,n.format||\"text\"):this._parsers[t]=fc.getTypeParser(n.dataTypeID,n.\nformat||\"text\")}}};a(Pr,\"Result\");var Ir=Pr;Ls.exports=Ir});var Ds=I((Eh,Us)=>{\"use strict\";p();var{EventEmitter:hc}=me(),ks=Fs(),Ms=rt(),Br=class Br extends hc{constructor(e,t,n){\nsuper(),e=Ms.normalizeQueryConfig(e,t,n),this.text=e.text,this.values=e.values,this.rows=e.rows,this.\ntypes=e.types,this.name=e.name,this.binary=e.binary,this.portal=e.portal||\"\",this.callback=e.callback,\nthis._rowMode=e.rowMode,m.domain&&e.callback&&(this.callback=m.domain.bind(e.callback)),this._result=\nnew ks(this._rowMode,this.types),this._results=this._result,this.isPreparedStatement=!1,this._canceledDueToError=\n!1,this._promise=null}requiresPreparation(){return this.name||this.rows?!0:!this.text||!this.values?\n!1:this.values.length>0}_checkForMultirow(){this._result.command&&(Array.isArray(this._results)||(this.\n_results=[this._result]),this._result=new ks(this._rowMode,this.types),this._results.push(this._result))}handleRowDescription(e){\nthis._checkForMultirow(),this._result.addFields(e.fields),this._accumulateRows=this.callback||!this.\nlisteners(\"row\").length}handleDataRow(e){let t;if(!this._canceledDueToError){try{t=this._result.parseRow(\ne.fields)}catch(n){this._canceledDueToError=n;return}this.emit(\"row\",t,this._result),this._accumulateRows&&\nthis._result.addRow(t)}}handleCommandComplete(e,t){this._checkForMultirow(),this._result.addCommandComplete(\ne),this.rows&&t.sync()}handleEmptyQuery(e){this.rows&&e.sync()}handleError(e,t){if(this._canceledDueToError&&\n(e=this._canceledDueToError,this._canceledDueToError=!1),this.callback)return this.callback(e);this.\nemit(\"error\",e)}handleReadyForQuery(e){if(this._canceledDueToError)return this.handleError(this._canceledDueToError,\ne);if(this.callback)try{this.callback(null,this._results)}catch(t){m.nextTick(()=>{throw t})}this.emit(\n\"end\",this._results)}submit(e){if(typeof this.text!=\"string\"&&typeof this.name!=\"string\")return new Error(\n\"A query must have either text or a name. Supplying neither is unsupported.\");let t=e.parsedStatements[this.\nname];return this.text&&t&&this.text!==t?new Error(`Prepared statements must be unique - '${this.name}\\\n' was used for a different statement`):this.values&&!Array.isArray(this.values)?new Error(\"Query val\\\nues must be an array\"):(this.requiresPreparation()?this.prepare(e):e.query(this.text),null)}hasBeenParsed(e){\nreturn this.name&&e.parsedStatements[this.name]}handlePortalSuspended(e){this._getRows(e,this.rows)}_getRows(e,t){\ne.execute({portal:this.portal,rows:t}),t?e.flush():e.sync()}prepare(e){this.isPreparedStatement=!0,this.\nhasBeenParsed(e)||e.parse({text:this.text,name:this.name,types:this.types});try{e.bind({portal:this.\nportal,statement:this.name,values:this.values,binary:this.binary,valueMapper:Ms.prepareValue})}catch(t){\nthis.handleError(t,e);return}e.describe({type:\"P\",name:this.portal||\"\"}),this._getRows(e,this.rows)}handleCopyInResponse(e){\ne.sendCopyFail(\"No source stream defined\")}handleCopyData(e,t){}};a(Br,\"Query\");var Rr=Br;Us.exports=\nRr});var an=I(T=>{\"use strict\";p();Object.defineProperty(T,\"__esModule\",{value:!0});T.NoticeMessage=T.DataRowMessage=\nT.CommandCompleteMessage=T.ReadyForQueryMessage=T.NotificationResponseMessage=T.BackendKeyDataMessage=\nT.AuthenticationMD5Password=T.ParameterStatusMessage=T.ParameterDescriptionMessage=T.RowDescriptionMessage=\nT.Field=T.CopyResponse=T.CopyDataMessage=T.DatabaseError=T.copyDone=T.emptyQuery=T.replicationStart=\nT.portalSuspended=T.noData=T.closeComplete=T.bindComplete=T.parseComplete=void 0;T.parseComplete={name:\"\\\nparseComplete\",length:5};T.bindComplete={name:\"bindComplete\",length:5};T.closeComplete={name:\"closeC\\\nomplete\",length:5};T.noData={name:\"noData\",length:5};T.portalSuspended={name:\"portalSuspended\",length:5};\nT.replicationStart={name:\"replicationStart\",length:4};T.emptyQuery={name:\"emptyQuery\",length:4};T.copyDone=\n{name:\"copyDone\",length:4};var Gr=class Gr extends Error{constructor(e,t,n){super(e),this.length=t,this.\nname=n}};a(Gr,\"DatabaseError\");var Lr=Gr;T.DatabaseError=Lr;var Vr=class Vr{constructor(e,t){this.length=\ne,this.chunk=t,this.name=\"copyData\"}};a(Vr,\"CopyDataMessage\");var Fr=Vr;T.CopyDataMessage=Fr;var zr=class zr{constructor(e,t,n,i){\nthis.length=e,this.name=t,this.binary=n,this.columnTypes=new Array(i)}};a(zr,\"CopyResponse\");var kr=zr;\nT.CopyResponse=kr;var Kr=class Kr{constructor(e,t,n,i,s,o,u){this.name=e,this.tableID=t,this.columnID=\nn,this.dataTypeID=i,this.dataTypeSize=s,this.dataTypeModifier=o,this.format=u}};a(Kr,\"Field\");var Mr=Kr;\nT.Field=Mr;var Yr=class Yr{constructor(e,t){this.length=e,this.fieldCount=t,this.name=\"rowDescriptio\\\nn\",this.fields=new Array(this.fieldCount)}};a(Yr,\"RowDescriptionMessage\");var Ur=Yr;T.RowDescriptionMessage=\nUr;var Zr=class Zr{constructor(e,t){this.length=e,this.parameterCount=t,this.name=\"parameterDescript\\\nion\",this.dataTypeIDs=new Array(this.parameterCount)}};a(Zr,\"ParameterDescriptionMessage\");var Dr=Zr;\nT.ParameterDescriptionMessage=Dr;var Jr=class Jr{constructor(e,t,n){this.length=e,this.parameterName=\nt,this.parameterValue=n,this.name=\"parameterStatus\"}};a(Jr,\"ParameterStatusMessage\");var Or=Jr;T.ParameterStatusMessage=\nOr;var Xr=class Xr{constructor(e,t){this.length=e,this.salt=t,this.name=\"authenticationMD5Password\"}};\na(Xr,\"AuthenticationMD5Password\");var qr=Xr;T.AuthenticationMD5Password=qr;var en=class en{constructor(e,t,n){\nthis.length=e,this.processID=t,this.secretKey=n,this.name=\"backendKeyData\"}};a(en,\"BackendKeyDataMes\\\nsage\");var Qr=en;T.BackendKeyDataMessage=Qr;var tn=class tn{constructor(e,t,n,i){this.length=e,this.\nprocessId=t,this.channel=n,this.payload=i,this.name=\"notification\"}};a(tn,\"NotificationResponseMessa\\\nge\");var Nr=tn;T.NotificationResponseMessage=Nr;var rn=class rn{constructor(e,t){this.length=e,this.\nstatus=t,this.name=\"readyForQuery\"}};a(rn,\"ReadyForQueryMessage\");var jr=rn;T.ReadyForQueryMessage=jr;\nvar nn=class nn{constructor(e,t){this.length=e,this.text=t,this.name=\"commandComplete\"}};a(nn,\"Comma\\\nndCompleteMessage\");var Wr=nn;T.CommandCompleteMessage=Wr;var sn=class sn{constructor(e,t){this.length=\ne,this.fields=t,this.name=\"dataRow\",this.fieldCount=t.length}};a(sn,\"DataRowMessage\");var Hr=sn;T.DataRowMessage=\nHr;var on=class on{constructor(e,t){this.length=e,this.message=t,this.name=\"notice\"}};a(on,\"NoticeMe\\\nssage\");var $r=on;T.NoticeMessage=$r});var Os=I(Pt=>{\"use strict\";p();Object.defineProperty(Pt,\"__esModule\",{value:!0});Pt.Writer=void 0;var cn=class cn{constructor(e=256){\nthis.size=e,this.offset=5,this.headerPosition=0,this.buffer=d.allocUnsafe(e)}ensure(e){var t=this.buffer.\nlength-this.offset;if(t<e){var n=this.buffer,i=n.length+(n.length>>1)+e;this.buffer=d.allocUnsafe(i),\nn.copy(this.buffer)}}addInt32(e){return this.ensure(4),this.buffer[this.offset++]=e>>>24&255,this.buffer[this.\noffset++]=e>>>16&255,this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addInt16(e){\nreturn this.ensure(2),this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addCString(e){\nif(!e)this.ensure(1);else{var t=d.byteLength(e);this.ensure(t+1),this.buffer.write(e,this.offset,\"ut\\\nf-8\"),this.offset+=t}return this.buffer[this.offset++]=0,this}addString(e=\"\"){var t=d.byteLength(e);\nreturn this.ensure(t),this.buffer.write(e,this.offset),this.offset+=t,this}add(e){return this.ensure(\ne.length),e.copy(this.buffer,this.offset),this.offset+=e.length,this}join(e){if(e){this.buffer[this.\nheaderPosition]=e;let t=this.offset-(this.headerPosition+1);this.buffer.writeInt32BE(t,this.headerPosition+\n1)}return this.buffer.slice(e?0:5,this.offset)}flush(e){var t=this.join(e);return this.offset=5,this.\nheaderPosition=0,this.buffer=d.allocUnsafe(this.size),t}};a(cn,\"Writer\");var un=cn;Pt.Writer=un});var Qs=I(Bt=>{\"use strict\";p();Object.defineProperty(Bt,\"__esModule\",{value:!0});Bt.serialize=void 0;\nvar ln=Os(),k=new ln.Writer,pc=a(r=>{k.addInt16(3).addInt16(0);for(let n of Object.keys(r))k.addCString(\nn).addCString(r[n]);k.addCString(\"client_encoding\").addCString(\"UTF8\");var e=k.addCString(\"\").flush(),\nt=e.length+4;return new ln.Writer().addInt32(t).add(e).flush()},\"startup\"),dc=a(()=>{let r=d.allocUnsafe(\n8);return r.writeInt32BE(8,0),r.writeInt32BE(80877103,4),r},\"requestSsl\"),yc=a(r=>k.addCString(r).flush(\n112),\"password\"),mc=a(function(r,e){return k.addCString(r).addInt32(d.byteLength(e)).addString(e),k.\nflush(112)},\"sendSASLInitialResponseMessage\"),gc=a(function(r){return k.addString(r).flush(112)},\"se\\\nndSCRAMClientFinalMessage\"),wc=a(r=>k.addCString(r).flush(81),\"query\"),qs=[],bc=a(r=>{let e=r.name||\n\"\";e.length>63&&(console.error(\"Warning! Postgres only supports 63 characters for query names.\"),console.\nerror(\"You supplied %s (%s)\",e,e.length),console.error(\"This can cause conflicts and silent errors e\\\nxecuting queries\"));let t=r.types||qs;for(var n=t.length,i=k.addCString(e).addCString(r.text).addInt16(\nn),s=0;s<n;s++)i.addInt32(t[s]);return k.flush(80)},\"parse\"),Oe=new ln.Writer,vc=a(function(r,e){for(let t=0;t<\nr.length;t++){let n=e?e(r[t],t):r[t];n==null?(k.addInt16(0),Oe.addInt32(-1)):n instanceof d?(k.addInt16(\n1),Oe.addInt32(n.length),Oe.add(n)):(k.addInt16(0),Oe.addInt32(d.byteLength(n)),Oe.addString(n))}},\"\\\nwriteValues\"),xc=a((r={})=>{let e=r.portal||\"\",t=r.statement||\"\",n=r.binary||!1,i=r.values||qs,s=i.length;\nreturn k.addCString(e).addCString(t),k.addInt16(s),vc(i,r.valueMapper),k.addInt16(s),k.add(Oe.flush()),\nk.addInt16(n?1:0),k.flush(66)},\"bind\"),Sc=d.from([69,0,0,0,9,0,0,0,0,0]),Ec=a(r=>{if(!r||!r.portal&&\n!r.rows)return Sc;let e=r.portal||\"\",t=r.rows||0,n=d.byteLength(e),i=4+n+1+4,s=d.allocUnsafe(1+i);return s[0]=\n69,s.writeInt32BE(i,1),s.write(e,5,\"utf-8\"),s[n+5]=0,s.writeUInt32BE(t,s.length-4),s},\"execute\"),Ac=a(\n(r,e)=>{let t=d.allocUnsafe(16);return t.writeInt32BE(16,0),t.writeInt16BE(1234,4),t.writeInt16BE(5678,\n6),t.writeInt32BE(r,8),t.writeInt32BE(e,12),t},\"cancel\"),fn=a((r,e)=>{let n=4+d.byteLength(e)+1,i=d.\nallocUnsafe(1+n);return i[0]=r,i.writeInt32BE(n,1),i.write(e,5,\"utf-8\"),i[n]=0,i},\"cstringMessage\"),\nCc=k.addCString(\"P\").flush(68),_c=k.addCString(\"S\").flush(68),Tc=a(r=>r.name?fn(68,`${r.type}${r.name||\n\"\"}`):r.type===\"P\"?Cc:_c,\"describe\"),Ic=a(r=>{let e=`${r.type}${r.name||\"\"}`;return fn(67,e)},\"close\"),\nPc=a(r=>k.add(r).flush(100),\"copyData\"),Rc=a(r=>fn(102,r),\"copyFail\"),Rt=a(r=>d.from([r,0,0,0,4]),\"c\\\nodeOnlyBuffer\"),Bc=Rt(72),Lc=Rt(83),Fc=Rt(88),kc=Rt(99),Mc={startup:pc,password:yc,requestSsl:dc,sendSASLInitialResponseMessage:mc,\nsendSCRAMClientFinalMessage:gc,query:wc,parse:bc,bind:xc,execute:Ec,describe:Tc,close:Ic,flush:a(()=>Bc,\n\"flush\"),sync:a(()=>Lc,\"sync\"),end:a(()=>Fc,\"end\"),copyData:Pc,copyDone:a(()=>kc,\"copyDone\"),copyFail:Rc,\ncancel:Ac};Bt.serialize=Mc});var Ns=I(Lt=>{\"use strict\";p();Object.defineProperty(Lt,\"__esModule\",{value:!0});Lt.BufferReader=void 0;\nvar Uc=d.allocUnsafe(0),pn=class pn{constructor(e=0){this.offset=e,this.buffer=Uc,this.encoding=\"utf\\\n-8\"}setBuffer(e,t){this.offset=e,this.buffer=t}int16(){let e=this.buffer.readInt16BE(this.offset);return this.\noffset+=2,e}byte(){let e=this.buffer[this.offset];return this.offset++,e}int32(){let e=this.buffer.readInt32BE(\nthis.offset);return this.offset+=4,e}uint32(){let e=this.buffer.readUInt32BE(this.offset);return this.\noffset+=4,e}string(e){let t=this.buffer.toString(this.encoding,this.offset,this.offset+e);return this.\noffset+=e,t}cstring(){let e=this.offset,t=e;for(;this.buffer[t++]!==0;);return this.offset=t,this.buffer.\ntoString(this.encoding,e,t-1)}bytes(e){let t=this.buffer.slice(this.offset,this.offset+e);return this.\noffset+=e,t}};a(pn,\"BufferReader\");var hn=pn;Lt.BufferReader=hn});var Hs=I(Ft=>{\"use strict\";p();Object.defineProperty(Ft,\"__esModule\",{value:!0});Ft.Parser=void 0;var M=an(),\nDc=Ns(),dn=1,Oc=4,js=dn+Oc,Ws=d.allocUnsafe(0),mn=class mn{constructor(e){if(this.buffer=Ws,this.bufferLength=\n0,this.bufferOffset=0,this.reader=new Dc.BufferReader,e?.mode===\"binary\")throw new Error(\"Binary mod\\\ne not supported yet\");this.mode=e?.mode||\"text\"}parse(e,t){this.mergeBuffer(e);let n=this.bufferOffset+\nthis.bufferLength,i=this.bufferOffset;for(;i+js<=n;){let s=this.buffer[i],o=this.buffer.readUInt32BE(\ni+dn),u=dn+o;if(u+i<=n){let c=this.handlePacket(i+js,s,o,this.buffer);t(c),i+=u}else break}i===n?(this.\nbuffer=Ws,this.bufferLength=0,this.bufferOffset=0):(this.bufferLength=n-i,this.bufferOffset=i)}mergeBuffer(e){\nif(this.bufferLength>0){let t=this.bufferLength+e.byteLength;if(t+this.bufferOffset>this.buffer.byteLength){\nlet i;if(t<=this.buffer.byteLength&&this.bufferOffset>=this.bufferLength)i=this.buffer;else{let s=this.\nbuffer.byteLength*2;for(;t>=s;)s*=2;i=d.allocUnsafe(s)}this.buffer.copy(i,0,this.bufferOffset,this.bufferOffset+\nthis.bufferLength),this.buffer=i,this.bufferOffset=0}e.copy(this.buffer,this.bufferOffset+this.bufferLength),\nthis.bufferLength=t}else this.buffer=e,this.bufferOffset=0,this.bufferLength=e.byteLength}handlePacket(e,t,n,i){\nswitch(t){case 50:return M.bindComplete;case 49:return M.parseComplete;case 51:return M.closeComplete;case 110:\nreturn M.noData;case 115:return M.portalSuspended;case 99:return M.copyDone;case 87:return M.replicationStart;case 73:\nreturn M.emptyQuery;case 68:return this.parseDataRowMessage(e,n,i);case 67:return this.parseCommandCompleteMessage(\ne,n,i);case 90:return this.parseReadyForQueryMessage(e,n,i);case 65:return this.parseNotificationMessage(\ne,n,i);case 82:return this.parseAuthenticationResponse(e,n,i);case 83:return this.parseParameterStatusMessage(\ne,n,i);case 75:return this.parseBackendKeyData(e,n,i);case 69:return this.parseErrorMessage(e,n,i,\"e\\\nrror\");case 78:return this.parseErrorMessage(e,n,i,\"notice\");case 84:return this.parseRowDescriptionMessage(\ne,n,i);case 116:return this.parseParameterDescriptionMessage(e,n,i);case 71:return this.parseCopyInMessage(\ne,n,i);case 72:return this.parseCopyOutMessage(e,n,i);case 100:return this.parseCopyData(e,n,i);default:\nreturn new M.DatabaseError(\"received invalid response: \"+t.toString(16),n,\"error\")}}parseReadyForQueryMessage(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.string(1);return new M.ReadyForQueryMessage(t,i)}parseCommandCompleteMessage(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.cstring();return new M.CommandCompleteMessage(t,i)}parseCopyData(e,t,n){\nlet i=n.slice(e,e+(t-4));return new M.CopyDataMessage(t,i)}parseCopyInMessage(e,t,n){return this.parseCopyMessage(\ne,t,n,\"copyInResponse\")}parseCopyOutMessage(e,t,n){return this.parseCopyMessage(e,t,n,\"copyOutRespon\\\nse\")}parseCopyMessage(e,t,n,i){this.reader.setBuffer(e,n);let s=this.reader.byte()!==0,o=this.reader.\nint16(),u=new M.CopyResponse(t,i,s,o);for(let c=0;c<o;c++)u.columnTypes[c]=this.reader.int16();return u}parseNotificationMessage(e,t,n){\nthis.reader.setBuffer(e,n);let i=this.reader.int32(),s=this.reader.cstring(),o=this.reader.cstring();\nreturn new M.NotificationResponseMessage(t,i,s,o)}parseRowDescriptionMessage(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int16(),s=new M.RowDescriptionMessage(t,i);for(let o=0;o<i;o++)s.fields[o]=this.\nparseField();return s}parseField(){let e=this.reader.cstring(),t=this.reader.uint32(),n=this.reader.\nint16(),i=this.reader.uint32(),s=this.reader.int16(),o=this.reader.int32(),u=this.reader.int16()===0?\n\"text\":\"binary\";return new M.Field(e,t,n,i,s,o,u)}parseParameterDescriptionMessage(e,t,n){this.reader.\nsetBuffer(e,n);let i=this.reader.int16(),s=new M.ParameterDescriptionMessage(t,i);for(let o=0;o<i;o++)\ns.dataTypeIDs[o]=this.reader.int32();return s}parseDataRowMessage(e,t,n){this.reader.setBuffer(e,n);\nlet i=this.reader.int16(),s=new Array(i);for(let o=0;o<i;o++){let u=this.reader.int32();s[o]=u===-1?\nnull:this.reader.string(u)}return new M.DataRowMessage(t,s)}parseParameterStatusMessage(e,t,n){this.\nreader.setBuffer(e,n);let i=this.reader.cstring(),s=this.reader.cstring();return new M.ParameterStatusMessage(\nt,i,s)}parseBackendKeyData(e,t,n){this.reader.setBuffer(e,n);let i=this.reader.int32(),s=this.reader.\nint32();return new M.BackendKeyDataMessage(t,i,s)}parseAuthenticationResponse(e,t,n){this.reader.setBuffer(\ne,n);let i=this.reader.int32(),s={name:\"authenticationOk\",length:t};switch(i){case 0:break;case 3:s.\nlength===8&&(s.name=\"authenticationCleartextPassword\");break;case 5:if(s.length===12){s.name=\"authen\\\nticationMD5Password\";let u=this.reader.bytes(4);return new M.AuthenticationMD5Password(t,u)}break;case 10:\ns.name=\"authenticationSASL\",s.mechanisms=[];let o;do o=this.reader.cstring(),o&&s.mechanisms.push(o);while(o);\nbreak;case 11:s.name=\"authenticationSASLContinue\",s.data=this.reader.string(t-8);break;case 12:s.name=\n\"authenticationSASLFinal\",s.data=this.reader.string(t-8);break;default:throw new Error(\"Unknown auth\\\nenticationOk message type \"+i)}return s}parseErrorMessage(e,t,n,i){this.reader.setBuffer(e,n);let s={},\no=this.reader.string(1);for(;o!==\"\\0\";)s[o]=this.reader.cstring(),o=this.reader.string(1);let u=s.M,\nc=i===\"notice\"?new M.NoticeMessage(t,u):new M.DatabaseError(u,t,i);return c.severity=s.S,c.code=s.C,\nc.detail=s.D,c.hint=s.H,c.position=s.P,c.internalPosition=s.p,c.internalQuery=s.q,c.where=s.W,c.schema=\ns.s,c.table=s.t,c.column=s.c,c.dataType=s.d,c.constraint=s.n,c.file=s.F,c.line=s.L,c.routine=s.R,c}};\na(mn,\"Parser\");var yn=mn;Ft.Parser=yn});var gn=I(ve=>{\"use strict\";p();Object.defineProperty(ve,\"__esModule\",{value:!0});ve.DatabaseError=ve.\nserialize=ve.parse=void 0;var qc=an();Object.defineProperty(ve,\"DatabaseError\",{enumerable:!0,get:a(\nfunction(){return qc.DatabaseError},\"get\")});var Qc=Qs();Object.defineProperty(ve,\"serialize\",{enumerable:!0,\nget:a(function(){return Qc.serialize},\"get\")});var Nc=Hs();function jc(r,e){let t=new Nc.Parser;return r.\non(\"data\",n=>t.parse(n,e)),new Promise(n=>r.on(\"end\",()=>n()))}a(jc,\"parse\");ve.parse=jc});var $s={};ne($s,{connect:()=>Wc});function Wc({socket:r,servername:e}){return r.startTls(e),r}var Gs=z(\n()=>{\"use strict\";p();a(Wc,\"connect\")});var vn=I((Gh,Ks)=>{\"use strict\";p();var Vs=(We(),D(yi)),Hc=me().EventEmitter,{parse:$c,serialize:q}=gn(),\nzs=q.flush(),Gc=q.sync(),Vc=q.end(),bn=class bn extends Hc{constructor(e){super(),e=e||{},this.stream=\ne.stream||new Vs.Socket,this._keepAlive=e.keepAlive,this._keepAliveInitialDelayMillis=e.keepAliveInitialDelayMillis,\nthis.lastBuffer=!1,this.parsedStatements={},this.ssl=e.ssl||!1,this._ending=!1,this._emitMessage=!1;\nvar t=this;this.on(\"newListener\",function(n){n===\"message\"&&(t._emitMessage=!0)})}connect(e,t){var n=this;\nthis._connecting=!0,this.stream.setNoDelay(!0),this.stream.connect(e,t),this.stream.once(\"connect\",function(){\nn._keepAlive&&n.stream.setKeepAlive(!0,n._keepAliveInitialDelayMillis),n.emit(\"connect\")});let i=a(function(s){\nn._ending&&(s.code===\"ECONNRESET\"||s.code===\"EPIPE\")||n.emit(\"error\",s)},\"reportStreamError\");if(this.\nstream.on(\"error\",i),this.stream.on(\"close\",function(){n.emit(\"end\")}),!this.ssl)return this.attachListeners(\nthis.stream);this.stream.once(\"data\",function(s){var o=s.toString(\"utf8\");switch(o){case\"S\":break;case\"\\\nN\":return n.stream.end(),n.emit(\"error\",new Error(\"The server does not support SSL connections\"));default:\nreturn n.stream.end(),n.emit(\"error\",new Error(\"There was an error establishing an SSL connection\"))}\nvar u=(Gs(),D($s));let c={socket:n.stream};n.ssl!==!0&&(Object.assign(c,n.ssl),\"key\"in n.ssl&&(c.key=\nn.ssl.key)),Vs.isIP(t)===0&&(c.servername=t);try{n.stream=u.connect(c)}catch(l){return n.emit(\"error\",\nl)}n.attachListeners(n.stream),n.stream.on(\"error\",i),n.emit(\"sslconnect\")})}attachListeners(e){e.on(\n\"end\",()=>{this.emit(\"end\")}),$c(e,t=>{var n=t.name===\"error\"?\"errorMessage\":t.name;this._emitMessage&&\nthis.emit(\"message\",t),this.emit(n,t)})}requestSsl(){this.stream.write(q.requestSsl())}startup(e){this.\nstream.write(q.startup(e))}cancel(e,t){this._send(q.cancel(e,t))}password(e){this._send(q.password(e))}sendSASLInitialResponseMessage(e,t){\nthis._send(q.sendSASLInitialResponseMessage(e,t))}sendSCRAMClientFinalMessage(e){this._send(q.sendSCRAMClientFinalMessage(\ne))}_send(e){return this.stream.writable?this.stream.write(e):!1}query(e){this._send(q.query(e))}parse(e){\nthis._send(q.parse(e))}bind(e){this._send(q.bind(e))}execute(e){this._send(q.execute(e))}flush(){this.\nstream.writable&&this.stream.write(zs)}sync(){this._ending=!0,this._send(zs),this._send(Gc)}ref(){this.\nstream.ref()}unref(){this.stream.unref()}end(){if(this._ending=!0,!this._connecting||!this.stream.writable){\nthis.stream.end();return}return this.stream.write(Vc,()=>{this.stream.end()})}close(e){this._send(q.\nclose(e))}describe(e){this._send(q.describe(e))}sendCopyFromChunk(e){this._send(q.copyData(e))}endCopyFrom(){\nthis._send(q.copyDone())}sendCopyFail(e){this._send(q.copyFail(e))}};a(bn,\"Connection\");var wn=bn;Ks.\nexports=wn});var Js=I((Yh,Zs)=>{\"use strict\";p();var zc=me().EventEmitter,Kh=(it(),D(nt)),Kc=rt(),xn=hs(),Yc=Es(),\nZc=St(),Jc=It(),Ys=Ds(),Xc=tt(),el=vn(),Sn=class Sn extends zc{constructor(e){super(),this.connectionParameters=\nnew Jc(e),this.user=this.connectionParameters.user,this.database=this.connectionParameters.database,\nthis.port=this.connectionParameters.port,this.host=this.connectionParameters.host,Object.defineProperty(\nthis,\"password\",{configurable:!0,enumerable:!1,writable:!0,value:this.connectionParameters.password}),\nthis.replication=this.connectionParameters.replication;var t=e||{};this._Promise=t.Promise||w.Promise,\nthis._types=new Zc(t.types),this._ending=!1,this._connecting=!1,this._connected=!1,this._connectionError=\n!1,this._queryable=!0,this.connection=t.connection||new el({stream:t.stream,ssl:this.connectionParameters.\nssl,keepAlive:t.keepAlive||!1,keepAliveInitialDelayMillis:t.keepAliveInitialDelayMillis||0,encoding:this.\nconnectionParameters.client_encoding||\"utf8\"}),this.queryQueue=[],this.binary=t.binary||Xc.binary,this.\nprocessID=null,this.secretKey=null,this.ssl=this.connectionParameters.ssl||!1,this.ssl&&this.ssl.key&&\nObject.defineProperty(this.ssl,\"key\",{enumerable:!1}),this._connectionTimeoutMillis=t.connectionTimeoutMillis||\n0}_errorAllQueries(e){let t=a(n=>{m.nextTick(()=>{n.handleError(e,this.connection)})},\"enqueueError\");\nthis.activeQuery&&(t(this.activeQuery),this.activeQuery=null),this.queryQueue.forEach(t),this.queryQueue.\nlength=0}_connect(e){var t=this,n=this.connection;if(this._connectionCallback=e,this._connecting||this.\n_connected){let i=new Error(\"Client has already been connected. You cannot reuse a client.\");m.nextTick(\n()=>{e(i)});return}this._connecting=!0,this.connectionTimeoutHandle,this._connectionTimeoutMillis>0&&\n(this.connectionTimeoutHandle=setTimeout(()=>{n._ending=!0,n.stream.destroy(new Error(\"timeout expir\\\ned\"))},this._connectionTimeoutMillis)),this.host&&this.host.indexOf(\"/\")===0?n.connect(this.host+\"/.\\\ns.PGSQL.\"+this.port):n.connect(this.port,this.host),n.on(\"connect\",function(){t.ssl?n.requestSsl():n.\nstartup(t.getStartupConf())}),n.on(\"sslconnect\",function(){n.startup(t.getStartupConf())}),this._attachListeners(\nn),n.once(\"end\",()=>{let i=this._ending?new Error(\"Connection terminated\"):new Error(\"Connection ter\\\nminated unexpectedly\");clearTimeout(this.connectionTimeoutHandle),this._errorAllQueries(i),this._ending||\n(this._connecting&&!this._connectionError?this._connectionCallback?this._connectionCallback(i):this.\n_handleErrorEvent(i):this._connectionError||this._handleErrorEvent(i)),m.nextTick(()=>{this.emit(\"en\\\nd\")})})}connect(e){if(e){this._connect(e);return}return new this._Promise((t,n)=>{this._connect(i=>{\ni?n(i):t()})})}_attachListeners(e){e.on(\"authenticationCleartextPassword\",this._handleAuthCleartextPassword.\nbind(this)),e.on(\"authenticationMD5Password\",this._handleAuthMD5Password.bind(this)),e.on(\"authentic\\\nationSASL\",this._handleAuthSASL.bind(this)),e.on(\"authenticationSASLContinue\",this._handleAuthSASLContinue.\nbind(this)),e.on(\"authenticationSASLFinal\",this._handleAuthSASLFinal.bind(this)),e.on(\"backendKeyDat\\\na\",this._handleBackendKeyData.bind(this)),e.on(\"error\",this._handleErrorEvent.bind(this)),e.on(\"erro\\\nrMessage\",this._handleErrorMessage.bind(this)),e.on(\"readyForQuery\",this._handleReadyForQuery.bind(this)),\ne.on(\"notice\",this._handleNotice.bind(this)),e.on(\"rowDescription\",this._handleRowDescription.bind(this)),\ne.on(\"dataRow\",this._handleDataRow.bind(this)),e.on(\"portalSuspended\",this._handlePortalSuspended.bind(\nthis)),e.on(\"emptyQuery\",this._handleEmptyQuery.bind(this)),e.on(\"commandComplete\",this._handleCommandComplete.\nbind(this)),e.on(\"parseComplete\",this._handleParseComplete.bind(this)),e.on(\"copyInResponse\",this._handleCopyInResponse.\nbind(this)),e.on(\"copyData\",this._handleCopyData.bind(this)),e.on(\"notification\",this._handleNotification.\nbind(this))}_checkPgPass(e){let t=this.connection;typeof this.password==\"function\"?this._Promise.resolve().\nthen(()=>this.password()).then(n=>{if(n!==void 0){if(typeof n!=\"string\"){t.emit(\"error\",new TypeError(\n\"Password must be a string\"));return}this.connectionParameters.password=this.password=n}else this.connectionParameters.\npassword=this.password=null;e()}).catch(n=>{t.emit(\"error\",n)}):this.password!==null?e():Yc(this.connectionParameters,\nn=>{n!==void 0&&(this.connectionParameters.password=this.password=n),e()})}_handleAuthCleartextPassword(e){\nthis._checkPgPass(()=>{this.connection.password(this.password)})}_handleAuthMD5Password(e){this._checkPgPass(\n()=>{let t=Kc.postgresMd5PasswordHash(this.user,this.password,e.salt);this.connection.password(t)})}_handleAuthSASL(e){\nthis._checkPgPass(()=>{this.saslSession=xn.startSession(e.mechanisms),this.connection.sendSASLInitialResponseMessage(\nthis.saslSession.mechanism,this.saslSession.response)})}_handleAuthSASLContinue(e){xn.continueSession(\nthis.saslSession,this.password,e.data),this.connection.sendSCRAMClientFinalMessage(this.saslSession.\nresponse)}_handleAuthSASLFinal(e){xn.finalizeSession(this.saslSession,e.data),this.saslSession=null}_handleBackendKeyData(e){\nthis.processID=e.processID,this.secretKey=e.secretKey}_handleReadyForQuery(e){this._connecting&&(this.\n_connecting=!1,this._connected=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback&&\n(this._connectionCallback(null,this),this._connectionCallback=null),this.emit(\"connect\"));let{activeQuery:t}=this;\nthis.activeQuery=null,this.readyForQuery=!0,t&&t.handleReadyForQuery(this.connection),this._pulseQueryQueue()}_handleErrorWhileConnecting(e){\nif(!this._connectionError){if(this._connectionError=!0,clearTimeout(this.connectionTimeoutHandle),this.\n_connectionCallback)return this._connectionCallback(e);this.emit(\"error\",e)}}_handleErrorEvent(e){if(this.\n_connecting)return this._handleErrorWhileConnecting(e);this._queryable=!1,this._errorAllQueries(e),this.\nemit(\"error\",e)}_handleErrorMessage(e){if(this._connecting)return this._handleErrorWhileConnecting(e);\nlet t=this.activeQuery;if(!t){this._handleErrorEvent(e);return}this.activeQuery=null,t.handleError(e,\nthis.connection)}_handleRowDescription(e){this.activeQuery.handleRowDescription(e)}_handleDataRow(e){\nthis.activeQuery.handleDataRow(e)}_handlePortalSuspended(e){this.activeQuery.handlePortalSuspended(this.\nconnection)}_handleEmptyQuery(e){this.activeQuery.handleEmptyQuery(this.connection)}_handleCommandComplete(e){\nthis.activeQuery.handleCommandComplete(e,this.connection)}_handleParseComplete(e){this.activeQuery.name&&\n(this.connection.parsedStatements[this.activeQuery.name]=this.activeQuery.text)}_handleCopyInResponse(e){\nthis.activeQuery.handleCopyInResponse(this.connection)}_handleCopyData(e){this.activeQuery.handleCopyData(\ne,this.connection)}_handleNotification(e){this.emit(\"notification\",e)}_handleNotice(e){this.emit(\"no\\\ntice\",e)}getStartupConf(){var e=this.connectionParameters,t={user:e.user,database:e.database},n=e.application_name||\ne.fallback_application_name;return n&&(t.application_name=n),e.replication&&(t.replication=\"\"+e.replication),\ne.statement_timeout&&(t.statement_timeout=String(parseInt(e.statement_timeout,10))),e.lock_timeout&&\n(t.lock_timeout=String(parseInt(e.lock_timeout,10))),e.idle_in_transaction_session_timeout&&(t.idle_in_transaction_session_timeout=\nString(parseInt(e.idle_in_transaction_session_timeout,10))),e.options&&(t.options=e.options),t}cancel(e,t){\nif(e.activeQuery===t){var n=this.connection;this.host&&this.host.indexOf(\"/\")===0?n.connect(this.host+\n\"/.s.PGSQL.\"+this.port):n.connect(this.port,this.host),n.on(\"connect\",function(){n.cancel(e.processID,\ne.secretKey)})}else e.queryQueue.indexOf(t)!==-1&&e.queryQueue.splice(e.queryQueue.indexOf(t),1)}setTypeParser(e,t,n){\nreturn this._types.setTypeParser(e,t,n)}getTypeParser(e,t){return this._types.getTypeParser(e,t)}escapeIdentifier(e){\nreturn'\"'+e.replace(/\"/g,'\"\"')+'\"'}escapeLiteral(e){for(var t=!1,n=\"'\",i=0;i<e.length;i++){var s=e[i];\ns===\"'\"?n+=s+s:s===\"\\\\\"?(n+=s+s,t=!0):n+=s}return n+=\"'\",t===!0&&(n=\" E\"+n),n}_pulseQueryQueue(){if(this.\nreadyForQuery===!0)if(this.activeQuery=this.queryQueue.shift(),this.activeQuery){this.readyForQuery=\n!1,this.hasExecuted=!0;let e=this.activeQuery.submit(this.connection);e&&m.nextTick(()=>{this.activeQuery.\nhandleError(e,this.connection),this.readyForQuery=!0,this._pulseQueryQueue()})}else this.hasExecuted&&\n(this.activeQuery=null,this.emit(\"drain\"))}query(e,t,n){var i,s,o,u,c;if(e==null)throw new TypeError(\n\"Client was passed a null or undefined query\");return typeof e.submit==\"function\"?(o=e.query_timeout||\nthis.connectionParameters.query_timeout,s=i=e,typeof t==\"function\"&&(i.callback=i.callback||t)):(o=this.\nconnectionParameters.query_timeout,i=new Ys(e,t,n),i.callback||(s=new this._Promise((l,f)=>{i.callback=\n(y,g)=>y?f(y):l(g)}))),o&&(c=i.callback,u=setTimeout(()=>{var l=new Error(\"Query read timeout\");m.nextTick(\n()=>{i.handleError(l,this.connection)}),c(l),i.callback=()=>{};var f=this.queryQueue.indexOf(i);f>-1&&\nthis.queryQueue.splice(f,1),this._pulseQueryQueue()},o),i.callback=(l,f)=>{clearTimeout(u),c(l,f)}),\nthis.binary&&!i.binary&&(i.binary=!0),i._result&&!i._result._types&&(i._result._types=this._types),this.\n_queryable?this._ending?(m.nextTick(()=>{i.handleError(new Error(\"Client was closed and is not query\\\nable\"),this.connection)}),s):(this.queryQueue.push(i),this._pulseQueryQueue(),s):(m.nextTick(()=>{i.\nhandleError(new Error(\"Client has encountered a connection error and is not queryable\"),this.connection)}),\ns)}ref(){this.connection.ref()}unref(){this.connection.unref()}end(e){if(this._ending=!0,!this.connection.\n_connecting)if(e)e();else return this._Promise.resolve();if(this.activeQuery||!this._queryable?this.\nconnection.stream.destroy():this.connection.end(),e)this.connection.once(\"end\",e);else return new this.\n_Promise(t=>{this.connection.once(\"end\",t)})}};a(Sn,\"Client\");var kt=Sn;kt.Query=Ys;Zs.exports=kt});var ro=I((Xh,to)=>{\"use strict\";p();var tl=me().EventEmitter,Xs=a(function(){},\"NOOP\"),eo=a((r,e)=>{\nlet t=r.findIndex(e);return t===-1?void 0:r.splice(t,1)[0]},\"removeWhere\"),Cn=class Cn{constructor(e,t,n){\nthis.client=e,this.idleListener=t,this.timeoutId=n}};a(Cn,\"IdleItem\");var En=Cn,_n=class _n{constructor(e){\nthis.callback=e}};a(_n,\"PendingItem\");var qe=_n;function rl(){throw new Error(\"Release called on cli\\\nent which has already been released to the pool.\")}a(rl,\"throwOnDoubleRelease\");function Mt(r,e){if(e)\nreturn{callback:e,result:void 0};let t,n,i=a(function(o,u){o?t(o):n(u)},\"cb\"),s=new r(function(o,u){\nn=o,t=u}).catch(o=>{throw Error.captureStackTrace(o),o});return{callback:i,result:s}}a(Mt,\"promisify\");\nfunction nl(r,e){return a(function t(n){n.client=e,e.removeListener(\"error\",t),e.on(\"error\",()=>{r.log(\n\"additional client error after disconnection due to error\",n)}),r._remove(e),r.emit(\"error\",n,e)},\"i\\\ndleListener\")}a(nl,\"makeIdleListener\");var Tn=class Tn extends tl{constructor(e,t){super(),this.options=\nObject.assign({},e),e!=null&&\"password\"in e&&Object.defineProperty(this.options,\"password\",{configurable:!0,\nenumerable:!1,writable:!0,value:e.password}),e!=null&&e.ssl&&e.ssl.key&&Object.defineProperty(this.options.\nssl,\"key\",{enumerable:!1}),this.options.max=this.options.max||this.options.poolSize||10,this.options.\nmaxUses=this.options.maxUses||1/0,this.options.allowExitOnIdle=this.options.allowExitOnIdle||!1,this.\noptions.maxLifetimeSeconds=this.options.maxLifetimeSeconds||0,this.log=this.options.log||function(){},\nthis.Client=this.options.Client||t||ot().Client,this.Promise=this.options.Promise||w.Promise,typeof this.\noptions.idleTimeoutMillis>\"u\"&&(this.options.idleTimeoutMillis=1e4),this._clients=[],this._idle=[],this.\n_expired=new WeakSet,this._pendingQueue=[],this._endCallback=void 0,this.ending=!1,this.ended=!1}_isFull(){\nreturn this._clients.length>=this.options.max}_pulseQueue(){if(this.log(\"pulse queue\"),this.ended){this.\nlog(\"pulse queue ended\");return}if(this.ending){this.log(\"pulse queue on ending\"),this._idle.length&&\nthis._idle.slice().map(t=>{this._remove(t.client)}),this._clients.length||(this.ended=!0,this._endCallback());\nreturn}if(!this._pendingQueue.length){this.log(\"no queued requests\");return}if(!this._idle.length&&this.\n_isFull())return;let e=this._pendingQueue.shift();if(this._idle.length){let t=this._idle.pop();clearTimeout(\nt.timeoutId);let n=t.client;n.ref&&n.ref();let i=t.idleListener;return this._acquireClient(n,e,i,!1)}\nif(!this._isFull())return this.newClient(e);throw new Error(\"unexpected condition\")}_remove(e){let t=eo(\nthis._idle,n=>n.client===e);t!==void 0&&clearTimeout(t.timeoutId),this._clients=this._clients.filter(\nn=>n!==e),e.end(),this.emit(\"remove\",e)}connect(e){if(this.ending){let i=new Error(\"Cannot use a poo\\\nl after calling end on the pool\");return e?e(i):this.Promise.reject(i)}let t=Mt(this.Promise,e),n=t.\nresult;if(this._isFull()||this._idle.length){if(this._idle.length&&m.nextTick(()=>this._pulseQueue()),\n!this.options.connectionTimeoutMillis)return this._pendingQueue.push(new qe(t.callback)),n;let i=a((u,c,l)=>{\nclearTimeout(o),t.callback(u,c,l)},\"queueCallback\"),s=new qe(i),o=setTimeout(()=>{eo(this._pendingQueue,\nu=>u.callback===i),s.timedOut=!0,t.callback(new Error(\"timeout exceeded when trying to connect\"))},this.\noptions.connectionTimeoutMillis);return o.unref&&o.unref(),this._pendingQueue.push(s),n}return this.\nnewClient(new qe(t.callback)),n}newClient(e){let t=new this.Client(this.options);this._clients.push(\nt);let n=nl(this,t);this.log(\"checking client timeout\");let i,s=!1;this.options.connectionTimeoutMillis&&\n(i=setTimeout(()=>{this.log(\"ending client due to timeout\"),s=!0,t.connection?t.connection.stream.destroy():\nt.end()},this.options.connectionTimeoutMillis)),this.log(\"connecting new client\"),t.connect(o=>{if(i&&\nclearTimeout(i),t.on(\"error\",n),o)this.log(\"client failed to connect\",o),this._clients=this._clients.\nfilter(u=>u!==t),s&&(o=new Error(\"Connection terminated due to connection timeout\",{cause:o})),this.\n_pulseQueue(),e.timedOut||e.callback(o,void 0,Xs);else{if(this.log(\"new client connected\"),this.options.\nmaxLifetimeSeconds!==0){let u=setTimeout(()=>{this.log(\"ending client due to expired lifetime\"),this.\n_expired.add(t),this._idle.findIndex(l=>l.client===t)!==-1&&this._acquireClient(t,new qe((l,f,y)=>y()),\nn,!1)},this.options.maxLifetimeSeconds*1e3);u.unref(),t.once(\"end\",()=>clearTimeout(u))}return this.\n_acquireClient(t,e,n,!0)}})}_acquireClient(e,t,n,i){i&&this.emit(\"connect\",e),this.emit(\"acquire\",e),\ne.release=this._releaseOnce(e,n),e.removeListener(\"error\",n),t.timedOut?i&&this.options.verify?this.\noptions.verify(e,e.release):e.release():i&&this.options.verify?this.options.verify(e,s=>{if(s)return e.\nrelease(s),t.callback(s,void 0,Xs);t.callback(void 0,e,e.release)}):t.callback(void 0,e,e.release)}_releaseOnce(e,t){\nlet n=!1;return i=>{n&&rl(),n=!0,this._release(e,t,i)}}_release(e,t,n){if(e.on(\"error\",t),e._poolUseCount=\n(e._poolUseCount||0)+1,this.emit(\"release\",n,e),n||this.ending||!e._queryable||e._ending||e._poolUseCount>=\nthis.options.maxUses){e._poolUseCount>=this.options.maxUses&&this.log(\"remove expended client\"),this.\n_remove(e),this._pulseQueue();return}if(this._expired.has(e)){this.log(\"remove expired client\"),this.\n_expired.delete(e),this._remove(e),this._pulseQueue();return}let s;this.options.idleTimeoutMillis&&(s=\nsetTimeout(()=>{this.log(\"remove idle client\"),this._remove(e)},this.options.idleTimeoutMillis),this.\noptions.allowExitOnIdle&&s.unref()),this.options.allowExitOnIdle&&e.unref(),this._idle.push(new En(e,\nt,s)),this._pulseQueue()}query(e,t,n){if(typeof e==\"function\"){let s=Mt(this.Promise,e);return b(function(){\nreturn s.callback(new Error(\"Passing a function as the first parameter to pool.query is not supporte\\\nd\"))}),s.result}typeof t==\"function\"&&(n=t,t=void 0);let i=Mt(this.Promise,n);return n=i.callback,this.\nconnect((s,o)=>{if(s)return n(s);let u=!1,c=a(l=>{u||(u=!0,o.release(l),n(l))},\"onError\");o.once(\"er\\\nror\",c),this.log(\"dispatching query\");try{o.query(e,t,(l,f)=>{if(this.log(\"query dispatched\"),o.removeListener(\n\"error\",c),!u)return u=!0,o.release(l),l?n(l):n(void 0,f)})}catch(l){return o.release(l),n(l)}}),i.result}end(e){\nif(this.log(\"ending\"),this.ending){let n=new Error(\"Called end on pool more than once\");return e?e(n):\nthis.Promise.reject(n)}this.ending=!0;let t=Mt(this.Promise,e);return this._endCallback=t.callback,this.\n_pulseQueue(),t.result}get waitingCount(){return this._pendingQueue.length}get idleCount(){return this.\n_idle.length}get expiredCount(){return this._clients.reduce((e,t)=>e+(this._expired.has(t)?1:0),0)}get totalCount(){\nreturn this._clients.length}};a(Tn,\"Pool\");var An=Tn;to.exports=An});var no={};ne(no,{default:()=>il});var il,io=z(()=>{\"use strict\";p();il={}});var so=I((np,sl)=>{sl.exports={name:\"pg\",version:\"8.8.0\",description:\"PostgreSQL client - pure javas\\\ncript & libpq with the same API\",keywords:[\"database\",\"libpq\",\"pg\",\"postgre\",\"postgres\",\"postgresql\",\n\"rdbms\"],homepage:\"https://github.com/brianc/node-postgres\",repository:{type:\"git\",url:\"git://github\\\n.com/brianc/node-postgres.git\",directory:\"packages/pg\"},author:\"Brian Carlson <brian.m.carlson@gmail\\\n.com>\",main:\"./lib\",dependencies:{\"buffer-writer\":\"2.0.0\",\"packet-reader\":\"1.0.0\",\"pg-connection-str\\\ning\":\"^2.5.0\",\"pg-pool\":\"^3.5.2\",\"pg-protocol\":\"^1.5.0\",\"pg-types\":\"^2.1.0\",pgpass:\"1.x\"},devDependencies:{\nasync:\"2.6.4\",bluebird:\"3.5.2\",co:\"4.6.0\",\"pg-copy-streams\":\"0.3.0\"},peerDependencies:{\"pg-native\":\"\\\n>=3.0.1\"},peerDependenciesMeta:{\"pg-native\":{optional:!0}},scripts:{test:\"make test-all\"},files:[\"li\\\nb\",\"SPONSORS.md\"],license:\"MIT\",engines:{node:\">= 8.0.0\"},gitHead:\"c99fb2c127ddf8d712500db2c7b9a5491\\\na178655\"}});var uo=I((ip,ao)=>{\"use strict\";p();var oo=me().EventEmitter,ol=(it(),D(nt)),In=rt(),Qe=ao.exports=function(r,e,t){\noo.call(this),r=In.normalizeQueryConfig(r,e,t),this.text=r.text,this.values=r.values,this.name=r.name,\nthis.callback=r.callback,this.state=\"new\",this._arrayMode=r.rowMode===\"array\",this._emitRowEvents=!1,\nthis.on(\"newListener\",function(n){n===\"row\"&&(this._emitRowEvents=!0)}.bind(this))};ol.inherits(Qe,oo);\nvar al={sqlState:\"code\",statementPosition:\"position\",messagePrimary:\"message\",context:\"where\",schemaName:\"\\\nschema\",tableName:\"table\",columnName:\"column\",dataTypeName:\"dataType\",constraintName:\"constraint\",sourceFile:\"\\\nfile\",sourceLine:\"line\",sourceFunction:\"routine\"};Qe.prototype.handleError=function(r){var e=this.native.\npq.resultErrorFields();if(e)for(var t in e){var n=al[t]||t;r[n]=e[t]}this.callback?this.callback(r):\nthis.emit(\"error\",r),this.state=\"error\"};Qe.prototype.then=function(r,e){return this._getPromise().then(\nr,e)};Qe.prototype.catch=function(r){return this._getPromise().catch(r)};Qe.prototype._getPromise=function(){\nreturn this._promise?this._promise:(this._promise=new Promise(function(r,e){this._once(\"end\",r),this.\n_once(\"error\",e)}.bind(this)),this._promise)};Qe.prototype.submit=function(r){this.state=\"running\";var e=this;\nthis.native=r.native,r.native.arrayMode=this._arrayMode;var t=a(function(s,o,u){if(r.native.arrayMode=\n!1,b(function(){e.emit(\"_done\")}),s)return e.handleError(s);e._emitRowEvents&&(u.length>1?o.forEach(\n(c,l)=>{c.forEach(f=>{e.emit(\"row\",f,u[l])})}):o.forEach(function(c){e.emit(\"row\",c,u)})),e.state=\"e\\\nnd\",e.emit(\"end\",u),e.callback&&e.callback(null,u)},\"after\");if(m.domain&&(t=m.domain.bind(t)),this.\nname){this.name.length>63&&(console.error(\"Warning! Postgres only supports 63 characters for query n\\\names.\"),console.error(\"You supplied %s (%s)\",this.name,this.name.length),console.error(\"This can cau\\\nse conflicts and silent errors executing queries\"));var n=(this.values||[]).map(In.prepareValue);if(r.\nnamedQueries[this.name]){if(this.text&&r.namedQueries[this.name]!==this.text){let s=new Error(`Prepa\\\nred statements must be unique - '${this.name}' was used for a different statement`);return t(s)}return r.\nnative.execute(this.name,n,t)}return r.native.prepare(this.name,this.text,n.length,function(s){return s?\nt(s):(r.namedQueries[e.name]=e.text,e.native.execute(e.name,n,t))})}else if(this.values){if(!Array.isArray(\nthis.values)){let s=new Error(\"Query values must be an array\");return t(s)}var i=this.values.map(In.\nprepareValue);r.native.query(this.text,i,t)}else r.native.query(this.text,t)}});var ho=I((up,fo)=>{\"use strict\";p();var ul=(io(),D(no)),cl=St(),ap=so(),co=me().EventEmitter,ll=(it(),D(nt)),\nfl=It(),lo=uo(),Z=fo.exports=function(r){co.call(this),r=r||{},this._Promise=r.Promise||w.Promise,this.\n_types=new cl(r.types),this.native=new ul({types:this._types}),this._queryQueue=[],this._ending=!1,this.\n_connecting=!1,this._connected=!1,this._queryable=!0;var e=this.connectionParameters=new fl(r);this.\nuser=e.user,Object.defineProperty(this,\"password\",{configurable:!0,enumerable:!1,writable:!0,value:e.\npassword}),this.database=e.database,this.host=e.host,this.port=e.port,this.namedQueries={}};Z.Query=\nlo;ll.inherits(Z,co);Z.prototype._errorAllQueries=function(r){let e=a(t=>{m.nextTick(()=>{t.native=this.\nnative,t.handleError(r)})},\"enqueueError\");this._hasActiveQuery()&&(e(this._activeQuery),this._activeQuery=\nnull),this._queryQueue.forEach(e),this._queryQueue.length=0};Z.prototype._connect=function(r){var e=this;\nif(this._connecting){m.nextTick(()=>r(new Error(\"Client has already been connected. You cannot reuse\\\n a client.\")));return}this._connecting=!0,this.connectionParameters.getLibpqConnectionString(function(t,n){\nif(t)return r(t);e.native.connect(n,function(i){if(i)return e.native.end(),r(i);e._connected=!0,e.native.\non(\"error\",function(s){e._queryable=!1,e._errorAllQueries(s),e.emit(\"error\",s)}),e.native.on(\"notifi\\\ncation\",function(s){e.emit(\"notification\",{channel:s.relname,payload:s.extra})}),e.emit(\"connect\"),e.\n_pulseQueryQueue(!0),r()})})};Z.prototype.connect=function(r){if(r){this._connect(r);return}return new this.\n_Promise((e,t)=>{this._connect(n=>{n?t(n):e()})})};Z.prototype.query=function(r,e,t){var n,i,s,o,u;if(r==\nnull)throw new TypeError(\"Client was passed a null or undefined query\");if(typeof r.submit==\"functio\\\nn\")s=r.query_timeout||this.connectionParameters.query_timeout,i=n=r,typeof e==\"function\"&&(r.callback=\ne);else if(s=this.connectionParameters.query_timeout,n=new lo(r,e,t),!n.callback){let c,l;i=new this.\n_Promise((f,y)=>{c=f,l=y}),n.callback=(f,y)=>f?l(f):c(y)}return s&&(u=n.callback,o=setTimeout(()=>{var c=new Error(\n\"Query read timeout\");m.nextTick(()=>{n.handleError(c,this.connection)}),u(c),n.callback=()=>{};var l=this.\n_queryQueue.indexOf(n);l>-1&&this._queryQueue.splice(l,1),this._pulseQueryQueue()},s),n.callback=(c,l)=>{\nclearTimeout(o),u(c,l)}),this._queryable?this._ending?(n.native=this.native,m.nextTick(()=>{n.handleError(\nnew Error(\"Client was closed and is not queryable\"))}),i):(this._queryQueue.push(n),this._pulseQueryQueue(),\ni):(n.native=this.native,m.nextTick(()=>{n.handleError(new Error(\"Client has encountered a connectio\\\nn error and is not queryable\"))}),i)};Z.prototype.end=function(r){var e=this;this._ending=!0,this._connected||\nthis.once(\"connect\",this.end.bind(this,r));var t;return r||(t=new this._Promise(function(n,i){r=a(s=>s?\ni(s):n(),\"cb\")})),this.native.end(function(){e._errorAllQueries(new Error(\"Connection terminated\")),\nm.nextTick(()=>{e.emit(\"end\"),r&&r()})}),t};Z.prototype._hasActiveQuery=function(){return this._activeQuery&&\nthis._activeQuery.state!==\"error\"&&this._activeQuery.state!==\"end\"};Z.prototype._pulseQueryQueue=function(r){\nif(this._connected&&!this._hasActiveQuery()){var e=this._queryQueue.shift();if(!e){r||this.emit(\"dra\\\nin\");return}this._activeQuery=e,e.submit(this);var t=this;e.once(\"_done\",function(){t._pulseQueryQueue()})}};\nZ.prototype.cancel=function(r){this._activeQuery===r?this.native.cancel(function(){}):this._queryQueue.\nindexOf(r)!==-1&&this._queryQueue.splice(this._queryQueue.indexOf(r),1)};Z.prototype.ref=function(){};\nZ.prototype.unref=function(){};Z.prototype.setTypeParser=function(r,e,t){return this._types.setTypeParser(\nr,e,t)};Z.prototype.getTypeParser=function(r,e){return this._types.getTypeParser(r,e)}});var Pn=I((fp,po)=>{\"use strict\";p();po.exports=ho()});var ot=I((pp,at)=>{\"use strict\";p();var hl=Js(),pl=tt(),dl=vn(),yl=ro(),{DatabaseError:ml}=gn(),gl=a(\nr=>{var e;return e=class extends yl{constructor(n){super(n,r)}},a(e,\"BoundPool\"),e},\"poolFactory\"),Rn=a(\nfunction(r){this.defaults=pl,this.Client=r,this.Query=this.Client.Query,this.Pool=gl(this.Client),this.\n_pools=[],this.Connection=dl,this.types=Je(),this.DatabaseError=ml},\"PG\");typeof m.env.NODE_PG_FORCE_NATIVE<\n\"u\"?at.exports=new Rn(Pn()):(at.exports=new Rn(hl),Object.defineProperty(at.exports,\"native\",{configurable:!0,\nenumerable:!1,get(){var r=null;try{r=new Rn(Pn())}catch(e){if(e.code!==\"MODULE_NOT_FOUND\")throw e}return Object.\ndefineProperty(at.exports,\"native\",{value:r}),r}}))});p();p();We();zt();p();var fa=Object.defineProperty,ha=Object.defineProperties,pa=Object.getOwnPropertyDescriptors,gi=Object.\ngetOwnPropertySymbols,da=Object.prototype.hasOwnProperty,ya=Object.prototype.propertyIsEnumerable,wi=a(\n(r,e,t)=>e in r?fa(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,\"__defNormalProp\"),\nma=a((r,e)=>{for(var t in e||(e={}))da.call(e,t)&&wi(r,t,e[t]);if(gi)for(var t of gi(e))ya.call(e,t)&&\nwi(r,t,e[t]);return r},\"__spreadValues\"),ga=a((r,e)=>ha(r,pa(e)),\"__spreadProps\"),wa=1008e3,bi=new Uint8Array(\nnew Uint16Array([258]).buffer)[0]===2,ba=new TextDecoder,Kt=new TextEncoder,dt=Kt.encode(\"0123456789\\\nabcdef\"),yt=Kt.encode(\"0123456789ABCDEF\"),va=Kt.encode(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqr\\\nstuvwxyz0123456789+/\");var vi=va.slice();vi[62]=45;vi[63]=95;var He,mt;function xa(r,{alphabet:e,scratchArr:t}={}){if(!He)if(He=\nnew Uint16Array(256),mt=new Uint16Array(256),bi)for(let C=0;C<256;C++)He[C]=dt[C&15]<<8|dt[C>>>4],mt[C]=\nyt[C&15]<<8|yt[C>>>4];else for(let C=0;C<256;C++)He[C]=dt[C&15]|dt[C>>>4]<<8,mt[C]=yt[C&15]|yt[C>>>4]<<\n8;r.byteOffset%4!==0&&(r=new Uint8Array(r));let n=r.length,i=n>>>1,s=n>>>2,o=t||new Uint16Array(n),u=new Uint32Array(\nr.buffer,r.byteOffset,s),c=new Uint32Array(o.buffer,o.byteOffset,i),l=e===\"upper\"?mt:He,f=0,y=0,g;if(bi)\nfor(;f<s;)g=u[f++],c[y++]=l[g>>>8&255]<<16|l[g&255],c[y++]=l[g>>>24]<<16|l[g>>>16&255];else for(;f<s;)\ng=u[f++],c[y++]=l[g>>>24]<<16|l[g>>>16&255],c[y++]=l[g>>>8&255]<<16|l[g&255];for(f<<=2;f<n;)o[f]=l[r[f++]];\nreturn ba.decode(o.subarray(0,n))}a(xa,\"_toHex\");function Sa(r,e={}){let t=\"\",n=r.length,i=wa>>>1,s=Math.\nceil(n/i),o=new Uint16Array(s>1?i:n);for(let u=0;u<s;u++){let c=u*i,l=c+i;t+=xa(r.subarray(c,l),ga(ma(\n{},e),{scratchArr:o}))}return t}a(Sa,\"_toHexChunked\");function xi(r,e={}){return e.alphabet!==\"upper\"&&\ntypeof r.toHex==\"function\"?r.toHex():Sa(r,e)}a(xi,\"toHex\");p();var gt=class gt{constructor(e,t){this.strings=e;this.values=t}toParameterizedQuery(e={query:\"\",params:[]}){\nlet{strings:t,values:n}=this;for(let i=0,s=t.length;i<s;i++)if(e.query+=t[i],i<n.length){let o=n[i];\nif(o instanceof Ge)e.query+=o.sql;else if(o instanceof Ae)if(o.queryData instanceof gt)o.queryData.toParameterizedQuery(\ne);else{if(o.queryData.params?.length)throw new Error(\"This query is not composable\");e.query+=o.queryData.\nquery}else{let{params:u}=e;u.push(o),e.query+=\"$\"+u.length,(o instanceof d||ArrayBuffer.isView(o))&&\n(e.query+=\"::bytea\")}}return e}};a(gt,\"SqlTemplate\");var $e=gt,Yt=class Yt{constructor(e){this.sql=e}};\na(Yt,\"UnsafeRawSql\");var Ge=Yt;var ss=xe(St()),os=xe(rt());var At=class At extends Error{constructor(t){super(t);E(this,\"name\",\"NeonDbError\");E(this,\"severity\");\nE(this,\"code\");E(this,\"detail\");E(this,\"hint\");E(this,\"position\");E(this,\"internalPosition\");E(this,\n\"internalQuery\");E(this,\"where\");E(this,\"schema\");E(this,\"table\");E(this,\"column\");E(this,\"dataType\");\nE(this,\"constraint\");E(this,\"file\");E(this,\"line\");E(this,\"routine\");E(this,\"sourceError\");\"captureS\\\ntackTrace\"in Error&&typeof Error.captureStackTrace==\"function\"&&Error.captureStackTrace(this,At)}};a(\nAt,\"NeonDbError\");var we=At,rs=\"transaction() expects an array of queries, or a function returning a\\\nn array of queries\",Pu=[\"severity\",\"code\",\"detail\",\"hint\",\"position\",\"internalPosition\",\"internalQue\\\nry\",\"where\",\"schema\",\"table\",\"column\",\"dataType\",\"constraint\",\"file\",\"line\",\"routine\"];function Ru(r){\nreturn r instanceof d?\"\\\\x\"+xi(r):r}a(Ru,\"encodeBuffersAsBytea\");function ns(r){let{query:e,params:t}=r instanceof\n$e?r.toParameterizedQuery():r;return{query:e,params:t.map(n=>Ru((0,os.prepareValue)(n)))}}a(ns,\"prep\\\nareQuery\");function as(r,{arrayMode:e,fullResults:t,fetchOptions:n,isolationLevel:i,readOnly:s,deferrable:o,\nauthToken:u}={}){if(!r)throw new Error(\"No database connection string was provided to `neon()`. Perh\\\naps an environment variable has not been set?\");let c;try{c=Vt(r)}catch{throw new Error(\"Database co\\\nnnection string provided to `neon()` is not a valid URL. Connection string: \"+String(r))}let{protocol:l,\nusername:f,hostname:y,port:g,pathname:A}=c;if(l!==\"postgres:\"&&l!==\"postgresql:\"||!f||!y||!A)throw new Error(\n\"Database connection string format for `neon()` should be: postgresql://user:<EMAIL>/dbnam\\\ne?option=value\");function C(P,...L){if(!(Array.isArray(P)&&Array.isArray(P.raw)&&Array.isArray(L)))throw new Error(\n'This function can now be called only as a tagged-template function: sql`SELECT ${value}`, not sql(\"\\\nSELECT $1\", [value], options). For a conventional function call with value placeholders ($1, $2, etc\\\n.), use sql.query(\"SELECT $1\", [value], options).');return new Ae(Q,new $e(P,L))}a(C,\"templateFn\"),C.\nquery=(P,L,_)=>new Ae(Q,{query:P,params:L??[]},_),C.unsafe=P=>new Ge(P),C.transaction=async(P,L)=>{if(typeof P==\n\"function\"&&(P=P(C)),!Array.isArray(P))throw new Error(rs);P.forEach(H=>{if(!(H instanceof Ae))throw new Error(\nrs)});let _=P.map(H=>H.queryData),x=P.map(H=>H.opts??{});return Q(_,x,L)};async function Q(P,L,_){let{\nfetchEndpoint:x,fetchFunction:H}=ge,le=Array.isArray(P)?{queries:P.map(J=>ns(J))}:ns(P),N=n??{},ie=e??\n!1,se=t??!1,oe=i,B=s,$=o;_!==void 0&&(_.fetchOptions!==void 0&&(N={...N,..._.fetchOptions}),_.arrayMode!==\nvoid 0&&(ie=_.arrayMode),_.fullResults!==void 0&&(se=_.fullResults),_.isolationLevel!==void 0&&(oe=_.\nisolationLevel),_.readOnly!==void 0&&(B=_.readOnly),_.deferrable!==void 0&&($=_.deferrable)),L!==void 0&&\n!Array.isArray(L)&&L.fetchOptions!==void 0&&(N={...N,...L.fetchOptions});let fe=u;!Array.isArray(L)&&\nL?.authToken!==void 0&&(fe=L.authToken);let Ce=typeof x==\"function\"?x(y,g,{jwtAuth:fe!==void 0}):x,he={\n\"Neon-Connection-String\":r,\"Neon-Raw-Text-Output\":\"true\",\"Neon-Array-Mode\":\"true\"},_e=await Bu(fe);_e&&\n(he.Authorization=`Bearer ${_e}`),Array.isArray(P)&&(oe!==void 0&&(he[\"Neon-Batch-Isolation-Level\"]=\noe),B!==void 0&&(he[\"Neon-Batch-Read-Only\"]=String(B)),$!==void 0&&(he[\"Neon-Batch-Deferrable\"]=String(\n$)));let ae;try{ae=await(H??fetch)(Ce,{method:\"POST\",body:JSON.stringify(le),headers:he,...N})}catch(J){\nlet j=new we(`Error connecting to database: ${J}`);throw j.sourceError=J,j}if(ae.ok){let J=await ae.\njson();if(Array.isArray(P)){let j=J.results;if(!Array.isArray(j))throw new we(\"Neon internal error: \\\nunexpected result format\");return j.map((X,V)=>{let Ne=L[V]??{},wo=Ne.arrayMode??ie,bo=Ne.fullResults??\nse;return is(X,{arrayMode:wo,fullResults:bo,types:Ne.types})})}else{let j=L??{},X=j.arrayMode??ie,V=j.\nfullResults??se;return is(J,{arrayMode:X,fullResults:V,types:j.types})}}else{let{status:J}=ae;if(J===\n400){let j=await ae.json(),X=new we(j.message);for(let V of Pu)X[V]=j[V]??void 0;throw X}else{let j=await ae.\ntext();throw new we(`Server error (HTTP status ${J}): ${j}`)}}}return a(Q,\"execute\"),C}a(as,\"neon\");\nvar fr=class fr{constructor(e,t,n){this.execute=e;this.queryData=t;this.opts=n}then(e,t){return this.\nexecute(this.queryData,this.opts).then(e,t)}catch(e){return this.execute(this.queryData,this.opts).catch(\ne)}finally(e){return this.execute(this.queryData,this.opts).finally(e)}};a(fr,\"NeonQueryPromise\");var Ae=fr;\nfunction is(r,{arrayMode:e,fullResults:t,types:n}){let i=new ss.default(n),s=r.fields.map(c=>c.name),\no=r.fields.map(c=>i.getTypeParser(c.dataTypeID)),u=e===!0?r.rows.map(c=>c.map((l,f)=>l===null?null:o[f](\nl))):r.rows.map(c=>Object.fromEntries(c.map((l,f)=>[s[f],l===null?null:o[f](l)])));return t?(r.viaNeonFetch=\n!0,r.rowAsArray=e,r.rows=u,r._parsers=o,r._types=i,r):u}a(is,\"processQueryResult\");async function Bu(r){\nif(typeof r==\"string\")return r;if(typeof r==\"function\")try{return await Promise.resolve(r())}catch(e){\nlet t=new we(\"Error getting auth token.\");throw e instanceof Error&&(t=new we(`Error getting auth to\\\nken: ${e.message}`)),t}}a(Bu,\"getAuthToken\");p();var mo=xe(ot());p();var yo=xe(ot());var Bn=class Bn extends yo.Client{constructor(t){super(t);this.config=t}get neonConfig(){return this.\nconnection.stream}connect(t){let{neonConfig:n}=this;n.forceDisablePgSSL&&(this.ssl=this.connection.ssl=\n!1),this.ssl&&n.useSecureWebSocket&&console.warn(\"SSL is enabled for both Postgres (e.g. ?sslmode=re\\\nquire in the connection string + forceDisablePgSSL = false) and the WebSocket tunnel (useSecureWebSo\\\ncket = true). Double encryption will increase latency and CPU usage. It may be appropriate to disabl\\\ne SSL in the Postgres connection parameters or set forceDisablePgSSL = true.\");let i=typeof this.config!=\n\"string\"&&this.config?.host!==void 0||typeof this.config!=\"string\"&&this.config?.connectionString!==\nvoid 0||m.env.PGHOST!==void 0,s=m.env.USER??m.env.USERNAME;if(!i&&this.host===\"localhost\"&&this.user===\ns&&this.database===s&&this.password===null)throw new Error(`No database host or connection string wa\\\ns set, and key parameters have default values (host: localhost, user: ${s}, db: ${s}, password: null\\\n). Is an environment variable missing? Alternatively, if you intended to connect with these paramete\\\nrs, please set the host to 'localhost' explicitly.`);let o=super.connect(t),u=n.pipelineTLS&&this.ssl,\nc=n.pipelineConnect===\"password\";if(!u&&!n.pipelineConnect)return o;let l=this.connection;if(u&&l.on(\n\"connect\",()=>l.stream.emit(\"data\",\"S\")),c){l.removeAllListeners(\"authenticationCleartextPassword\"),\nl.removeAllListeners(\"readyForQuery\"),l.once(\"readyForQuery\",()=>l.on(\"readyForQuery\",this._handleReadyForQuery.\nbind(this)));let f=this.ssl?\"sslconnect\":\"connect\";l.on(f,()=>{this._handleAuthCleartextPassword(),this.\n_handleReadyForQuery()})}return o}async _handleAuthSASLContinue(t){if(typeof crypto>\"u\"||crypto.subtle===\nvoid 0||crypto.subtle.importKey===void 0)throw new Error(\"Cannot use SASL auth when `crypto.subtle` \\\nis not defined\");let n=crypto.subtle,i=this.saslSession,s=this.password,o=t.data;if(i.message!==\"SAS\\\nLInitialResponse\"||typeof s!=\"string\"||typeof o!=\"string\")throw new Error(\"SASL: protocol error\");let u=Object.\nfromEntries(o.split(\",\").map(X=>{if(!/^.=/.test(X))throw new Error(\"SASL: Invalid attribute pair ent\\\nry\");let V=X[0],Ne=X.substring(2);return[V,Ne]})),c=u.r,l=u.s,f=u.i;if(!c||!/^[!-+--~]+$/.test(c))throw new Error(\n\"SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing/unprintable\");if(!l||!/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.\ntest(l))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing/not base64\");if(!f||!/^[1-9][0-9]*$/.\ntest(f))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: missing/invalid iteration count\");if(!c.startsWith(\ni.clientNonce))throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with c\\\nlient nonce\");if(c.length===i.clientNonce.length)throw new Error(\"SASL: SCRAM-SERVER-FIRST-MESSAGE: \\\nserver nonce is too short\");let y=parseInt(f,10),g=d.from(l,\"base64\"),A=new TextEncoder,C=A.encode(s),\nQ=await n.importKey(\"raw\",C,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]),P=new Uint8Array(await n.\nsign(\"HMAC\",Q,d.concat([g,d.from([0,0,0,1])]))),L=P;for(var _=0;_<y-1;_++)P=new Uint8Array(await n.sign(\n\"HMAC\",Q,P)),L=d.from(L.map((X,V)=>L[V]^P[V]));let x=L,H=await n.importKey(\"raw\",x,{name:\"HMAC\",hash:{\nname:\"SHA-256\"}},!1,[\"sign\"]),le=new Uint8Array(await n.sign(\"HMAC\",H,A.encode(\"Client Key\"))),N=await n.\ndigest(\"SHA-256\",le),ie=\"n=*,r=\"+i.clientNonce,se=\"r=\"+c+\",s=\"+l+\",i=\"+y,oe=\"c=biws,r=\"+c,B=ie+\",\"+se+\n\",\"+oe,$=await n.importKey(\"raw\",N,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]);var fe=new Uint8Array(\nawait n.sign(\"HMAC\",$,A.encode(B))),Ce=d.from(le.map((X,V)=>le[V]^fe[V])),he=Ce.toString(\"base64\");let _e=await n.\nimportKey(\"raw\",x,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]),ae=await n.sign(\"HMAC\",_e,A.encode(\n\"Server Key\")),J=await n.importKey(\"raw\",ae,{name:\"HMAC\",hash:{name:\"SHA-256\"}},!1,[\"sign\"]);var j=d.\nfrom(await n.sign(\"HMAC\",J,A.encode(B)));i.message=\"SASLResponse\",i.serverSignature=j.toString(\"base\\\n64\"),i.response=oe+\",p=\"+he,this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}};\na(Bn,\"NeonClient\");var ut=Bn;We();var go=xe(It());function wl(r,e){if(e)return{callback:e,result:void 0};let t,n,i=a(function(o,u){o?t(o):n(u)},\"cb\"),\ns=new r(function(o,u){n=o,t=u});return{callback:i,result:s}}a(wl,\"promisify\");var Fn=class Fn extends mo.Pool{constructor(){\nsuper(...arguments);E(this,\"Client\",ut);E(this,\"hasFetchUnsupportedListeners\",!1);E(this,\"addListene\\\nr\",this.on)}on(t,n){return t!==\"error\"&&(this.hasFetchUnsupportedListeners=!0),super.on(t,n)}query(t,n,i){\nif(!ge.poolQueryViaFetch||this.hasFetchUnsupportedListeners||typeof t==\"function\")return super.query(\nt,n,i);typeof n==\"function\"&&(i=n,n=void 0);let s=wl(this.Promise,i);i=s.callback;try{let o=new go.default(\nthis.options),u=encodeURIComponent,c=encodeURI,l=`postgresql://${u(o.user)}:${u(o.password)}@${u(o.host)}\\\n/${c(o.database)}`,f=typeof t==\"string\"?t:t.text,y=n??t.values??[];as(l,{fullResults:!0,arrayMode:t.\nrowMode===\"array\"}).query(f,y,{types:t.types??this.options?.types}).then(A=>i(void 0,A)).catch(A=>i(\nA))}catch(o){i(o)}return s.result}};a(Fn,\"NeonPool\");var Ln=Fn;We();var kn=xe(ot()),_p=\"mjs\";var export_DatabaseError=kn.DatabaseError;var export_defaults=kn.defaults;var export_types=kn.types;\n\n/*! Bundled license information:\n\nieee754/index.js:\n  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)\n\nbuffer/index.js:\n  (*!\n   * The buffer module from node.js, for the browser.\n   *\n   * <AUTHOR> Aboukhadijeh <https://feross.org>\n   * @license  MIT\n   *)\n*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG5lb25kYXRhYmFzZS9zZXJ2ZXJsZXNzL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxQkFBcUIsNkJBQTZCLHVDQUF1QyxrQ0FBa0MsZ0VBQWdFLCtCQUErQixrREFBa0QsU0FBUywwQkFBMEIsd0JBQXdCLEVBQUUsbUNBQW1DLDJCQUEyQixXQUFXLG1DQUFtQyx1QkFBdUI7QUFDNWMsY0FBYyxFQUFFLGdCQUFnQjtBQUNoQywwQkFBMEIsbURBQW1ELEVBQUUsVUFBVSx1Q0FBdUMseUNBQXlDLHNCQUFzQjtBQUMvTCxtQkFBbUIsZUFBZSxTQUFTLEtBQUssaURBQWlELGNBQWMsYUFBYSxJQUFJLGlCQUFpQixrQkFBa0Isb0JBQW9CO0FBQ3ZMO0FBQ0EsWUFBWSxzQkFBc0IsTUFBTSw0Q0FBNEMsVUFBVTtBQUM5RixHQUFHLFVBQVUsZUFBZSxlQUFlO0FBQzNDLGtCQUFrQixxQkFBcUIsY0FBYyxvQkFBb0IsWUFBWTtBQUNyRixlQUFlLDBCQUEwQixrQkFBa0IsbUJBQW1CLG1CQUFtQjtBQUNqRyxTQUFTLG9CQUFvQixlQUFlO0FBQzVDLFVBQVUsUUFBUSxJQUFJO0FBQ3RCLHNFQUFzRTtBQUN0RTtBQUNBLDJEQUEyRCxvQkFBb0IsZUFBZTtBQUM5Rix5Q0FBeUMsd0JBQXdCLG1CQUFtQixtQkFBbUI7QUFDdkcsRUFBRSx3RUFBd0Usa0JBQWtCO0FBQzVGLFlBQVksZUFBZSxrREFBa0QsSUFBSTtBQUNqRixjQUFjO0FBQ2QsMERBQTBELHNCQUFzQixFQUFFLGNBQWMsSUFBSSw0QkFBNEI7QUFDaEksY0FBYyxtQ0FBbUMsSUFBSSwwQkFBMEI7QUFDL0UsUUFBUSxJQUFJLDBCQUEwQixlQUFlLEtBQUsscUNBQXFDO0FBQy9GLGVBQWUsa0NBQWtDLCtCQUErQjtBQUNoRjtBQUNBLElBQUk7QUFDSjtBQUNBLGdGQUFnRixLQUFLO0FBQ3JGLGVBQWUsa0JBQWtCLElBQUksK0JBQStCLGlCQUFpQixFQUFFLGNBQWMsYUFBYSxJQUFJO0FBQ3RILHlEQUF5RCxZQUFZLGlCQUFpQjtBQUN0RixHQUFHLGtCQUFrQixpQkFBaUIsMkJBQTJCO0FBQ2pFO0FBQ0Esa0dBQWtHO0FBQ2xHLElBQUksMkJBQTJCLGlCQUFpQixVQUFVLFNBQVM7QUFDbkUsbURBQW1ELE1BQU0sVUFBVSwwQkFBMEI7QUFDN0YscUNBQXFDLCtCQUErQjtBQUNwRSxPQUFPLFFBQVEsRUFBRSw0Q0FBNEMsK0JBQStCO0FBQzVGLDZCQUE2QixRQUFRLEVBQUUsZUFBZTtBQUN0RCxpQ0FBaUMsd0JBQXdCO0FBQ3pELEVBQUUscUJBQXFCLGtCQUFrQix1QkFBdUI7QUFDaEUsc0VBQXNFLGFBQWE7QUFDbkYsY0FBYyxnQkFBZ0IsbUJBQW1CLHFDQUFxQztBQUN0Rix1QkFBdUI7QUFDdkIsOEVBQThFO0FBQzlFO0FBQ0EscUNBQXFDO0FBQ3JDLG9EQUFvRCw2QkFBNkI7QUFDakYsWUFBWSxZQUFZLGNBQWM7QUFDdEMsNEVBQTRFO0FBQzVFO0FBQ0Esb0JBQW9CLGFBQWEsdUJBQXVCLGtCQUFrQjtBQUMxRSxzQkFBc0Isb0NBQW9DLGVBQWU7QUFDekUsMENBQTBDO0FBQzFDLG9CQUFvQixtQkFBbUIsbUJBQW1CO0FBQzFELDZDQUE2QyxjQUFjLHdCQUF3QjtBQUNuRixlQUFlLCtCQUErQixvQkFBb0IsMEJBQTBCO0FBQzVGLElBQUksOEJBQThCLGNBQWMsaUJBQWlCO0FBQ2pFLHFFQUFxRTtBQUNyRSxLQUFLLGlDQUFpQyxtQkFBbUIsZUFBZTtBQUN4RSxVQUFVLFlBQVksSUFBSSxtQkFBbUIsU0FBUyxzQkFBc0IsZUFBZTtBQUMzRix3QkFBd0IsOENBQThDLGFBQWE7QUFDbkYsS0FBSyxtQkFBbUI7QUFDeEIsUUFBUSxzRkFBc0Y7QUFDOUY7QUFDQSw4Q0FBOEMsd0JBQXdCLGVBQWU7QUFDckYsNkJBQTZCLHVDQUF1QztBQUNwRSwyQ0FBMkM7QUFDM0MsbUJBQW1CLGVBQWU7QUFDbEMsZ0RBQWdELFdBQVcsZ0JBQWdCLGVBQWU7QUFDMUYscUJBQXFCLG1CQUFtQix5QkFBeUI7QUFDakUsWUFBWSxhQUFhLDBCQUEwQjtBQUNuRDtBQUNBLHlFQUF5RSxrQkFBa0I7QUFDM0YsV0FBVyw0QkFBNEIsSUFBSSxvQkFBb0IsY0FBYyxNQUFNO0FBQ25GLElBQUksWUFBWSwyQkFBMkIsZ0NBQWdDO0FBQzNFO0FBQ0EsbUJBQW1CLGtCQUFrQixlQUFlLHlCQUF5QjtBQUM3RSwrQ0FBK0Msa0NBQWtDLE1BQU07QUFDdkYsWUFBWSxXQUFXLG1CQUFtQiwyQkFBMkIsUUFBUSxXQUFXLEtBQUs7QUFDN0Y7QUFDQSxnQkFBZ0Isa0NBQWtDO0FBQ2xELHNCQUFzQixZQUFZLFNBQVMsV0FBVyxpQkFBaUI7QUFDdkUsZ0VBQWdFO0FBQ2hFO0FBQ0EsdURBQXVELHNCQUFzQixTQUFTLE1BQU0sV0FBVztBQUN2RywwQ0FBMEMsMkNBQTJDO0FBQ3JGLCtDQUErQyx1QkFBdUIsaUNBQWlDO0FBQ3ZHLDhCQUE4Qiw2QkFBNkIsbUJBQW1CLGdCQUFnQjtBQUM5RixTQUFTO0FBQ1Qsa0NBQWtDLG1CQUFtQixXQUFXLDhCQUE4QjtBQUM5RixxQ0FBcUMsZ0NBQWdDO0FBQ3JFLFVBQVUsaUNBQWlDO0FBQzNDLFVBQVUseURBQXlELDZCQUE2QjtBQUNoRyxnQkFBZ0IseUJBQXlCLG1CQUFtQixXQUFXLGlCQUFpQjtBQUN4RixnQ0FBZ0Msa0JBQWtCO0FBQ2xELDJCQUEyQixZQUFZLElBQUksb0JBQW9CLFlBQVksV0FBVztBQUN0RixhQUFhLGtCQUFrQjtBQUMvQixRQUFRLFlBQVksSUFBSSxxQ0FBcUMsWUFBWSxXQUFXO0FBQ3BGLGFBQWEsa0JBQWtCO0FBQy9CLFFBQVEsWUFBWSxJQUFJLHVFQUF1RSxZQUFZO0FBQzNHLFVBQVUsa0NBQWtDLGtCQUFrQjtBQUM5RCxtQ0FBbUMsYUFBYSxnREFBZ0Q7QUFDaEcscUJBQXFCLG1FQUFtRTtBQUN4RiwyQkFBMkIsV0FBVyxpQ0FBaUM7QUFDdkUsOENBQThDLEVBQUU7QUFDaEQsV0FBVyxZQUFZLDBDQUEwQztBQUNqRTtBQUNBLG1GQUFtRjtBQUNuRjtBQUNBLHlEQUF5RCx1QkFBdUIsaUJBQWlCO0FBQ2pHLFdBQVcsaURBQWlEO0FBQzVELG9CQUFvQixZQUFZLElBQUksb0JBQW9CLGNBQWMsTUFBTSxzQkFBc0I7QUFDbEcsVUFBVSx1QkFBdUIseUJBQXlCO0FBQzFEO0FBQ0EsUUFBUSxjQUFjLGFBQWEscUJBQXFCLGNBQWM7QUFDdEUseURBQXlEO0FBQ3pEO0FBQ0EsNEJBQTRCLDREQUE0RDtBQUN4RixpQkFBaUIsdUJBQXVCLDhCQUE4QjtBQUN0RSx5REFBeUQsbUNBQW1DO0FBQzVGLFVBQVUsZ0JBQWdCLHNDQUFzQyxZQUFZLE1BQU0sTUFBTSxTQUFTO0FBQ2pHLEVBQUUsSUFBSSxtQ0FBbUMsc0NBQXNDO0FBQy9FLEtBQUssNEJBQTRCLEtBQUssS0FBSyxTQUFTLFlBQVksSUFBSSwwQkFBMEIsS0FBSztBQUNuRyxjQUFjLFNBQVMscUJBQXFCLHVDQUF1QztBQUNuRixZQUFZLGFBQWEsc0NBQXNDLHlCQUF5QjtBQUN4RiwwQ0FBMEMseUJBQXlCLGdCQUFnQjtBQUNuRixlQUFlLGlCQUFpQiwrQkFBK0IsZUFBZSxlQUFlLE1BQU07QUFDbkcsRUFBRSxJQUFJLEtBQUssbUNBQW1DLGtCQUFrQixTQUFTLFNBQVMsaUJBQWlCO0FBQ25HLGtDQUFrQyxrQkFBa0IscUJBQXFCLHVCQUF1QjtBQUNoRyxjQUFjLHFCQUFxQix1QkFBdUIsb0JBQW9CLHFCQUFxQjtBQUNuRyx3QkFBd0Isa0JBQWtCLHNDQUFzQztBQUNoRixxQkFBcUIsNkRBQTZEO0FBQ2xGLG9FQUFvRTtBQUNwRSxnRUFBZ0Usb0JBQW9CO0FBQ3BGO0FBQ0EsYUFBYSxjQUFjLFNBQVMsTUFBTSxXQUFXLGdDQUFnQztBQUNyRiw2QkFBNkIsNERBQTREO0FBQ3pGLHNCQUFzQiwwRUFBMEU7QUFDaEcsaURBQWlELDZCQUE2QixVQUFVO0FBQ3hGLG9CQUFvQixPQUFPLGtFQUFrRTtBQUM3RixJQUFJLG1CQUFtQjtBQUN2QixvQkFBb0IsbUJBQW1CLHVCQUF1QixhQUFhLEtBQUssSUFBSSxFQUFFO0FBQ3RGLDRCQUE0QixXQUFXLFlBQVksVUFBVSxvQkFBb0IsTUFBTTtBQUN2RixrREFBa0QsTUFBTTtBQUN4RCxxRUFBcUUsTUFBTTtBQUMzRTtBQUNBLG1CQUFtQjtBQUNuQixlQUFlLGFBQWEsa0JBQWtCLFlBQVksZUFBZSxlQUFlO0FBQ3hGLDZCQUE2QixhQUFhLEtBQUssSUFBSTtBQUNuRCxLQUFLLFNBQVMsOEJBQThCLG1CQUFtQixTQUFTLHVCQUF1QixZQUFZO0FBQzNHLEVBQUUscUNBQXFDLFNBQVMsbUJBQW1CLG1CQUFtQixTQUFTO0FBQy9GLGdCQUFnQixZQUFZLElBQUksaUNBQWlDLFNBQVMsb0JBQW9CO0FBQzlGLGVBQWUsdUNBQXVDLFNBQVMsWUFBWSxJQUFJLGdCQUFnQjtBQUMvRixpQkFBaUIsbUJBQW1CLHdCQUF3QixZQUFZLGFBQWE7QUFDckYsOEJBQThCLFNBQVMscUJBQXFCLGtDQUFrQztBQUM5RixPQUFPO0FBQ1AsU0FBUyx5QkFBeUIsOENBQThDLFVBQVU7QUFDMUYsMkRBQTJEO0FBQzNELDhCQUE4QixtQkFBbUI7QUFDakQsc0NBQXNDLHNCQUFzQixLQUFLLGdCQUFnQixnQkFBZ0IsU0FBUztBQUMxRyxjQUFjLGdFQUFnRTtBQUM5RSxlQUFlLHNCQUFzQixLQUFLLGNBQWMsa0JBQWtCLFNBQVMsZUFBZTtBQUNsRywwREFBMEQsNkNBQTZDO0FBQ3ZHLGFBQWEsa0VBQWtFO0FBQy9FLG9DQUFvQyxpQkFBaUI7QUFDckQsZ0JBQWdCLDBEQUEwRCxpQkFBaUI7QUFDM0Ysc0RBQXNEO0FBQ3RELCtDQUErQyxpQkFBaUI7QUFDaEUsNkJBQTZCO0FBQzdCLGlCQUFpQixpQkFBaUIsNkNBQTZDO0FBQy9FLDBCQUEwQiw4Q0FBOEM7QUFDeEUseUVBQXlFO0FBQ3pFLFlBQVkscUJBQXFCLDZDQUE2QztBQUM5RSwwQkFBMEIsOENBQThDO0FBQ3hFLDRFQUE0RTtBQUM1RSxlQUFlLHFCQUFxQix3Q0FBd0M7QUFDNUUsYUFBYSxzQkFBc0IsS0FBSyxnQkFBZ0IsZ0JBQWdCO0FBQ3hFLGNBQWMsY0FBYyx3Q0FBd0M7QUFDcEUsMEJBQTBCLEtBQUssY0FBYyxrQkFBa0I7QUFDL0QsTUFBTSxjQUFjLHFDQUFxQztBQUN6RCwrQkFBK0IsYUFBYSx3Q0FBd0M7QUFDcEYsYUFBYSwyQkFBMkIsOEJBQThCLGdCQUFnQjtBQUN0RixnQkFBZ0IsOEJBQThCLDJCQUEyQjtBQUN6RSxFQUFFLGdCQUFnQix3Q0FBd0M7QUFDMUQseUNBQXlDLGdCQUFnQix3Q0FBd0M7QUFDakcsNkVBQTZFLGdCQUFnQjtBQUM3RixnQ0FBZ0MsdUJBQXVCLDBCQUEwQjtBQUNqRixvQkFBb0IsdURBQXVEO0FBQzNFLDhEQUE4RCxvQkFBb0I7QUFDbEYsaUJBQWlCLHVCQUF1QiwwQkFBMEI7QUFDbEUsVUFBVSx1REFBdUQ7QUFDakUsa0RBQWtELG9CQUFvQjtBQUN0RSw2REFBNkQsZ0JBQWdCO0FBQzdFLGdCQUFnQiw2REFBNkQsZ0JBQWdCO0FBQzdGLDZCQUE2Qiw2REFBNkQ7QUFDMUYsT0FBTyx5Q0FBeUM7QUFDaEQsU0FBUyxpQkFBaUIsd0JBQXdCO0FBQ2xELG1DQUFtQztBQUNuQyxLQUFLLDJEQUEyRCxnQkFBZ0I7QUFDaEYsNENBQTRDLDRCQUE0Qix3QkFBd0I7QUFDaEcsV0FBVyxZQUFZLGtCQUFrQixnQkFBZ0IsbUJBQW1CLFdBQVc7QUFDdkYsb0VBQW9FLDRCQUE0QjtBQUNoRyxhQUFhLGtCQUFrQixjQUFjLG9CQUFvQixpQkFBaUI7QUFDbEYsV0FBVyxnQkFBZ0IsZ0VBQWdFO0FBQzNGLG9EQUFvRCxlQUFlO0FBQ25FLGdDQUFnQztBQUNoQyxVQUFVLGtCQUFrQixzRUFBc0U7QUFDbEcsc0VBQXNFLGtCQUFrQjtBQUN4RiwwREFBMEQ7QUFDMUQsdUVBQXVFLGtCQUFrQjtBQUN6RiwwREFBMEQ7QUFDMUQsdUVBQXVFLGtCQUFrQjtBQUN6RixnQkFBZ0IsbUNBQW1DO0FBQ25ELEVBQUUsK0NBQStDO0FBQ2pELGFBQWEsdUJBQXVCLHVCQUF1QixnQkFBZ0I7QUFDM0UseURBQXlEO0FBQ3pELGtFQUFrRSx1QkFBdUI7QUFDekYsc0NBQXNDLDJEQUEyRDtBQUNqRyxtQkFBbUIsa0RBQWtEO0FBQ3JFLHVCQUF1QixzQkFBc0IsMkNBQTJDO0FBQ3hGLE1BQU0sd0JBQXdCLHFCQUFxQixnQkFBZ0Isa0JBQWtCLGdCQUFnQjtBQUNyRyw0REFBNEQsV0FBVyxlQUFlO0FBQ3RGLG9CQUFvQixvQkFBb0Isd0JBQXdCLHFCQUFxQjtBQUNyRixvQkFBb0IsaUJBQWlCLDZEQUE2RDtBQUNsRyxFQUFFLGVBQWUsd0NBQXdDO0FBQ3pELG1DQUFtQyxjQUFjLDJDQUEyQztBQUM1RiwyRUFBMkUsaUJBQWlCO0FBQzVGLCtCQUErQjtBQUMvQixhQUFhLGlCQUFpQiwyQ0FBMkM7QUFDekUsa0dBQWtHO0FBQ2xHLGVBQWUsMkNBQTJDO0FBQzFEO0FBQ0EsSUFBSSxpQkFBaUIsaURBQWlEO0FBQ3RFLGlEQUFpRCxxQkFBcUI7QUFDdEUsa0JBQWtCLCtFQUErRTtBQUNqRyxrQkFBa0IseUJBQXlCO0FBQzNDLGtEQUFrRCxxQkFBcUIsdUJBQXVCO0FBQzlGLGdHQUFnRztBQUNoRyxjQUFjLDJDQUEyQyx5QkFBeUIsaUJBQWlCO0FBQ25HLHlDQUF5Qyx5QkFBeUIsaUJBQWlCO0FBQ25GO0FBQ0EsT0FBTyxvQkFBb0IsNENBQTRDLHlCQUF5QjtBQUNoRyxjQUFjLDRDQUE0Qyx5QkFBeUI7QUFDbkYscUNBQXFDO0FBQ3JDLE1BQU07QUFDTiwwQ0FBMEMseURBQXlEO0FBQ25HLDZEQUE2RDtBQUM3RCxnQkFBZ0IsZ0VBQWdFLFVBQVU7QUFDMUY7QUFDQSwrQkFBK0IsU0FBUyxxQ0FBcUM7QUFDN0U7QUFDQSwwREFBMEQ7QUFDMUQsd0JBQXdCLGlCQUFpQixzQkFBc0I7QUFDL0QsSUFBSSxtRUFBbUU7QUFDdkUsNkNBQTZDLG9CQUFvQjtBQUNqRSxXQUFXLE1BQU0sOEJBQThCLElBQUksY0FBYyxLQUFLO0FBQ3RFLGNBQWMsa0ZBQWtGO0FBQ2hHLEVBQUUsTUFBTSxxQkFBcUIsWUFBWSxTQUFTLFVBQVUsbUJBQW1CLE1BQU0seUJBQXlCO0FBQzlHLDhDQUE4QywwREFBMEQ7QUFDeEcsYUFBYSxXQUFXLEdBQUcsRUFBRSwrQkFBK0IsV0FBVyxTQUFTLFlBQVk7QUFDNUYsYUFBYSxrREFBa0QsRUFBRSxXQUFXLFNBQVMsV0FBVyxHQUFHLEVBQUU7QUFDckcsS0FBSyxhQUFhLEdBQUcscUJBQXFCLFVBQVUsMENBQTBDO0FBQzlGLEdBQUcsR0FBRyw4RUFBOEUsYUFBYTtBQUNqRyxxQ0FBcUMsY0FBYyxFQUFFO0FBQ3JELElBQUksU0FBUyxFQUFFLFlBQVksc0NBQXNDLHVCQUF1QixFQUFFO0FBQzFGLGNBQWM7QUFDZCw4RkFBOEYsRUFBRTtBQUNoRyxTQUFTLEVBQUUsSUFBSSxhQUFhLGVBQWUscUNBQXFDLEtBQUssT0FBTztBQUM1RixHQUFHLGVBQWUsRUFBRSxFQUFFLEVBQUUsU0FBUyxhQUFhLEVBQUUsRUFBRSxFQUFFLDhCQUE4QjtBQUNsRixzRUFBc0Usb0JBQW9CO0FBQzFGLGFBQWEsa0NBQWtDLHdDQUF3QyxHQUFHLFNBQVMsRUFBRTtBQUNyRyxNQUFNLFFBQVEsRUFBRSxFQUFFLGFBQWEsR0FBRyxLQUFLLFVBQVUsRUFBRSxFQUFFLGVBQWUsVUFBVSxFQUFFLEVBQUUsVUFBVSxFQUFFLEVBQUUsR0FBRztBQUNuRyxRQUFRLEVBQUUsRUFBRSxFQUFFLHVDQUF1QyxVQUFVLG1CQUFtQixpQkFBaUI7QUFDbkcseURBQXlELHVCQUF1QixtQkFBbUI7QUFDbkc7QUFDQSwwQ0FBMEMsT0FBTyxTQUFTLEVBQUUsS0FBSyxvQkFBb0I7QUFDckYsZUFBZSxtRUFBbUUsS0FBSztBQUN2RixFQUFFLFNBQVMsU0FBUyxvQkFBb0IsaUJBQWlCLFNBQVMsNkJBQTZCLFlBQVk7QUFDM0csRUFBRSxLQUFLLHVDQUF1QyxPQUFPLFlBQVksK0JBQStCLFNBQVM7QUFDekcsT0FBTywrQkFBK0IsU0FBUyxJQUFJLFNBQVMsWUFBWTtBQUN4RSxJQUFJLFNBQVMsOEJBQThCLHVDQUF1QyxpQkFBaUI7QUFDbkcsV0FBVyxVQUFVLGdCQUFnQixrQkFBa0IsMEJBQTBCLGlCQUFpQjtBQUNsRyxXQUFXLHVDQUF1QyxtQkFBbUIsa0JBQWtCO0FBQ3ZGLHVDQUF1QywyQ0FBMkMsU0FBUztBQUMzRixXQUFXLGVBQWUsU0FBUyxZQUFZLFdBQVcsZ0NBQWdDLFNBQVM7QUFDbkcsbUJBQW1CLGlCQUFpQixlQUFlLFlBQVksd0JBQXdCO0FBQ3ZGLHNDQUFzQyxTQUFTLHVCQUF1QixlQUFlO0FBQ3JGLE9BQU8sc0JBQXNCLHFCQUFxQixNQUFNLFFBQVEsbUNBQW1DO0FBQ25HLFlBQVksU0FBUyxtQkFBbUIsaUJBQWlCO0FBQ3pELDREQUE0RCxtQkFBbUIsZUFBZTtBQUM5RixFQUFFLG9CQUFvQixrQkFBa0IsMENBQTBDLFlBQVksS0FBSztBQUNuRyxXQUFXLFlBQVksS0FBSyxxQkFBcUIsU0FBUyxHQUFHLGVBQWU7QUFDNUUsS0FBSywyQkFBMkIsY0FBYyx3Q0FBd0M7QUFDdEYsaUJBQWlCLEVBQUUsdUJBQXVCLGFBQWE7QUFDdkQ7QUFDQSwrRUFBK0UsZ0JBQWdCLEVBQUU7QUFDakcsaUJBQWlCLEVBQUUsTUFBTSx3QkFBd0IsMkJBQTJCLEVBQUUsbUJBQW1CLGFBQWEsSUFBSTtBQUNsSCxzQ0FBc0MsNENBQTRDO0FBQ2xGLDhGQUE4RjtBQUM5RiwrREFBK0Qsb0NBQW9DO0FBQ25HLHVCQUF1QixtQkFBbUIsZUFBZSx1Q0FBdUM7QUFDaEcsc0JBQXNCLG1DQUFtQyxhQUFhLGdCQUFnQixhQUFhO0FBQ25HLGdCQUFnQixvQkFBb0IsYUFBYSxtQkFBbUIsaUJBQWlCO0FBQ3JGLE9BQU8sMkJBQTJCLGlDQUFpQyxVQUFVLGVBQWU7QUFDNUY7QUFDQSxzQkFBc0IsK0NBQStDO0FBQ3JFLFVBQVUsMEJBQTBCO0FBQ3BDLGdHQUFnRyxLQUFLO0FBQ3JHLE9BQU8sRUFBRSxrQkFBa0I7QUFDM0I7QUFDQSwwQ0FBMEM7QUFDMUMsb0ZBQW9GO0FBQ3BGLE9BQU8sb0JBQW9CLGVBQWU7QUFDMUMseUJBQXlCLHlDQUF5QyxnQkFBZ0I7QUFDbEYsK0JBQStCLGlCQUFpQixtQkFBbUIseUJBQXlCO0FBQzVGLHVCQUF1QixvQ0FBb0Msb0JBQW9CLE1BQU0sTUFBTTtBQUMzRix1Q0FBdUM7QUFDdkMsb0JBQW9CLFdBQVcsdUJBQXVCLHFDQUFxQztBQUMzRixxQkFBcUIsSUFBSSxvQkFBb0IsU0FBUyxTQUFTLHFCQUFxQixVQUFVO0FBQzlGO0FBQ0EsMkdBQTJHO0FBQzNHLDZGQUE2RjtBQUM3RixHQUFHO0FBQ0gsMERBQTBEO0FBQzFELGtDQUFrQyxTQUFTLHFCQUFxQjtBQUNoRSx1QkFBdUIsZ0JBQWdCLHVDQUF1QztBQUM5RSxnQkFBZ0IsdUJBQXVCLG9CQUFvQixjQUFjO0FBQ3pFO0FBQ0EsbURBQW1ELG9CQUFvQixtQkFBbUIsT0FBTztBQUNqRyx5Q0FBeUMsY0FBYyxpQ0FBaUM7QUFDeEYsTUFBTSxpQ0FBaUMsMENBQTBDLFNBQVM7QUFDMUYsb0NBQW9DLHVEQUF1RDtBQUMzRixlQUFlLDJDQUEyQyxjQUFjO0FBQ3hFLG1CQUFtQixpQ0FBaUM7QUFDcEQ7QUFDQSxJQUFJLDhCQUE4QixzQkFBc0IsS0FBSyxvQ0FBb0M7QUFDakcsYUFBYSxNQUFNLG1CQUFtQjtBQUN0QywyQ0FBMkMsWUFBWSxtQkFBbUI7QUFDMUUsZUFBZSw2Q0FBNkMsVUFBVTtBQUN0RTtBQUNBLDhGQUE4RjtBQUM5RixZQUFZLHVCQUF1QixRQUFRLFdBQVc7QUFDdEQsR0FBRztBQUNILE9BQU8sd0RBQXdELG9DQUFvQztBQUNuRyxFQUFFLGdDQUFnQyxZQUFZLHVCQUF1QixtQkFBbUI7QUFDeEYsdUJBQXVCLFdBQVc7QUFDbEMscUJBQXFCLG1CQUFtQixvQ0FBb0MscUJBQXFCO0FBQ2pHLFdBQVcsdUNBQXVDLHFCQUFxQixpQkFBaUI7QUFDeEYsY0FBYywyRUFBMkU7QUFDekYsaUJBQWlCLGVBQWUsbUJBQW1CLGVBQWUsV0FBVztBQUM3RSxTQUFTLDhCQUE4QixTQUFTLHNCQUFzQjtBQUN0RSwrQ0FBK0MsZUFBZSxpQkFBaUI7QUFDL0UsSUFBSSxJQUFJLGNBQWMsU0FBUyxtQkFBbUIsaUJBQWlCLEtBQUssYUFBYTtBQUNyRixRQUFRLGtCQUFrQixlQUFlLGtDQUFrQyxXQUFXO0FBQ3RGLGVBQWUsU0FBUyx3QkFBd0IsaUJBQWlCLGlDQUFpQztBQUNsRywyQkFBMkIscUJBQXFCLGFBQWE7QUFDN0Qsc0RBQXNELDBCQUEwQixRQUFRO0FBQ3hGLFFBQVEsUUFBUSxFQUFFLEVBQUUsYUFBYSxtQkFBbUIsMkNBQTJDO0FBQy9GLGlDQUFpQyxxQkFBcUI7QUFDdEQsVUFBVSxtRkFBbUY7QUFDN0YsZ0NBQWdDLGtCQUFrQjtBQUNsRCx1REFBdUQsdUNBQXVDLEVBQUUsVUFBVSxPQUFPLDBCQUEwQixFQUFFLGVBQWUsU0FBUyx5QkFBeUI7QUFDOUwsT0FBTyxJQUFJLGNBQWMsYUFBYSxnREFBZ0Q7QUFDdEYsb0JBQW9CLGdCQUFnQixFQUFFLHdCQUF3QixxQkFBcUI7QUFDbkYsSUFBSSx1QkFBdUIsd0JBQXdCLHVCQUF1QixrQkFBa0I7QUFDNUYsY0FBYyxxQkFBcUIsa0JBQWtCLG1CQUFtQjtBQUN4RSw4REFBOEQsZ0NBQWdDO0FBQzlGLG9CQUFvQiwyQkFBMkIsc0RBQXNEO0FBQ3JHLHVCQUF1QixrQ0FBa0MsU0FBUztBQUNsRSxvRkFBb0Y7QUFDcEYsc0RBQXNELDRCQUE0QjtBQUNsRixFQUFFLGtDQUFrQyxvRUFBb0U7QUFDeEcsOEJBQThCLDJCQUEyQiw4REFBOEQ7QUFDdkgsaUNBQWlDLHFCQUFxQiwwQ0FBMEM7QUFDaEcsaUJBQWlCLGNBQWMsb0NBQW9DLGVBQWU7QUFDbEYsRUFBRSw0QkFBNEIsd0RBQXdEO0FBQ3RGLHdCQUF3QixxQkFBcUIsa0RBQWtEO0FBQy9GLDJCQUEyQixnQ0FBZ0M7QUFDM0QsbUJBQW1CLGlDQUFpQyw0QkFBNEI7QUFDaEYsMERBQTBELDBCQUEwQjtBQUNwRixFQUFFLCtCQUErQiw4REFBOEQ7QUFDL0YsMkJBQTJCLHdCQUF3Qix3REFBd0Q7QUFDM0csOEJBQThCLHdCQUF3QixnREFBZ0Q7QUFDdEcsb0JBQW9CLGlCQUFpQiwwQ0FBMEMsa0JBQWtCO0FBQ2pHLGtCQUFrQiw2QkFBNkIsMERBQTBEO0FBQ3pHLHlCQUF5QixzQkFBc0Isb0RBQW9EO0FBQ25HLDRCQUE0QixvQkFBb0Isd0NBQXdDO0FBQ3hGLGdCQUFnQixhQUFhLGtDQUFrQyxjQUFjLG1CQUFtQjtBQUNoRyxrREFBa0QsMEJBQTBCLHFCQUFxQjtBQUNqRyw0Q0FBNEMsbUJBQW1CLHdCQUF3QjtBQUN2Riw4Q0FBOEMsd0JBQXdCLG1CQUFtQjtBQUN6Rix3Q0FBd0MsaUJBQWlCLHNCQUFzQjtBQUMvRSxtQkFBbUI7QUFDbkI7QUFDQSxnQkFBZ0Isc0NBQXNDLEVBQUUsV0FBVyxFQUFFLEdBQUcsRUFBRSxFQUFFLGFBQWEsWUFBWTtBQUNyRyxZQUFZLE1BQU0sWUFBWSxRQUFRLFlBQVksZUFBZTtBQUNqRSxVQUFVLGFBQWEsMkVBQTJFO0FBQ2xHLHFDQUFxQywwREFBMEQ7QUFDL0YsbUNBQW1DLG1DQUFtQyxzQkFBc0I7QUFDNUYsYUFBYSxxQkFBcUIsa0NBQWtDLG1CQUFtQjtBQUN2RixVQUFVLHlCQUF5QixJQUFJO0FBQ3ZDLEdBQUcsU0FBUyx3Q0FBd0MsT0FBTyxJQUFJO0FBQy9ELGNBQWM7QUFDZCxJQUFJLFNBQVMsb0NBQW9DLE1BQU0sZ0RBQWdEO0FBQ3ZHLHdEQUF3RCxTQUFTLFNBQVMscUJBQXFCO0FBQy9GLElBQUksNkNBQTZDLCtCQUErQixZQUFZO0FBQzVGO0FBQ0E7QUFDQSwyQkFBMkIsRUFBRSx1QkFBdUIsR0FBRyxrQkFBa0I7QUFDekU7QUFDQSw4RUFBOEUsZ0JBQWdCO0FBQzlGO0FBQ0EseUNBQXlDLGVBQWUscUNBQXFDO0FBQzdGLHNFQUFzRSxFQUFFO0FBQ3hFLDZHQUE2RztBQUM3RyxNQUFNLEVBQUUsMkJBQTJCLHFCQUFxQixnQkFBZ0Isc0JBQXNCO0FBQzlGLHlCQUF5QixnQkFBZ0IsT0FBTztBQUNoRCxLQUFLLHVEQUF1RCxJQUFJLEtBQUs7QUFDckUsaUJBQWlCLDZFQUE2RTtBQUM5RixhQUFhLEVBQUU7QUFDZixxRUFBcUUsa0JBQWtCO0FBQ3ZGLFlBQVksa0NBQWtDLEVBQUUsMkJBQTJCLG9CQUFvQixPQUFPO0FBQ3RHLHNDQUFzQyw4QkFBOEI7QUFDcEUsVUFBVSxNQUFNO0FBQ2hCLElBQUk7QUFDSjtBQUNBLGtFQUFrRSxlQUFlLEVBQUUsS0FBSyxFQUFFLFVBQVUsT0FBTyxhQUFhLEVBQUUsb0JBQW9CLElBQUksV0FBVztBQUM3SixXQUFXLHlGQUF5RjtBQUNwRyxHQUFHLHdFQUF3RTtBQUMzRSwyQkFBMkIsT0FBTztBQUNsQyxvQ0FBb0MsY0FBYyxhQUFhLElBQUksY0FBYyxFQUFFLGNBQWMsYUFBYSxJQUFJLHVCQUF1Qiw0QkFBNEIsZ0JBQWdCO0FBQ3JMLHFHQUFxRztBQUNyRyx5Q0FBeUMsZ0JBQWdCLG1DQUFtQztBQUM1RixNQUFNLDhDQUE4QyxFQUFFLG9CQUFvQixVQUFVO0FBQ3BGLEdBQUcsWUFBWSxNQUFNO0FBQ3JCLHVFQUF1RSxvQkFBb0I7QUFDM0YsU0FBUyxjQUFjLEVBQUUsMkJBQTJCLHdCQUF3QixTQUFTLFVBQVU7QUFDL0Ysb0JBQW9CLGNBQWMsdUNBQXVDO0FBQ3pFO0FBQ0Esa0NBQWtDLG9CQUFvQixPQUFPO0FBQzdELGtDQUFrQztBQUNsQyw2Q0FBNkM7QUFDN0MsZUFBZSxzQkFBc0Isb0JBQW9CLFVBQVUsZUFBZSxTQUFTO0FBQzNGLFFBQVEsRUFBRSxtQkFBbUIsSUFBSSxZQUFZLFlBQVksdUJBQXVCLE9BQU8sbUJBQW1CO0FBQzFHLFdBQVcsV0FBVyxZQUFZLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSxZQUFZLEdBQUcsTUFBTSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRSxPQUFPLEdBQUc7QUFDekksU0FBUyxHQUFHLE1BQU0sRUFBRSxNQUFNLEVBQUUseUJBQXlCLEVBQUUsUUFBUSxFQUFFLFFBQVEsRUFBRSxzQkFBc0I7QUFDakcsc0JBQXNCLGdEQUFnRCxpQkFBaUI7QUFDdkYsU0FBUyxpQ0FBaUMsYUFBYTtBQUN2RCxtREFBbUQsd0JBQXdCLGNBQWM7QUFDekY7QUFDQSxtREFBbUQsY0FBYyxlQUFlLGlCQUFpQjtBQUNqRyxpQ0FBaUMsYUFBYSxtREFBbUQ7QUFDakcsd0JBQXdCLGdCQUFnQixlQUFlLDhCQUE4QjtBQUNyRixlQUFlLE1BQU0sV0FBVyxvQkFBb0I7QUFDcEQsb0NBQW9DLGdCQUFnQix1QkFBdUIsZUFBZTtBQUMxRixHQUFHLDZCQUE2QixlQUFlLG1CQUFtQixnQkFBZ0IsRUFBRSxtQkFBbUIsSUFBSSxjQUFjLHVDQUF1QyxlQUFlLFlBQVk7QUFDM0wsaUJBQWlCLEtBQUssbUJBQW1CLHlDQUF5QyxTQUFTO0FBQzNGLE1BQU0sRUFBRSxtQkFBbUIsYUFBYSxJQUFJLFlBQVksY0FBYyxlQUFlO0FBQ3JGLGlCQUFpQixlQUFlLHlCQUF5QjtBQUN6RCxrQkFBa0IsbUNBQW1DLDBDQUEwQztBQUMvRiwyRkFBMkY7QUFDM0YsRUFBRTtBQUNGLGFBQWEsa0JBQWtCLFFBQVE7QUFDdkMsR0FBRywrREFBK0Q7QUFDbEUsaUJBQWlCLHdEQUF3RCxrQkFBa0I7QUFDM0YsaUJBQWlCO0FBQ2pCLHFCQUFxQjtBQUNyQiwwQ0FBMEMsSUFBSSxnREFBZ0Q7QUFDOUYsVUFBVSxvQkFBb0Isc0VBQXNFO0FBQ3BHLGdEQUFnRCxlQUFlLGlDQUFpQztBQUNoRyxVQUFVLDBCQUEwQixlQUFlLGVBQWUsOEJBQThCO0FBQ2hHLDhCQUE4QixtQkFBbUI7QUFDakQsMkNBQTJDLEdBQUcsRUFBRSxjQUFjLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSx5QkFBeUI7QUFDN0gsVUFBVSxpQkFBaUIsV0FBVyw0QkFBNEIsZUFBZSxFQUFFO0FBQ25GLCtEQUErRCxLQUFLLFlBQVk7QUFDaEYsS0FBSyxLQUFLLFlBQVksa0JBQWtCLFlBQVkscUJBQXFCLHlCQUF5QjtBQUNsRyxhQUFhLEVBQUUsbUJBQW1CLElBQUksNENBQTRDLGVBQWU7QUFDakcsdUJBQXVCLGdCQUFnQixrQkFBa0IsZUFBZTtBQUN4RSwyREFBMkQsa0JBQWtCLGVBQWU7QUFDNUYsb0JBQW9CLHVCQUF1QixlQUFlLHNCQUFzQjtBQUNoRixJQUFJLGVBQWUsaUNBQWlDLDBCQUEwQixlQUFlO0FBQzdGLDBCQUEwQixvQkFBb0IsUUFBUSw2QkFBNkI7QUFDbkYsa0JBQWtCLDhCQUE4Qiw2QkFBNkIsRUFBRSxpQkFBaUI7QUFDaEcsaUNBQWlDLGtCQUFrQiw4QkFBOEI7QUFDakYsaUJBQWlCLEVBQUUsaUJBQWlCLHFDQUFxQyxrQkFBa0I7QUFDM0YsVUFBVSxpQkFBaUIsc0NBQXNDLGtCQUFrQjtBQUNuRixZQUFZLDZCQUE2QixFQUFFLGlCQUFpQixvQ0FBb0M7QUFDaEcsWUFBWSw4QkFBOEIsNkJBQTZCLEVBQUUsaUJBQWlCO0FBQzFGLDhCQUE4QixpQ0FBaUM7QUFDL0Qsc0JBQXNCLGtDQUFrQyxnQkFBZ0IsMEJBQTBCO0FBQ2xHLG1DQUFtQyx5Q0FBeUM7QUFDNUUsWUFBWSxnRUFBZ0U7QUFDNUUsTUFBTSxFQUFFLGdDQUFnQyxzQ0FBc0M7QUFDOUUsSUFBSSxhQUFhLEtBQUssNEJBQTRCLEtBQUssU0FBUyxvQkFBb0I7QUFDcEYsTUFBTSxZQUFZLGdDQUFnQyxpQ0FBaUM7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RkFBNkY7QUFDN0YsWUFBWSxTQUFTLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSxVQUFVLGVBQWU7QUFDcEYsUUFBUSxxQ0FBcUMsc0JBQXNCO0FBQ25FLG1EQUFtRCwwQkFBMEIsSUFBSSxXQUFXO0FBQzVGLENBQUMscUZBQXFGO0FBQ3RGLFdBQVcsSUFBSSxXQUFXLFNBQVM7QUFDbkMsbUJBQW1CLDBCQUEwQixJQUFJLFdBQVcsUUFBUTtBQUNwRSxhQUFhLGlCQUFpQixjQUFjLEVBQUUsbUJBQW1CLElBQUksb0NBQW9DLG9DQUFvQztBQUM3SSxpQkFBaUIsMkJBQTJCLGtCQUFrQixzQkFBc0I7QUFDcEYsdUJBQXVCLFFBQVEsK0JBQStCLHVCQUF1QixJQUFJO0FBQ3pGLFNBQVMsY0FBYyxzQ0FBc0MsbUNBQW1DO0FBQ2hHLGlDQUFpQyxrQkFBa0IsNEJBQTRCLGFBQWEsWUFBWTtBQUN4RyxFQUFFLDhCQUE4QixTQUFTLHlDQUF5QztBQUNsRixpRUFBaUU7QUFDakUsK0NBQStDLGdDQUFnQztBQUMvRSx3QkFBd0IsZ0NBQWdDLGtCQUFrQjtBQUMxRSxtQkFBbUIsa0NBQWtDLGlCQUFpQix1QkFBdUI7QUFDN0YsMkNBQTJDLElBQUksZ0NBQWdDO0FBQy9FLFFBQVEscUNBQXFDLG9DQUFvQztBQUNqRiwrQ0FBK0M7QUFDL0MsK0NBQStDLGlCQUFpQiwrQkFBK0I7QUFDL0YsT0FBTyxpQ0FBaUMsaUJBQWlCLEdBQUcsK0JBQStCO0FBQzNGLDhDQUE4QyxJQUFJLCtCQUErQjtBQUNqRixnQkFBZ0IsbUNBQW1DLE1BQU07QUFDekQsRUFBRSxnRUFBZ0U7QUFDbEUsb0JBQW9CLG1DQUFtQyxXQUFXLGVBQWUsZ0JBQWdCO0FBQ2pHLEVBQUUsSUFBSSxnQkFBZ0IsYUFBYSxhQUFhLE9BQU8sY0FBYyxTQUFTLFVBQVUsY0FBYztBQUN0RywrQkFBK0IsMEJBQTBCLCtCQUErQjtBQUN4RixjQUFjLCtCQUErQjtBQUM3QztBQUNBLCtCQUErQixTQUFTLFlBQVksU0FBUyxFQUFFLG1CQUFtQixJQUFJLFlBQVk7QUFDbEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxFQUFFLGNBQWMsSUFBSSxvQ0FBb0Msb0JBQW9CLG9CQUFvQjtBQUM5RyxHQUFHLGVBQWUsUUFBUSxPQUFPLFlBQVksZUFBZSxpQkFBaUIsZ0JBQWdCO0FBQzdGLHVDQUF1QyxzQkFBc0IsbUJBQW1CO0FBQ2hGLDBCQUEwQixzQkFBc0Isc0JBQXNCLGFBQWEsRUFBRTtBQUNyRixlQUFlLEVBQUUsRUFBRSxtQkFBbUIsYUFBYSxJQUFJLFlBQVksZUFBZSw4QkFBOEI7QUFDaEgsR0FBRyxzQkFBc0Isc0NBQXNDLFVBQVUsNEJBQTRCO0FBQ3JHLDJCQUEyQixtQkFBbUIsMkNBQTJDO0FBQ3pGLHFEQUFxRCx5Q0FBeUM7QUFDOUYsbUVBQW1FLGNBQWMsRUFBRSxlQUFlO0FBQ2xHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOERBQThELGdCQUFnQixLQUFLO0FBQ25GLCtCQUErQixhQUFhLEtBQUssS0FBSztBQUN0RCxvQ0FBb0MsMkJBQTJCO0FBQy9ELFlBQVksS0FBSyxLQUFLO0FBQ3RCLHdDQUF3QyxrREFBa0Q7QUFDMUYsOERBQThELG9CQUFvQjtBQUNsRixnQ0FBZ0MsWUFBWSxXQUFXLDRCQUE0QixZQUFZO0FBQy9GLFNBQVMsaUNBQWlDLEtBQUssS0FBSyxVQUFVLElBQUksS0FBSyxLQUFLLFVBQVUsVUFBVTtBQUNoRztBQUNBLFVBQVUseUJBQXlCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBLDRGQUE0RjtBQUM1RixtQkFBbUIsZUFBZSxZQUFZLGNBQWMsYUFBYSxJQUFJLGVBQWUsRUFBRSxtQkFBbUIsYUFBYSxJQUFJLFVBQVUsY0FBYyx3QkFBd0I7QUFDbEwsVUFBVSxtQ0FBbUMsc0NBQXNDO0FBQ25GLG9CQUFvQjtBQUNwQiwyQkFBMkIsNkJBQTZCO0FBQ3hELFVBQVUsdUJBQXVCLHNEQUFzRDtBQUN2RiwyREFBMkQsZUFBZTtBQUMxRSxRQUFRLFFBQVEsSUFBSSwwQkFBMEIsSUFBSTtBQUNsRCxhQUFhLGtCQUFrQixzQkFBc0IsZ0NBQWdDO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsUUFBUTtBQUN2Riw2REFBNkQsYUFBYTtBQUMxRSxtQ0FBbUMsUUFBUSxXQUFXLE1BQU0sb0NBQW9DO0FBQ2hHLHdDQUF3QztBQUN4QyxvQkFBb0IsS0FBSztBQUN6Qix3REFBd0Q7QUFDeEQsNkJBQTZCO0FBQzdCLGlDQUFpQyxrQkFBa0I7QUFDbkQsTUFBTSxNQUFNLEVBQUUsZ0NBQWdDLElBQUksMEJBQTBCLGNBQWM7QUFDMUYsa0NBQWtDLGlDQUFpQyxtQkFBbUI7QUFDdEYsNENBQTRDLE1BQU0sRUFBRSxnQ0FBZ0MsSUFBSTtBQUN4RixjQUFjLG9EQUFvRCxpQ0FBaUM7QUFDbkcsa0JBQWtCLE9BQU87QUFDekIsbUVBQW1FLFlBQVk7QUFDL0UsZ0JBQWdCO0FBQ2hCLGNBQWMsV0FBVyxzQ0FBc0MsVUFBVTtBQUN6RSxxQ0FBcUMsb0JBQW9CLHlCQUF5QjtBQUNsRjtBQUNBLHNCQUFzQixLQUFLLG9DQUFvQyxJQUFJLEtBQUssbUJBQW1CO0FBQzNGLGdDQUFnQyxnQkFBZ0I7QUFDaEQsZUFBZTtBQUNmO0FBQ0EscURBQXFELEtBQUssRUFBRSxVQUFVLE9BQU8sdURBQXVELEVBQUUsZUFBZTtBQUNySiw0QkFBNEIsZUFBZSx1QkFBdUIscUJBQXFCLE9BQU87QUFDOUYsV0FBVyxxQkFBcUIsWUFBWSxZQUFZLG9CQUFvQixxQkFBcUI7QUFDakcsb0JBQW9CLDREQUE0RCxZQUFZO0FBQzVGLDhCQUE4QixFQUFFLGtCQUFrQixpQkFBaUI7QUFDbkUsd0NBQXdDLEVBQUUsS0FBSyxPQUFPLHFCQUFxQixPQUFPO0FBQ2xGO0FBQ0EsSUFBSSxlQUFlLGdCQUFnQixjQUFjLHlCQUF5QixhQUFhO0FBQ3ZGLHlCQUF5QixZQUFZLEtBQUssOEJBQThCO0FBQ3hFLElBQUksdUJBQXVCLHlCQUF5QixnREFBZ0Q7QUFDcEcsV0FBVyxZQUFZLGNBQWMsYUFBYSxJQUFJLEtBQUssS0FBSyxvQkFBb0I7QUFDcEYsbUJBQW1CLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSxZQUFZO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyxRQUFRLG9EQUFvRDtBQUM1RCxnRkFBZ0YsRUFBRSxFQUFFLG1CQUFtQixhQUFhLElBQUksNEJBQTRCLGVBQWU7QUFDbkssNEJBQTRCLGdCQUFnQixzQkFBc0IsZUFBZSxZQUFZLE1BQU07QUFDbkcsU0FBUztBQUNULGlFQUFpRSxhQUFhLElBQUk7QUFDbEYsdUJBQXVCLHVCQUF1QiwyQkFBMkIsMEJBQTBCO0FBQ25HLHlDQUF5QztBQUN6QyxjQUFjO0FBQ2QsOEJBQThCLGlCQUFpQixpQkFBaUI7QUFDaEU7QUFDQSxVQUFVLHdDQUF3Qyx5QkFBeUIsc0JBQXNCO0FBQ2pHLFdBQVcsV0FBVyxTQUFTLFNBQVMsV0FBVyxlQUFlO0FBQ2xFLE1BQU0scUJBQXFCO0FBQzNCLGdGQUFnRjtBQUNoRixrRUFBa0UscUJBQXFCO0FBQ3ZGLCtCQUErQixxQkFBcUI7QUFDcEQ7QUFDQSwwQkFBMEIsbUNBQW1DLHdCQUF3QjtBQUNyRiw2QkFBNkIsT0FBTztBQUNwQyxLQUFLLDZCQUE2QixxQkFBcUI7QUFDdkQsY0FBYyw2QkFBNkIsNENBQTRDLGNBQWM7QUFDckcsMkJBQTJCLFlBQVksMkJBQTJCLGFBQWE7QUFDL0UsMkRBQTJELEVBQUUsVUFBVSxPQUFPLGVBQWUsRUFBRSxpQkFBaUIsYUFBYSxJQUFJLE1BQU0sRUFBRSxtQkFBbUIsYUFBYSxJQUFJLG9CQUFvQixlQUFlO0FBQ2hOLGdGQUFnRjtBQUNoRix1QkFBdUIsT0FBTztBQUM5QixzQkFBc0IscUJBQXFCLG1CQUFtQjtBQUM5RCxrREFBa0Q7QUFDbEQsMERBQTBEO0FBQzFELDZEQUE2RCxZQUFZO0FBQ3pFO0FBQ0Esb0JBQW9CO0FBQ3BCLHdCQUF3QjtBQUN4QjtBQUNBLHNGQUFzRjtBQUN0Rix1RUFBdUUsd0JBQXdCO0FBQy9GLHlGQUF5RjtBQUN6RiwwRkFBMEYsSUFBSSxrQkFBa0I7QUFDaEgsR0FBRztBQUNILGFBQWEsd0JBQXdCLGVBQWU7QUFDcEQseUJBQXlCO0FBQ3pCLFFBQVEseUJBQXlCLGVBQWUseUJBQXlCLEVBQUUsbUJBQW1CLEVBQUUsaUJBQWlCLEVBQUU7QUFDbkgsUUFBUSxpQkFBaUIsZUFBZTtBQUN4Qyw2QkFBNkIsb0NBQW9DO0FBQ2pFLHFDQUFxQyw0QkFBNEIsWUFBWSxHQUFHO0FBQ2hGLFFBQVEsZUFBZSx5QkFBeUIsTUFBTTtBQUN0RCw4REFBOEQ7QUFDOUQsa0NBQWtDLGlCQUFpQixNQUFNO0FBQ3pELHdDQUF3QztBQUN4QyxXQUFXLGlCQUFpQixNQUFNO0FBQ2xDLHVDQUF1QztBQUN2QyxlQUFlLHFCQUFxQixPQUFPLDRCQUE0QjtBQUN2RSxlQUFlLHFCQUFxQixNQUFNO0FBQzFDLHFDQUFxQztBQUNyQyx1QkFBdUIsT0FBTyxtQkFBbUIsZ0NBQWdDLGlCQUFpQjtBQUNsRyxtRUFBbUU7QUFDbkUsb0NBQW9DO0FBQ3BDLElBQUksMkRBQTJEO0FBQy9ELG1CQUFtQixlQUFlLGtEQUFrRCxlQUFlO0FBQ25HLG9EQUFvRCxtQkFBbUIsbUJBQW1CO0FBQzFGLDJDQUEyQyxNQUFNLHdCQUF3QixTQUFTLFdBQVc7QUFDN0YsQ0FBQyx1REFBdUQsRUFBRSxVQUFVLE9BQU8sWUFBWSxFQUFFLGtCQUFrQixtQkFBbUIsY0FBYyxhQUFhLElBQUk7QUFDN0osV0FBVyxFQUFFLFVBQVUsT0FBTyxZQUFZLEVBQUUsaUJBQWlCLDhCQUE4QixjQUFjO0FBQ3pHLEtBQUssSUFBSSxhQUFhLEVBQUUsVUFBVSxPQUFPLGVBQWUsRUFBRSxpQkFBaUIsYUFBYSxJQUFJLE1BQU0sRUFBRSxVQUFVLE9BQU8scUJBQXFCLEVBQUUsb0JBQW9CLGFBQWEsSUFBSSxZQUFZO0FBQzdMLGFBQWEsMkJBQTJCLFNBQVMseUJBQXlCLFVBQVUsRUFBRSxPQUFPO0FBQzdGLGVBQWUsc0JBQXNCLE1BQU0sRUFBRSxtQkFBbUIsYUFBYSxJQUFJLElBQUksYUFBYSxlQUFlLGlCQUFpQjtBQUNsSSw2QkFBNkIsbUJBQW1CLE1BQU0sa0JBQWtCO0FBQ3hFLHNDQUFzQywyQkFBMkI7QUFDakUsNkJBQTZCLGlCQUFpQixZQUFZLFdBQVcsUUFBUSwyQkFBMkI7QUFDeEcsWUFBWSxtRkFBbUY7QUFDL0YsMkJBQTJCLE9BQU8sSUFBSSxrQkFBa0IsZUFBZTtBQUN2RSxJQUFJLCtCQUErQixTQUFTLFlBQVksSUFBSSxjQUFjLGlCQUFpQjtBQUMzRixVQUFVLGFBQWEsZUFBZSxTQUFTLGFBQWEsbUJBQW1CO0FBQy9FLGFBQWEsbUJBQW1CO0FBQ2hDLDJDQUEyQyxNQUFNO0FBQ2pELHFCQUFxQixrQkFBa0I7QUFDdkMsR0FBRyxnQkFBZ0I7QUFDbkIseUVBQXlFO0FBQ3pFLFFBQVEsR0FBRyxjQUFjLGNBQWMsRUFBRSxtQkFBbUIsYUFBYSxJQUFJO0FBQzdFLHlFQUF5RSxlQUFlO0FBQ3hGLEdBQUcsa0JBQWtCLDZFQUE2RTtBQUNsRyx5Q0FBeUMsTUFBTTtBQUMvQyxHQUFHLGlDQUFpQyxhQUFhLDBDQUEwQztBQUMzRixVQUFVLDBCQUEwQixLQUFLLFFBQVEsRUFBRSw4QkFBOEIsU0FBUztBQUMxRixLQUFLLG1DQUFtQztBQUN4Qyw0REFBNEQsVUFBVTtBQUN0RTtBQUNBLHlFQUF5RTtBQUN6RSx5RkFBeUY7QUFDekYsb0JBQW9CLDZDQUE2QztBQUNqRSx5Q0FBeUMsTUFBTSx1Q0FBdUM7QUFDdEYsTUFBTSxjQUFjLFlBQVkscUNBQXFDLGNBQWM7QUFDbkYsaUJBQWlCLDBCQUEwQjtBQUMzQyxRQUFRLFVBQVUsMERBQTBEO0FBQzVFLFlBQVksNkNBQTZDLGtDQUFrQztBQUMzRixnQkFBZ0IsdUJBQXVCO0FBQ3ZDLHVDQUF1QyxpQkFBaUIsYUFBYSxNQUFNO0FBQzNFLGdCQUFnQixPQUFPLE1BQU0sZ0RBQWdEO0FBQzdFLHFCQUFxQix3Q0FBd0MsV0FBVyxjQUFjO0FBQ3RGLEVBQUUsZUFBZTtBQUNqQixHQUFHLGVBQWUsa0JBQWtCLGVBQWUsa0JBQWtCLGVBQWU7QUFDcEYsR0FBRyxLQUFLLFlBQVksTUFBTSxpQ0FBaUMsZUFBZSxVQUFVLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSw0Q0FBNEM7QUFDdEssdUJBQXVCLHdCQUF3QiwwQ0FBMEM7QUFDekYsR0FBRyxzQkFBc0IsR0FBRyw0QkFBNEIsRUFBRSxVQUFVLE9BQU8sZUFBZSxFQUFFLGlCQUFpQixhQUFhLElBQUksTUFBTSxFQUFFLG1CQUFtQixhQUFhLElBQUksb0NBQW9DLGVBQWU7QUFDN04sS0FBSyxtQkFBbUIsT0FBTyx5QkFBeUI7QUFDeEQsa0VBQWtFO0FBQ2xFLDJCQUEyQiwrQkFBK0I7QUFDMUQ7QUFDQSxtQ0FBbUMsNEJBQTRCLGlCQUFpQjtBQUNoRixTQUFTLG1CQUFtQix3REFBd0Q7QUFDcEY7QUFDQSxrRkFBa0Y7QUFDbEY7QUFDQSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHLE1BQU0sbUVBQW1FLGlCQUFpQjtBQUM3RixHQUFHLE9BQU8sU0FBUyxjQUFjLGNBQWMsWUFBWSxFQUFFLG1CQUFtQixhQUFhLElBQUksOERBQThEO0FBQy9KLHlFQUF5RSx3QkFBd0I7QUFDakcsZUFBZSx1QkFBdUIsc0VBQXNFO0FBQzVHLGtCQUFrQix1QkFBdUIsY0FBYztBQUN2RCwrREFBK0Q7QUFDL0QsV0FBVyw2QkFBNkIsb0JBQW9CLGVBQWU7QUFDM0UsS0FBSyx3Q0FBd0M7QUFDN0M7QUFDQSx1RUFBdUU7QUFDdkUsa0NBQWtDO0FBQ2xDO0FBQ0EsV0FBVyxzQkFBc0IsZ0VBQWdFLGNBQWM7QUFDL0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RUFBNEU7QUFDNUUsU0FBUztBQUNULHNGQUFzRjtBQUN0RixpQ0FBaUMsaUJBQWlCLElBQUk7QUFDdEQ7QUFDQTtBQUNBLDBDQUEwQztBQUMxQyxrQ0FBa0MsbUVBQW1FO0FBQ3JHLDZCQUE2QixVQUFVLGNBQWMsRUFBRSxtQkFBbUIsYUFBYSxJQUFJLGdFQUFnRTtBQUMzSjtBQUNBLG1IQUFtSDtBQUNuSCxNQUFNO0FBQ04sOEVBQThFLG9CQUFvQjtBQUNsRyx5QkFBeUIsSUFBSSxLQUFLLFdBQVcsNENBQTRDLFNBQVM7QUFDbEcsWUFBWSxnQkFBZ0IsSUFBSSxLQUFLLGlDQUFpQztBQUN0RSxhQUFhLFNBQVMsVUFBVSxrQkFBa0IsYUFBYTtBQUMvRCw4QkFBOEIsWUFBWSxXQUFXLEtBQUssV0FBVztBQUNyRTtBQUNBLG1CQUFtQixlQUFlLFVBQVUsY0FBYyxFQUFFLG1CQUFtQixhQUFhLElBQUksSUFBSSxnQkFBZ0IsNkNBQTZDO0FBQ2pLO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHNCQUFzQjtBQUM1Qyx3QkFBd0Isb0JBQW9CO0FBQzVDLHlHQUF5RztBQUN6RztBQUNBLHdCQUF3QixpQkFBaUIsTUFBTSw4QkFBOEIsSUFBSTtBQUNqRixVQUFVLFNBQVMsMkJBQTJCLE9BQU87QUFDckQsd0JBQXdCLDJCQUEyQjtBQUNuRCx1QkFBdUIsb0JBQW9CLG9CQUFvQixpQkFBaUI7QUFDaEYsK0ZBQStGO0FBQy9GLGdCQUFnQix1QkFBdUI7QUFDdkMsR0FBRyxxQkFBcUIsa0NBQWtDLFNBQVMsZ0JBQWdCLFFBQVEsRUFBRTtBQUM3RixxQkFBcUIsVUFBVTtBQUMvQiw4RUFBOEU7QUFDOUUsTUFBTSxzRkFBc0YsVUFBVTtBQUN0RztBQUNBLDRGQUE0RjtBQUM1RixnREFBZ0QseUJBQXlCLDJCQUEyQjtBQUNwRyxXQUFXLDBCQUEwQix1QkFBdUIsV0FBVztBQUN2RSwyQkFBMkIsK0NBQStDLEVBQUUsSUFBSSxRQUFRO0FBQ3hGLDZGQUE2RixFQUFFO0FBQy9GLHNCQUFzQixPQUFPLFlBQVksOEJBQThCLDZCQUE2QjtBQUNwRywyQ0FBMkMsdUJBQXVCLGNBQWMsVUFBVTtBQUMxRixHQUFHLEVBQUUsYUFBYSxhQUFhLElBQUksc0NBQXNDLFNBQVMsRUFBRTtBQUNwRjtBQUNBO0FBQ0E7QUFDQSxpRkFBaUYsaUJBQWlCO0FBQ2xHLHlCQUF5QixnQkFBZ0IsOEJBQThCLGlCQUFpQjtBQUN4RixtQkFBbUIsVUFBVSx3QkFBd0IsbUJBQW1CO0FBQ3hFLG9CQUFvQixrQ0FBa0MsY0FBYyw0QkFBNEI7QUFDaEcsQ0FBQywwQkFBMEIsOEJBQThCLG1CQUFtQjtBQUM1RSxTQUFTLHNCQUFzQixVQUFVLG1CQUFtQixnQkFBZ0IsaUJBQWlCO0FBQzdGLHNDQUFzQyx3QkFBd0IsVUFBVSxxQkFBcUIsZ0JBQWdCO0FBQzdHLHdFQUF3RSxxQkFBcUI7QUFDN0Ysa0JBQWtCLGdCQUFnQiwyQkFBMkI7QUFDN0QsZ0ZBQWdGLGNBQWM7QUFDOUYsV0FBVyxnQkFBZ0IsaUJBQWlCO0FBQzVDLDRDQUE0Qyw4QkFBOEIsVUFBVTtBQUNwRixHQUFHLGdCQUFnQixpQkFBaUI7QUFDcEMsdURBQXVELG9DQUFvQztBQUMzRixpQ0FBaUMsZ0JBQWdCLG1CQUFtQjtBQUNwRSxzREFBc0QsK0JBQStCLFVBQVU7QUFDL0YsR0FBRyxnQkFBZ0IsaUJBQWlCO0FBQ3BDLGtDQUFrQyxVQUFVLCtCQUErQixnQkFBZ0I7QUFDM0YsNkVBQTZFO0FBQzdFLE9BQU8sVUFBVSwyQkFBMkIsZ0JBQWdCLHFCQUFxQjtBQUNqRixxRUFBcUU7QUFDckUsS0FBSyxVQUFVLGlDQUFpQyxnQkFBZ0IsaUJBQWlCO0FBQ2pGLHFDQUFxQyw2QkFBNkIsVUFBVTtBQUM1RSxnQkFBZ0IsaUJBQWlCLHdEQUF3RDtBQUN6RixvQkFBb0IsVUFBVSw0QkFBNEIsZ0JBQWdCLGlCQUFpQjtBQUMzRiwrREFBK0QsdUJBQXVCLFVBQVU7QUFDaEcsR0FBRyxnQkFBZ0IsaUJBQWlCLGtEQUFrRDtBQUN0RixRQUFRLFVBQVUsbUJBQW1CLEVBQUUsY0FBYyxhQUFhLElBQUksdUNBQXVDLFNBQVMsRUFBRSxpQkFBaUIsZ0JBQWdCO0FBQ3pKLDZFQUE2RSxVQUFVO0FBQ3ZGLG1CQUFtQixRQUFRLDZDQUE2QztBQUN4RSxxQkFBcUIsWUFBWTtBQUNqQyxvR0FBb0c7QUFDcEcscUdBQXFHO0FBQ3JHLHFCQUFxQixLQUFLLHNCQUFzQjtBQUNoRCxxQkFBcUIseUNBQXlDLGdCQUFnQjtBQUM5RSwyRUFBMkUsT0FBTztBQUNsRixxRUFBcUUsUUFBUSxNQUFNO0FBQ25GLGtCQUFrQiwwQ0FBMEM7QUFDNUQsR0FBRyw0Q0FBNEMsU0FBUyxtQkFBbUI7QUFDM0UsMERBQTBELGVBQWUsVUFBVSxhQUFhLEVBQUUsY0FBYyxhQUFhLElBQUksdUNBQXVDLFNBQVMsRUFBRTtBQUNuTCxxQ0FBcUMsMEJBQTBCO0FBQy9ELG9CQUFvQixtREFBbUQ7QUFDdkUsYUFBYSxrREFBa0Qsc0JBQXNCO0FBQ3JGLEdBQUcsd0RBQXdEO0FBQzNELG9DQUFvQztBQUNwQyxXQUFXLG9EQUFvRCxpQ0FBaUM7QUFDaEcsc0ZBQXNGO0FBQ3RGLEdBQUc7QUFDSDtBQUNBLG9CQUFvQixrQkFBa0I7QUFDdEMsT0FBTyxJQUFJLHFCQUFxQixtQkFBbUIsOENBQThDLFlBQVk7QUFDN0csU0FBUyxLQUFLLHVCQUF1QjtBQUNyQyxrR0FBa0c7QUFDbEcsd0JBQXdCLElBQUk7QUFDNUI7QUFDQSw4QkFBOEIsb0RBQW9EO0FBQ2xGLGtCQUFrQixnRkFBZ0Y7QUFDbEcscUZBQXFGO0FBQ3JGLFFBQVEsd0JBQXdCO0FBQ2hDLDhDQUE4Qyx3QkFBd0I7QUFDdEUsaUJBQWlCLGdFQUFnRTtBQUNqRixzRkFBc0YsT0FBTyxFQUFFO0FBQy9GLEdBQUcsMkNBQTJDLFNBQVMsT0FBTyxFQUFFLFdBQVcsRUFBRSxnQkFBZ0I7QUFDN0Y7QUFDQSw0REFBNEQ7QUFDNUQ7QUFDQTtBQUNBLFdBQVcsZ0JBQWdCLEVBQUUsY0FBYyxhQUFhLElBQUksdUNBQXVDLFNBQVMsRUFBRTtBQUM5RyxvQ0FBb0MsaUJBQWlCO0FBQ3JELElBQUksZUFBZSw0QkFBNEIsUUFBUSwyQ0FBMkM7QUFDbEcsWUFBWSxPQUFPLCtCQUErQix1QkFBdUIsUUFBUTtBQUNqRixhQUFhLHdCQUF3QixTQUFTLDRDQUE0QztBQUMxRixZQUFZLFVBQVUsb0VBQW9FO0FBQzFGLFlBQVksVUFBVSxzQkFBc0IsS0FBSyxxQkFBcUIsRUFBRTtBQUN4RSw4QkFBOEIsU0FBUyxtREFBbUQ7QUFDMUYsY0FBYyxxQkFBcUIsVUFBVSxtQkFBbUIsRUFBRSxjQUFjLGFBQWEsSUFBSSx1Q0FBdUMsU0FBUyxFQUFFLGlCQUFpQjtBQUNwSywyREFBMkQsZUFBZTtBQUMxRTtBQUNBLHNCQUFzQiwwQkFBMEIsV0FBVyxvQkFBb0I7QUFDL0Usc0NBQXNDLEtBQUssUUFBUSxFQUFFO0FBQ3JELGFBQWEsV0FBVyw4Q0FBOEMsVUFBVSxXQUFXO0FBQzNGLCtGQUErRjtBQUMvRix3QkFBd0IscUNBQXFDO0FBQzdELE1BQU0saUZBQWlGLEtBQUs7QUFDNUYsb0JBQW9CLEtBQUssS0FBSyxNQUFNLG1CQUFtQjtBQUN2RCxxREFBcUQ7QUFDckQsb0JBQW9CLHNFQUFzRTtBQUMxRixVQUFVLDhCQUE4QiwrQkFBK0IsK0JBQStCO0FBQ3RHLGdCQUFnQixrQ0FBa0MsMEJBQTBCLGtDQUFrQztBQUM5RyxvQkFBb0IsK0NBQStDO0FBQ25FLE9BQU8scURBQXFEO0FBQzVELE9BQU8sdURBQXVEO0FBQzlELE9BQU8sK0NBQStDO0FBQ3RELE9BQU8sc0RBQXNEO0FBQzdELE9BQU8sNkRBQTZEO0FBQ3BFLE9BQU8sK0NBQStDLDBDQUEwQztBQUNoRyxvRkFBb0Y7QUFDcEYsMkJBQTJCLDRCQUE0Qix1Q0FBdUM7QUFDOUYsMkJBQTJCLDRCQUE0Qix5Q0FBeUM7QUFDaEcseUJBQXlCLGtDQUFrQywwQkFBMEI7QUFDckYsd0JBQXdCLDJCQUEyQjtBQUNuRCxLQUFLLDBCQUEwQiwyQkFBMkI7QUFDMUQsc0NBQXNDLFlBQVksSUFBSSx5Q0FBeUMsU0FBUztBQUN4RywyQkFBMkI7QUFDM0Isa0RBQWtELGtDQUFrQztBQUNwRixLQUFLLDZEQUE2RCxZQUFZLElBQUk7QUFDbEYsYUFBYSxTQUFTLGFBQWE7QUFDbkM7QUFDQSxnQkFBZ0Isa0NBQWtDLHdDQUF3QztBQUMxRixlQUFlLG1FQUFtRSxZQUFZLElBQUk7QUFDbEcscUNBQXFDLFNBQVMsMkJBQTJCO0FBQ3pFLHlDQUF5QyxZQUFZLElBQUksS0FBSywwQkFBMEI7QUFDeEYsMkJBQTJCLGlDQUFpQyxtQ0FBbUM7QUFDL0Ysc0JBQXNCLG9EQUFvRDtBQUMxRSxPQUFPLDJCQUEyQiwyQkFBMkI7QUFDN0QsUUFBUSwwQ0FBMEMsbUNBQW1DO0FBQ3JGLEtBQUssNkJBQTZCLGtDQUFrQyxVQUFVLGFBQWE7QUFDM0YsdURBQXVELE1BQU0seUJBQXlCO0FBQ3RGLHFCQUFxQiwyQkFBMkIsNENBQTRDLE1BQU07QUFDbEcsNENBQTRDLE1BQU0sbURBQW1EO0FBQ3JHLE1BQU0sMkVBQTJFLE1BQU07QUFDdkYseURBQXlELE1BQU07QUFDL0QsK0JBQStCLFNBQVMsMkJBQTJCLDJCQUEyQixRQUFRO0FBQ3RHLHdCQUF3QixLQUFLLFNBQVMsb0RBQW9EO0FBQzFGLG1FQUFtRTtBQUNuRTtBQUNBO0FBQ0EsZUFBZSxVQUFVLGFBQWEsRUFBRSxjQUFjLGFBQWEsSUFBSSx1Q0FBdUMsU0FBUyxFQUFFO0FBQ3pILDBCQUEwQixZQUFZLDBDQUEwQztBQUNoRixXQUFXLHdCQUF3QixRQUFRLEVBQUUsWUFBWSxzQ0FBc0M7QUFDL0YsaUJBQWlCLG9CQUFvQixRQUFRLEVBQUUsWUFBWSxpQkFBaUIsb0JBQW9CO0FBQ2hHLCtEQUErRCxjQUFjLFlBQVksRUFBRSxVQUFVLE9BQU8sZUFBZSxFQUFFLGFBQWEsc0JBQXNCLEVBQUUsdUJBQXVCO0FBQ3pMLEtBQUssYUFBYSxJQUFJLGdCQUFnQixFQUFFLG1CQUFtQixhQUFhLElBQUksMENBQTBDLHFCQUFxQjtBQUMzSSwyREFBMkQsZUFBZSxlQUFlO0FBQ3pGO0FBQ0EsMkNBQTJDO0FBQzNDLFdBQVcsa0NBQWtDLG1DQUFtQyxFQUFFLGFBQWE7QUFDL0Y7QUFDQSx5RkFBeUYsRUFBRTtBQUMzRix3RUFBd0Usc0JBQXNCO0FBQzlGLHVEQUF1RCxjQUFjO0FBQ3JFLGFBQWEsb0NBQW9DLHlCQUF5QixVQUFVLGNBQWM7QUFDbEcsa0dBQWtHO0FBQ2xHO0FBQ0EsbUJBQW1CLE9BQU8saUJBQWlCO0FBQzNDLDZDQUE2QyxJQUFJLHNCQUFzQixTQUFTO0FBQ2hGLEdBQUcsd0VBQXdFLEVBQUUsbUJBQW1CO0FBQ2hHLFdBQVcsaUJBQWlCLFdBQVcsNkNBQTZDO0FBQ3BGLHNDQUFzQyxFQUFFLGFBQWEsa0NBQWtDLFdBQVc7QUFDbEcsMkJBQTJCLFlBQVksMEJBQTBCLFlBQVksMEJBQTBCO0FBQ3ZHLGtEQUFrRCwrQkFBK0I7QUFDakYsSUFBSSxTQUFTLG9EQUFvRCxTQUFTLHVCQUF1QjtBQUNqRyx1QkFBdUIsUUFBUSxzQkFBc0IsV0FBVyx5QkFBeUIsUUFBUTtBQUNqRyx1Q0FBdUMsT0FBTyw4Q0FBOEMsTUFBTTtBQUNsRyxhQUFhLFFBQVEsb0JBQW9CLE1BQU07QUFDL0Msa0JBQWtCLE9BQU8saUNBQWlDLGtCQUFrQixFQUFFLFNBQVM7QUFDdkYsVUFBVSxZQUFZLDBCQUEwQixxQkFBcUIsMEJBQTBCO0FBQy9GLHlCQUF5QixnQkFBZ0IsNEJBQTRCLG1CQUFtQixVQUFVO0FBQ2xHLFdBQVcsRUFBRSxtQkFBbUIsYUFBYSxJQUFJO0FBQ2pELCtEQUErRCxlQUFlO0FBQzlFO0FBQ0E7QUFDQSxpQkFBaUIsbUZBQW1GO0FBQ3BHLHVEQUF1RCxZQUFZO0FBQ25FO0FBQ0EsNERBQTREO0FBQzVEO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0Esc0NBQXNDLGNBQWM7QUFDcEQsRUFBRSxvQkFBb0IsWUFBWSxnQkFBZ0IsaUNBQWlDLEVBQUU7QUFDckY7QUFDQSxTQUFTLFlBQVksNkJBQTZCO0FBQ2xELFlBQVksaUZBQWlGO0FBQzdGLEtBQUssS0FBSyxFQUFFLE9BQU87QUFDbkIsOENBQThDO0FBQzlDLE1BQU07QUFDTiw4RUFBOEU7QUFDOUUsNEJBQTRCLCtCQUErQiw4QkFBOEI7QUFDekYscUJBQXFCO0FBQ3JCLHVCQUF1QjtBQUN2QjtBQUNBLHVGQUF1RjtBQUN2RixJQUFJLEVBQUUsRUFBRSxXQUFXLE1BQU0saUJBQWlCLE9BQU8saUNBQWlDO0FBQ2xGLFdBQVcsRUFBRSxFQUFFLG9CQUFvQjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0JBQWdCLHNCQUFzQjtBQUNsRCxtQ0FBbUMsZUFBZSx1QkFBdUI7QUFDekUsOEJBQThCLE9BQU8sbURBQW1EO0FBQ3hGLDRCQUE0QixJQUFJLFlBQVksa0JBQWtCO0FBQzlELElBQUkscUVBQXFFLEVBQUU7QUFDM0UsdUJBQXVCLHdDQUF3QyxFQUFFLDBCQUEwQjtBQUMzRixLQUFLLGlFQUFpRSw0QkFBNEIsRUFBRTtBQUNwRyx1QkFBdUI7QUFDdkIsc0RBQXNELEVBQUUsMkJBQTJCO0FBQ25GO0FBQ0EsVUFBVSx3QkFBd0Isa0VBQWtFO0FBQ3BHLHNEQUFzRCx3QkFBd0I7QUFDOUU7QUFDQSwwRkFBMEYsSUFBSSxjQUFjO0FBQzVHLDhHQUE4RztBQUM5RywyQkFBMkI7QUFDM0IsdURBQXVELHNCQUFzQixxQkFBcUI7QUFDbEcsdURBQXVEO0FBQ3ZELGdCQUFnQix1QkFBdUI7QUFDdkMsdUJBQXVCLE9BQU8sMEJBQTBCLE9BQU87QUFDL0QsaUJBQWlCLHlCQUF5Qix5Q0FBeUM7QUFDbkYsa0NBQWtDLDBCQUEwQjtBQUM1RCxZQUFZLHFCQUFxQixtREFBbUQ7QUFDcEYsMERBQTBELHdCQUF3QjtBQUNsRixnRkFBZ0Y7QUFDaEYsdURBQXVELG1CQUFtQjtBQUMxRSxtQkFBbUIsdUJBQXVCLDRCQUE0QixpQkFBaUI7QUFDdkYsU0FBUyxpQkFBaUIsbUNBQW1DLGdDQUFnQztBQUM3Riw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBLCtGQUErRjtBQUMvRixzQkFBc0Isc0JBQXNCO0FBQzVDLGlGQUFpRjtBQUNqRixhQUFhLEVBQUUsa0ZBQWtGO0FBQ2pHLHdDQUF3QyxtQkFBbUIsc0NBQXNDO0FBQ2pHLG1DQUFtQyxpQkFBaUIsdUJBQXVCLFdBQVcsS0FBSztBQUMzRiwyQ0FBMkMsbUNBQW1DLG1CQUFtQjtBQUNqRyxpRkFBaUY7QUFDakYsdUJBQXVCLCtDQUErQyxtQkFBbUI7QUFDekYsNkVBQTZFLEVBQUU7QUFDL0UsMkNBQTJDLGFBQWEsY0FBYztBQUN0RSwrQ0FBK0M7QUFDL0M7QUFDQSw0RkFBNEY7QUFDNUYsbUJBQW1CLHVDQUF1QyxzQ0FBc0M7QUFDaEcsS0FBSyxpQ0FBaUMseUJBQXlCLGlDQUFpQztBQUNoRyxvREFBb0QsdUJBQXVCLHVCQUF1QjtBQUNsRztBQUNBLHlDQUF5QztBQUN6Qyx3QkFBd0IsMEVBQTBFO0FBQ2xHLHlHQUF5RztBQUN6RyxHQUFHLE1BQU0sc0JBQXNCLFFBQVEsd0JBQXdCLE9BQU87QUFDdEUscUJBQXFCLG9DQUFvQztBQUN6RCxrRkFBa0Y7QUFDbEYsYUFBYSw4QkFBOEIsSUFBSSxlQUFlLFVBQVUsWUFBWSxjQUFjLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSwwQ0FBMEM7QUFDbEwscUJBQXFCLHNDQUFzQyw0QkFBNEI7QUFDdkYscURBQXFELGlCQUFpQixzQkFBc0I7QUFDNUYsa0JBQWtCLG9CQUFvQixVQUFVLGNBQWM7QUFDOUQsbURBQW1ELDZCQUE2QixpQkFBaUI7QUFDakcsT0FBTywwQkFBMEIsMEJBQTBCLFlBQVk7QUFDdkUsUUFBUSxZQUFZLG1DQUFtQyxFQUFFLE9BQU8scUJBQXFCO0FBQ3JGLGlCQUFpQix1QkFBdUIseURBQXlEO0FBQ2pHLDhEQUE4RCxtQ0FBbUM7QUFDakcsY0FBYyx5QkFBeUIsMkJBQTJCLGlCQUFpQjtBQUNuRixnQkFBZ0IsNEVBQTRFO0FBQzVGLDJDQUEyQztBQUMzQyxXQUFXLGNBQWM7QUFDekI7QUFDQSxxR0FBcUc7QUFDckc7QUFDQTtBQUNBLGlHQUFpRztBQUNqRyw4Q0FBOEMsY0FBYyx1Q0FBdUM7QUFDbkcseUJBQXlCLE9BQU8sZ0JBQWdCO0FBQ2hELDJCQUEyQix1QkFBdUI7QUFDbEQsT0FBTywrQkFBK0IsK0JBQStCLE9BQU87QUFDNUUsaUJBQWlCLGlDQUFpQyxzQkFBc0IsdUJBQXVCO0FBQy9GLGFBQWEsZUFBZSxlQUFlLHFCQUFxQjtBQUNoRSw0Q0FBNEMsd0NBQXdDLFdBQVc7QUFDL0YsNEJBQTRCO0FBQzVCLHdDQUF3QyxXQUFXLGdCQUFnQjtBQUNuRSxrQ0FBa0MscUNBQXFDO0FBQ3ZFLE9BQU8sc0NBQXNDO0FBQzdDLDJGQUEyRjtBQUMzRixrQ0FBa0MsZ0RBQWdEO0FBQ2xGLGtHQUFrRztBQUNsRyxpQ0FBaUMsdURBQXVEO0FBQ3hGLGdDQUFnQyxhQUFhLG9DQUFvQztBQUNqRixHQUFHLGlCQUFpQixvQ0FBb0MsV0FBVztBQUNuRSxtQkFBbUI7QUFDbkIsUUFBUSx3RkFBd0Y7QUFDaEc7QUFDQSxvRkFBb0YsUUFBUTtBQUM1RixrREFBa0QsS0FBSztBQUN2RCx3QkFBd0Isc0JBQXNCO0FBQzlDO0FBQ0EsTUFBTSxzQ0FBc0MsNENBQTRDO0FBQ3hGLDBCQUEwQixFQUFFLHdCQUF3QjtBQUNwRDtBQUNBLHlGQUF5RjtBQUN6RixtQ0FBbUMsK0JBQStCLGlDQUFpQztBQUNuRyxTQUFTLFdBQVcsbUNBQW1DLGdCQUFnQjtBQUN2RTtBQUNBLHNCQUFzQjtBQUN0Qiw4QkFBOEIsT0FBTyx5QkFBeUI7QUFDOUQsc0RBQXNELE9BQU8sTUFBTTtBQUNuRSxnQkFBZ0IsK0NBQStDO0FBQy9EO0FBQ0EseUJBQXlCLGFBQWEseUJBQXlCLHlCQUF5QjtBQUN4RjtBQUNBLEtBQUssV0FBVyxxQ0FBcUMseUJBQXlCO0FBQzlFLGdCQUFnQixpQkFBaUIsaUJBQWlCLDRCQUE0QixZQUFZO0FBQzFGLHNDQUFzQyxJQUFJLG9CQUFvQjtBQUM5RCwwREFBMEQsRUFBRSxTQUFTLDBCQUEwQixXQUFXO0FBQzFHLG1DQUFtQyxxREFBcUQ7QUFDeEYsdUJBQXVCLGVBQWUseUJBQXlCO0FBQy9ELHVCQUF1QixtQkFBbUIsaUNBQWlDLGdCQUFnQjtBQUMzRixhQUFhLG1CQUFtQixtRUFBbUU7QUFDbkcsOEJBQThCLGFBQWEsVUFBVSxjQUFjLEVBQUUsVUFBVSxPQUFPLGVBQWUsRUFBRSxpQkFBaUIsYUFBYSxJQUFJLE1BQU0sRUFBRSxtQkFBbUIsWUFBWTtBQUNoTDtBQUNBLHdFQUF3RTtBQUN4RSx1REFBdUQ7QUFDdkQsa0NBQWtDO0FBQ2xDLHlGQUF5RjtBQUN6RixvRUFBb0UsbUJBQW1CO0FBQ3ZGLFNBQVMsdUJBQXVCLGFBQWEsYUFBYSxVQUFVLHFCQUFxQjtBQUN6Rix5Q0FBeUMsZ0JBQWdCO0FBQ3pELFVBQVUsRUFBRSxtQkFBbUIsYUFBYSxJQUFJO0FBQ2hEO0FBQ0E7QUFDQSxrQ0FBa0Msb0NBQW9DLGNBQWM7QUFDcEYsUUFBUTtBQUNSO0FBQ0Esa0RBQWtELHFDQUFxQztBQUN2Rix1QkFBdUIscUJBQXFCLGVBQWUsVUFBVTtBQUNyRSx5Q0FBeUMsZ0NBQWdDO0FBQ3pFLE1BQU0sK0JBQStCLG9DQUFvQztBQUN6RSw0RUFBNEU7QUFDNUUsaUJBQWlCLDZCQUE2QixnQ0FBZ0MscUJBQXFCO0FBQ25HLHdEQUF3RCx3QkFBd0I7QUFDaEYsZ0JBQWdCLGdCQUFnQiw0QkFBNEI7QUFDNUQsUUFBUSxjQUFjLHFCQUFxQixFQUFFLHdCQUF3QixrQkFBa0I7QUFDdkYsbURBQW1ELFVBQVU7QUFDN0QsTUFBTTtBQUNOO0FBQ0Esb0RBQW9ELDZDQUE2QztBQUNqRyx5QkFBeUIscURBQXFEO0FBQzlFLG1DQUFtQyxVQUFVLHVDQUF1QyxZQUFZO0FBQ2hHLDhCQUE4QixpRUFBaUU7QUFDL0Ysa0VBQWtFLEVBQUUscUJBQXFCO0FBQ3pGLGNBQWMsaURBQWlELFlBQVk7QUFDM0UsY0FBYyw4QkFBOEIsa0NBQWtDLEVBQUUsbUJBQW1CLGFBQWEsSUFBSTtBQUNwSCx5Q0FBeUMscUJBQXFCO0FBQzlELDJDQUEyQyxrQkFBa0I7QUFDN0QscURBQXFELDBDQUEwQztBQUMvRixtREFBbUQ7QUFDbkQsU0FBUyxtRkFBbUY7QUFDNUYsR0FBRyxrQkFBa0IseUNBQXlDLFlBQVksZ0JBQWdCO0FBQzFGLHdCQUF3QixFQUFFLGlCQUFpQjtBQUMzQyw2REFBNkQsaUNBQWlDO0FBQzlGLHFCQUFxQjtBQUNyQixlQUFlLE9BQU87QUFDdEIsaUJBQWlCLCtCQUErQixnQ0FBZ0M7QUFDaEYsdUJBQXVCLHdEQUF3RDtBQUMvRSxvQkFBb0IsdUJBQXVCLGtDQUFrQyxFQUFFO0FBQy9FLHlCQUF5QixFQUFFLEdBQUcsZ0NBQWdDLE1BQU0saUJBQWlCLE9BQU87QUFDNUYsaUJBQWlCLGtCQUFrQixXQUFXLEVBQUUsR0FBRyxrQ0FBa0MsY0FBYztBQUNuRyx3RUFBd0U7QUFDeEU7QUFDQSxHQUFHLCtFQUErRSxRQUFRO0FBQzFGLGlCQUFpQixRQUFRLGdDQUFnQywwQ0FBMEM7QUFDbkcsc0JBQXNCLGdCQUFnQixpQ0FBaUMseUJBQXlCO0FBQ2hHLHVCQUF1QiwyREFBMkQ7QUFDbEYsdUJBQXVCLHFFQUFxRTtBQUM1RixxREFBcUQ7QUFDckQseUNBQXlDO0FBQ3pDLGdDQUFnQyxNQUFNLDRCQUE0QixXQUFXO0FBQzdFLDJDQUEyQyxNQUFNLDZDQUE2QztBQUM5RixlQUFlLDhCQUE4QjtBQUM3QyxnQkFBZ0IscUJBQXFCLEVBQUUsS0FBSyx1Q0FBdUM7QUFDbkYsb0VBQW9FO0FBQ3BFLDZDQUE2QywrQkFBK0IsT0FBTztBQUNuRixLQUFLLE9BQU8sbUNBQW1DLFdBQVcsMEJBQTBCLHFCQUFxQjtBQUN6RywrQkFBK0IscURBQXFEO0FBQ3BGLHlFQUF5RTtBQUN6RSwrQkFBK0IsMENBQTBDO0FBQ3pFLFFBQVEsd0NBQXdDLHVDQUF1QyxFQUFFLG1CQUFtQixhQUFhLElBQUksZ0JBQWdCLEVBQUUsbUJBQW1CLGFBQWEsSUFBSSxxQ0FBcUMsaUJBQWlCO0FBQ3pPLElBQUksTUFBTSwwQkFBMEIsZUFBZSxZQUFZLG9CQUFvQjtBQUNuRixZQUFZO0FBQ1osbUVBQW1FLE9BQU87QUFDMUUsOEZBQThGO0FBQzlGLG9CQUFvQixXQUFXLElBQUksZUFBZSxTQUFTLHVDQUF1QztBQUNsRyxvQ0FBb0MsUUFBUSxLQUFLLEdBQUcsRUFBRSxJQUFJLElBQUksS0FBSyxLQUFLLElBQUk7QUFDNUU7QUFDQSx3QkFBd0Isa0RBQWtEO0FBQzFFLGFBQWEscUJBQXFCLDZCQUE2QjtBQUMvRCxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBLHVCQUF1QixrQkFBa0IsVUFBVSxVQUFVLFVBQVUsZUFBZSx3QkFBd0IsR0FBRyxFQUFFO0FBQ25ILDREQUE0RCxNQUFNO0FBQ2xFLHNCQUFzQixpQkFBaUIsTUFBTTtBQUM3QyxFQUFFLDBDQUEwQztBQUM1QyxrR0FBa0c7QUFDbEcsS0FBSyxJQUFJLDhFQUE4RSxVQUFVLElBQUk7QUFDckcsNkVBQTZFLFVBQVUsSUFBSTtBQUMzRixrQ0FBa0MsZUFBZSxrQkFBa0IsRUFBRTtBQUNyRSxxQ0FBcUMsWUFBWSxJQUFJLEtBQUssZ0JBQWdCO0FBQzFFLEVBQUUsS0FBSyxhQUFhLEdBQUcsU0FBUyxzQkFBc0Isa0JBQWtCLEVBQUU7QUFDMUUsNkNBQTZDLGNBQWMsSUFBSSxnQkFBZ0IsaUJBQWlCLGVBQWUsY0FBYyx3QkFBd0IsbUJBQW1CO0FBQ3hLLElBQUksbUJBQW1CLE1BQU0sdUJBQXVCLElBQUksaUNBQWlDO0FBQ3pGLGtDQUFrQztBQUNsQyxHQUFHLEtBQUssOEVBQThFO0FBQ3RGLE1BQU0sS0FBSyxJQUFJLFNBQVMsR0FBRztBQUMzQixzQkFBc0IsV0FBVyxvQkFBb0Isc0JBQXNCLGVBQWU7QUFDMUYscUJBQXFCLFVBQVUsNEJBQTRCLDhCQUE4QixlQUFlLFNBQVMsNkJBQTZCO0FBQzlJLGVBQWUsaUJBQWlCLGVBQWUsbUJBQW1CLDJCQUEyQjtBQUM3RixpQkFBaUIsZ0JBQWdCLGlCQUFpQixnQkFBZ0IsaUJBQWlCO0FBQ25GLHFCQUFxQixlQUFlLGVBQWUsa0JBQWtCLHNCQUFzQjtBQUMzRixtR0FBbUc7QUFDbkcsa0JBQWtCO0FBQ2xCO0FBQ0EsdUZBQXVGO0FBQ3ZGLG9DQUFvQyw2QkFBNkIsZUFBZSxJQUFJLGlCQUFpQjtBQUNyRyw4QkFBOEIsT0FBTyxxREFBcUQ7QUFDMUYsV0FBVyxlQUFlO0FBQzFCLFlBQVksR0FBRyxFQUFFO0FBQ2pCLGdEQUFnRCxNQUFNLElBQUksUUFBUSxNQUFNO0FBQ3hFLHlGQUF5RixJQUFJO0FBQzdGLHdDQUF3QyxHQUFHO0FBQzNDO0FBQ0EsaUJBQWlCLG1CQUFtQjtBQUNwQyxrRkFBa0YsTUFBTTtBQUN4RjtBQUNBLG9EQUFvRCw2QkFBNkI7QUFDakYseUJBQXlCLHFCQUFxQixxREFBcUQ7QUFDbkcsMkRBQTJELGNBQWM7QUFDekUsSUFBSSxFQUFFLGlEQUFpRCxFQUFFLGlCQUFpQix3QkFBd0I7QUFDbEcsZ0NBQWdDLHlCQUF5Qix3QkFBd0IsY0FBYztBQUMvRix5QkFBeUIsMENBQTBDLHVCQUF1QjtBQUMxRjtBQUNBO0FBQ0EsZ0RBQWdELHVCQUF1QixFQUFFLFNBQVM7QUFDbEYsd0NBQXdDLG1DQUFtQyxvQkFBb0I7QUFDL0Ysa0ZBQWtGLGlCQUFpQjtBQUNuRyw0QkFBNEIsR0FBRztBQUMvQjtBQUNBLEtBQUssT0FBTyxJQUFJLHVCQUF1QixzREFBc0QsRUFBRTtBQUMvRiw4Q0FBOEMsRUFBRSxHQUFHLHdCQUF3QixVQUFVO0FBQ3JGLE9BQU8scUJBQXFCLGdCQUFnQjtBQUM1QywyQkFBMkIscUJBQXFCLGVBQWU7QUFDL0QsR0FBRyxhQUFhLDJDQUEyQyxFQUFFLEVBQUUsS0FBSyxXQUFXO0FBQy9FLGdCQUFnQixhQUFhLHdDQUF3QyxHQUFHLEtBQUssSUFBSSxTQUFTLElBQUk7QUFDOUYsS0FBSywwQ0FBMEMsa0NBQWtDLFFBQVEsS0FBSztBQUM5RixPQUFPLDBDQUEwQyxFQUFFLEtBQUssRUFBRSxLQUFLLHdCQUF3QjtBQUN2RixnQkFBZ0IsbUJBQW1CLGVBQWUsaUJBQWlCLFlBQVksVUFBVTtBQUN6Riw0Q0FBNEMsU0FBUztBQUNyRCxHQUFHLFdBQVcsMkRBQTJELHlCQUF5QjtBQUNsRyxlQUFlLGtDQUFrQyxFQUFFO0FBQ25EO0FBQ0EsbUZBQW1GO0FBQ25GLHdEQUF3RCwyQkFBMkI7QUFDbkYsK0JBQStCLDRCQUE0QixrQ0FBa0M7QUFDN0YsMENBQTBDO0FBQzFDLE9BQU8sVUFBVSxPQUFPLHFCQUFxQixJQUFJLGdCQUFnQixJQUFJLGdCQUFnQixrQ0FBa0MsZUFBZSxTQUFTLGNBQWMsaUJBQWlCO0FBQzlLLGtCQUFrQixXQUFXLElBQUksYUFBYSxNQUFNO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLCtFQUErRTtBQUMvRTtBQUNBLDJEQUEyRDtBQUMzRDtBQUNBLHdFQUF3RSxFQUFFLFFBQVEsRUFBRTtBQUNwRjtBQUNBLHFEQUFxRDtBQUNyRCxpQ0FBaUMsbUNBQW1DLHNCQUFzQjtBQUMxRiw0Q0FBNEM7QUFDNUM7QUFDQSxhQUFhLHNDQUFzQyxZQUFZO0FBQy9ELHVCQUF1QixFQUFFLFNBQVMsaUNBQWlDO0FBQ25FO0FBQ0EsaUJBQWlCLGdFQUFnRTtBQUNqRixrR0FBa0c7QUFDbEcsaUNBQWlDO0FBQ2pDLEtBQUssNkJBQTZCLGFBQWEscUJBQXFCO0FBQ3BFLCtEQUErRCwyQkFBMkIsRUFBRSxtQkFBbUIsRUFBRSxpQkFBaUIsRUFBRTtBQUNwSSxxRkFBcUY7QUFDckYsNkZBQTZGO0FBQzdGO0FBQ0EsY0FBYztBQUNkLDRCQUE0QjtBQUM1Qiw2QkFBNkIsa0JBQWtCLGdCQUFnQjtBQUMvRCxvREFBb0QsWUFBWSxNQUFNO0FBQ3RFLCtDQUErQyxxQ0FBcUM7QUFDcEYsZ0JBQWdCO0FBQ2hCO0FBQ0Esb0NBQW9DLGtCQUFrQixnQkFBZ0IsY0FBYztBQUNwRixtR0FBbUc7QUFDbkcsbUJBQW1CLGtCQUFrQixnQkFBZ0I7QUFDckQsNkNBQTZDLGtCQUFrQixnQkFBZ0IsY0FBYztBQUM3Rix5Q0FBeUM7QUFDekM7QUFDQSxtQkFBbUIsVUFBVSxLQUFLLGdCQUFnQixpQkFBaUIsWUFBWSwwQkFBMEIsMEJBQTBCLFlBQVk7QUFDL0ksc0JBQXNCLFFBQVEsRUFBRSxPQUFPLHFCQUFxQixrQkFBa0IsZ0NBQWdDO0FBQzlHLG9CQUFvQixvQkFBb0IsMENBQTBDO0FBQ2xGLFlBQVksUUFBUSx5RUFBeUU7QUFDN0Y7QUFDQSxPQUFPLHFDQUFxQyx5QkFBeUIsYUFBYSxJQUFJO0FBQ3RGLGlFQUFpRSxVQUFVLEdBQUcsY0FBYyxHQUFHLFVBQVU7QUFDekcsR0FBRyxjQUFjLGtEQUFrRCxNQUFNO0FBQ3pFLGtCQUFrQixhQUFhLG1DQUFtQztBQUNsRSxJQUFJLFNBQVMsS0FBSyxrQkFBa0IsaUJBQWlCLFVBQVUsS0FBSyx5QkFBeUIsMENBQTBDLGdDQUFnQztBQUdoSjtBQUN2Qjs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxQcm9qZWN0c1xcaW5zcGVjdGlvbi1vcmRlci1tZ210XFxvcmRlci1tZ210LWFwaVxcbm9kZV9tb2R1bGVzXFxAbmVvbmRhdGFiYXNlXFxzZXJ2ZXJsZXNzXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHZvPU9iamVjdC5jcmVhdGU7dmFyIFRlPU9iamVjdC5kZWZpbmVQcm9wZXJ0eTt2YXIgeG89T2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcjt2YXIgU289T2JqZWN0LmdldE93blByb3BlcnR5TmFtZXM7dmFyIEVvPU9iamVjdC5nZXRQcm90b3R5cGVPZixBbz1PYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O3ZhciBDbz0ocixlLHQpPT5lIGluIHI/VGUocixlLHtlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMCx2YWx1ZTp0fSk6cltlXT10O3ZhciBhPShyLGUpPT5UZShyLFwibmFtZVwiLHt2YWx1ZTplLGNvbmZpZ3VyYWJsZTohMH0pO3ZhciB6PShyLGUpPT4oKT0+KHImJihlPXIocj0wKSksZSk7dmFyIEk9KHIsZSk9PigpPT4oZXx8cigoZT17ZXhwb3J0czp7fX0pLmV4cG9ydHMsZSksZS5leHBvcnRzKSxuZT0ocixlKT0+e2Zvcih2YXIgdCBpbiBlKVRlKHIsdCx7Z2V0OmVbdF0sXG5lbnVtZXJhYmxlOiEwfSl9LE1uPShyLGUsdCxuKT0+e2lmKGUmJnR5cGVvZiBlPT1cIm9iamVjdFwifHx0eXBlb2YgZT09XCJmdW5jdGlvblwiKWZvcihsZXQgaSBvZiBTbyhlKSkhQW8uXG5jYWxsKHIsaSkmJmkhPT10JiZUZShyLGkse2dldDooKT0+ZVtpXSxlbnVtZXJhYmxlOiEobj14byhlLGkpKXx8bi5lbnVtZXJhYmxlfSk7cmV0dXJuIHJ9O3ZhciB4ZT0ocixlLHQpPT4odD1yIT1udWxsP3ZvKEVvKHIpKTp7fSxNbihlfHwhcnx8IXIuX19lc01vZHVsZT9UZSh0LFwiZGVmYXVsdFwiLHt2YWx1ZTpyLGVudW1lcmFibGU6ITB9KTpcbnQscikpLEQ9cj0+TW4oVGUoe30sXCJfX2VzTW9kdWxlXCIse3ZhbHVlOiEwfSkscik7dmFyIEU9KHIsZSx0KT0+Q28ocix0eXBlb2YgZSE9XCJzeW1ib2xcIj9lK1wiXCI6ZSx0KTt2YXIgT249SShjdD0+e1widXNlIHN0cmljdFwiO3AoKTtjdC5ieXRlTGVuZ3RoPVRvO2N0LnRvQnl0ZUFycmF5PVBvO2N0LmZyb21CeXRlQXJyYXk9TG87dmFyIHVlPVtdLGVlPVtdLFxuX289dHlwZW9mIFVpbnQ4QXJyYXk8XCJ1XCI/VWludDhBcnJheTpBcnJheSxVdD1cIkFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMVxcXG4yMzQ1Njc4OSsvXCI7Zm9yKFNlPTAsVW49VXQubGVuZ3RoO1NlPFVuOysrU2UpdWVbU2VdPVV0W1NlXSxlZVtVdC5jaGFyQ29kZUF0KFNlKV09U2U7dmFyIFNlLFVuO2VlWzQ1XT1cbjYyO2VlWzk1XT02MztmdW5jdGlvbiBEbihyKXt2YXIgZT1yLmxlbmd0aDtpZihlJTQ+MCl0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHN0cmluZy4gTGVuZ3RoIG11c3QgYmUgXFxcbmEgbXVsdGlwbGUgb2YgNFwiKTt2YXIgdD1yLmluZGV4T2YoXCI9XCIpO3Q9PT0tMSYmKHQ9ZSk7dmFyIG49dD09PWU/MDo0LXQlNDtyZXR1cm5bdCxuXX1hKERuLFwiZ2V0TGVuc1wiKTtcbmZ1bmN0aW9uIFRvKHIpe3ZhciBlPURuKHIpLHQ9ZVswXSxuPWVbMV07cmV0dXJuKHQrbikqMy80LW59YShUbyxcImJ5dGVMZW5ndGhcIik7ZnVuY3Rpb24gSW8ocixlLHQpe3JldHVybihlK1xudCkqMy80LXR9YShJbyxcIl9ieXRlTGVuZ3RoXCIpO2Z1bmN0aW9uIFBvKHIpe3ZhciBlLHQ9RG4ociksbj10WzBdLGk9dFsxXSxzPW5ldyBfbyhJbyhyLG4saSkpLG89MCx1PWk+XG4wP24tNDpuLGM7Zm9yKGM9MDtjPHU7Yys9NCllPWVlW3IuY2hhckNvZGVBdChjKV08PDE4fGVlW3IuY2hhckNvZGVBdChjKzEpXTw8MTJ8ZWVbci5jaGFyQ29kZUF0KGMrMildPDxcbjZ8ZWVbci5jaGFyQ29kZUF0KGMrMyldLHNbbysrXT1lPj4xNiYyNTUsc1tvKytdPWU+PjgmMjU1LHNbbysrXT1lJjI1NTtyZXR1cm4gaT09PTImJihlPWVlW3IuY2hhckNvZGVBdChcbmMpXTw8MnxlZVtyLmNoYXJDb2RlQXQoYysxKV0+PjQsc1tvKytdPWUmMjU1KSxpPT09MSYmKGU9ZWVbci5jaGFyQ29kZUF0KGMpXTw8MTB8ZWVbci5jaGFyQ29kZUF0KGMrMSldPDxcbjR8ZWVbci5jaGFyQ29kZUF0KGMrMildPj4yLHNbbysrXT1lPj44JjI1NSxzW28rK109ZSYyNTUpLHN9YShQbyxcInRvQnl0ZUFycmF5XCIpO2Z1bmN0aW9uIFJvKHIpe3JldHVybiB1ZVtyPj5cbjE4JjYzXSt1ZVtyPj4xMiY2M10rdWVbcj4+NiY2M10rdWVbciY2M119YShSbyxcInRyaXBsZXRUb0Jhc2U2NFwiKTtmdW5jdGlvbiBCbyhyLGUsdCl7Zm9yKHZhciBuLGk9W10scz1lO3M8XG50O3MrPTMpbj0ocltzXTw8MTYmMTY3MTE2ODApKyhyW3MrMV08PDgmNjUyODApKyhyW3MrMl0mMjU1KSxpLnB1c2goUm8obikpO3JldHVybiBpLmpvaW4oXCJcIil9YShCbyxcImVuXFxcbmNvZGVDaHVua1wiKTtmdW5jdGlvbiBMbyhyKXtmb3IodmFyIGUsdD1yLmxlbmd0aCxuPXQlMyxpPVtdLHM9MTYzODMsbz0wLHU9dC1uO288dTtvKz1zKWkucHVzaChCbyhyLG8sXG5vK3M+dT91Om8rcykpO3JldHVybiBuPT09MT8oZT1yW3QtMV0saS5wdXNoKHVlW2U+PjJdK3VlW2U8PDQmNjNdK1wiPT1cIikpOm49PT0yJiYoZT0oclt0LTJdPDw4KStyW3QtMV0sXG5pLnB1c2godWVbZT4+MTBdK3VlW2U+PjQmNjNdK3VlW2U8PDImNjNdK1wiPVwiKSksaS5qb2luKFwiXCIpfWEoTG8sXCJmcm9tQnl0ZUFycmF5XCIpfSk7dmFyIHFuPUkoRHQ9PntwKCk7RHQucmVhZD1mdW5jdGlvbihyLGUsdCxuLGkpe3ZhciBzLG8sdT1pKjgtbi0xLGM9KDE8PHUpLTEsbD1jPj4xLGY9LTcseT10P2ktMTowLGc9dD9cbi0xOjEsQT1yW2UreV07Zm9yKHkrPWcscz1BJigxPDwtZiktMSxBPj49LWYsZis9dTtmPjA7cz1zKjI1NityW2UreV0seSs9ZyxmLT04KTtmb3Iobz1zJigxPDwtZiktMSxzPj49XG4tZixmKz1uO2Y+MDtvPW8qMjU2K3JbZSt5XSx5Kz1nLGYtPTgpO2lmKHM9PT0wKXM9MS1sO2Vsc2V7aWYocz09PWMpcmV0dXJuIG8/TmFOOihBPy0xOjEpKigxLzApO289bytNYXRoLlxucG93KDIsbikscz1zLWx9cmV0dXJuKEE/LTE6MSkqbypNYXRoLnBvdygyLHMtbil9O0R0LndyaXRlPWZ1bmN0aW9uKHIsZSx0LG4saSxzKXt2YXIgbyx1LGMsbD1zKjgtaS0xLFxuZj0oMTw8bCktMSx5PWY+PjEsZz1pPT09MjM/TWF0aC5wb3coMiwtMjQpLU1hdGgucG93KDIsLTc3KTowLEE9bj8wOnMtMSxDPW4/MTotMSxRPWU8MHx8ZT09PTAmJjEvZTwwP1xuMTowO2ZvcihlPU1hdGguYWJzKGUpLGlzTmFOKGUpfHxlPT09MS8wPyh1PWlzTmFOKGUpPzE6MCxvPWYpOihvPU1hdGguZmxvb3IoTWF0aC5sb2coZSkvTWF0aC5MTjIpLGUqKGM9XG5NYXRoLnBvdygyLC1vKSk8MSYmKG8tLSxjKj0yKSxvK3k+PTE/ZSs9Zy9jOmUrPWcqTWF0aC5wb3coMiwxLXkpLGUqYz49MiYmKG8rKyxjLz0yKSxvK3k+PWY/KHU9MCxvPWYpOlxubyt5Pj0xPyh1PShlKmMtMSkqTWF0aC5wb3coMixpKSxvPW8reSk6KHU9ZSpNYXRoLnBvdygyLHktMSkqTWF0aC5wb3coMixpKSxvPTApKTtpPj04O3JbdCtBXT11JjI1NSxBKz1cbkMsdS89MjU2LGktPTgpO2ZvcihvPW88PGl8dSxsKz1pO2w+MDtyW3QrQV09byYyNTUsQSs9QyxvLz0yNTYsbC09OCk7clt0K0EtQ118PVEqMTI4fX0pO3ZhciByaT1JKEJlPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciBPdD1PbigpLFBlPXFuKCksUW49dHlwZW9mIFN5bWJvbD09XCJmdW5jdGlvblwiJiZ0eXBlb2YgU3ltYm9sLmZvcj09XG5cImZ1bmN0aW9uXCI/U3ltYm9sLmZvcihcIm5vZGVqcy51dGlsLmluc3BlY3QuY3VzdG9tXCIpOm51bGw7QmUuQnVmZmVyPWg7QmUuU2xvd0J1ZmZlcj1PbztCZS5JTlNQRUNUX01BWF9CWVRFUz1cbjUwO3ZhciBsdD0yMTQ3NDgzNjQ3O0JlLmtNYXhMZW5ndGg9bHQ7aC5UWVBFRF9BUlJBWV9TVVBQT1JUPUZvKCk7IWguVFlQRURfQVJSQVlfU1VQUE9SVCYmdHlwZW9mIGNvbnNvbGU8XG5cInVcIiYmdHlwZW9mIGNvbnNvbGUuZXJyb3I9PVwiZnVuY3Rpb25cIiYmY29uc29sZS5lcnJvcihcIlRoaXMgYnJvd3NlciBsYWNrcyB0eXBlZCBhcnJheSAoVWludDhBcnJheSkgc3VcXFxucHBvcnQgd2hpY2ggaXMgcmVxdWlyZWQgYnkgYGJ1ZmZlcmAgdjUueC4gVXNlIGBidWZmZXJgIHY0LnggaWYgeW91IHJlcXVpcmUgb2xkIGJyb3dzZXIgc3VwcG9ydC5cIik7ZnVuY3Rpb24gRm8oKXtcbnRyeXtsZXQgcj1uZXcgVWludDhBcnJheSgxKSxlPXtmb286YShmdW5jdGlvbigpe3JldHVybiA0Mn0sXCJmb29cIil9O3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2YoZSxVaW50OEFycmF5LlxucHJvdG90eXBlKSxPYmplY3Quc2V0UHJvdG90eXBlT2YocixlKSxyLmZvbygpPT09NDJ9Y2F0Y2h7cmV0dXJuITF9fWEoRm8sXCJ0eXBlZEFycmF5U3VwcG9ydFwiKTtPYmplY3QuXG5kZWZpbmVQcm9wZXJ0eShoLnByb3RvdHlwZSxcInBhcmVudFwiLHtlbnVtZXJhYmxlOiEwLGdldDphKGZ1bmN0aW9uKCl7aWYoaC5pc0J1ZmZlcih0aGlzKSlyZXR1cm4gdGhpcy5cbmJ1ZmZlcn0sXCJnZXRcIil9KTtPYmplY3QuZGVmaW5lUHJvcGVydHkoaC5wcm90b3R5cGUsXCJvZmZzZXRcIix7ZW51bWVyYWJsZTohMCxnZXQ6YShmdW5jdGlvbigpe2lmKGguaXNCdWZmZXIoXG50aGlzKSlyZXR1cm4gdGhpcy5ieXRlT2Zmc2V0fSxcImdldFwiKX0pO2Z1bmN0aW9uIHBlKHIpe2lmKHI+bHQpdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ1RoZSB2YWx1ZSBcIicrcisnXFxcblwiIGlzIGludmFsaWQgZm9yIG9wdGlvbiBcInNpemVcIicpO2xldCBlPW5ldyBVaW50OEFycmF5KHIpO3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2YoZSxoLnByb3RvdHlwZSksXG5lfWEocGUsXCJjcmVhdGVCdWZmZXJcIik7ZnVuY3Rpb24gaChyLGUsdCl7aWYodHlwZW9mIHI9PVwibnVtYmVyXCIpe2lmKHR5cGVvZiBlPT1cInN0cmluZ1wiKXRocm93IG5ldyBUeXBlRXJyb3IoXG4nVGhlIFwic3RyaW5nXCIgYXJndW1lbnQgbXVzdCBiZSBvZiB0eXBlIHN0cmluZy4gUmVjZWl2ZWQgdHlwZSBudW1iZXInKTtyZXR1cm4ganQocil9cmV0dXJuIEhuKHIsZSx0KX1cbmEoaCxcIkJ1ZmZlclwiKTtoLnBvb2xTaXplPTgxOTI7ZnVuY3Rpb24gSG4ocixlLHQpe2lmKHR5cGVvZiByPT1cInN0cmluZ1wiKXJldHVybiBNbyhyLGUpO2lmKEFycmF5QnVmZmVyLlxuaXNWaWV3KHIpKXJldHVybiBVbyhyKTtpZihyPT1udWxsKXRocm93IG5ldyBUeXBlRXJyb3IoXCJUaGUgZmlyc3QgYXJndW1lbnQgbXVzdCBiZSBvbmUgb2YgdHlwZSBzdHJpbmdcXFxuLCBCdWZmZXIsIEFycmF5QnVmZmVyLCBBcnJheSwgb3IgQXJyYXktbGlrZSBPYmplY3QuIFJlY2VpdmVkIHR5cGUgXCIrdHlwZW9mIHIpO2lmKGNlKHIsQXJyYXlCdWZmZXIpfHxcbnImJmNlKHIuYnVmZmVyLEFycmF5QnVmZmVyKXx8dHlwZW9mIFNoYXJlZEFycmF5QnVmZmVyPFwidVwiJiYoY2UocixTaGFyZWRBcnJheUJ1ZmZlcil8fHImJmNlKHIuYnVmZmVyLFxuU2hhcmVkQXJyYXlCdWZmZXIpKSlyZXR1cm4gUXQocixlLHQpO2lmKHR5cGVvZiByPT1cIm51bWJlclwiKXRocm93IG5ldyBUeXBlRXJyb3IoJ1RoZSBcInZhbHVlXCIgYXJndW1lbnRcXFxuIG11c3Qgbm90IGJlIG9mIHR5cGUgbnVtYmVyLiBSZWNlaXZlZCB0eXBlIG51bWJlcicpO2xldCBuPXIudmFsdWVPZiYmci52YWx1ZU9mKCk7aWYobiE9bnVsbCYmbiE9PXIpcmV0dXJuIGguXG5mcm9tKG4sZSx0KTtsZXQgaT1EbyhyKTtpZihpKXJldHVybiBpO2lmKHR5cGVvZiBTeW1ib2w8XCJ1XCImJlN5bWJvbC50b1ByaW1pdGl2ZSE9bnVsbCYmdHlwZW9mIHJbU3ltYm9sLlxudG9QcmltaXRpdmVdPT1cImZ1bmN0aW9uXCIpcmV0dXJuIGguZnJvbShyW1N5bWJvbC50b1ByaW1pdGl2ZV0oXCJzdHJpbmdcIiksZSx0KTt0aHJvdyBuZXcgVHlwZUVycm9yKFwiVGhlXFxcbiBmaXJzdCBhcmd1bWVudCBtdXN0IGJlIG9uZSBvZiB0eXBlIHN0cmluZywgQnVmZmVyLCBBcnJheUJ1ZmZlciwgQXJyYXksIG9yIEFycmF5LWxpa2UgT2JqZWN0LiBSZWNlaXZcXFxuZWQgdHlwZSBcIit0eXBlb2Ygcil9YShIbixcImZyb21cIik7aC5mcm9tPWZ1bmN0aW9uKHIsZSx0KXtyZXR1cm4gSG4ocixlLHQpfTtPYmplY3Quc2V0UHJvdG90eXBlT2YoaC5wcm90b3R5cGUsXG5VaW50OEFycmF5LnByb3RvdHlwZSk7T2JqZWN0LnNldFByb3RvdHlwZU9mKGgsVWludDhBcnJheSk7ZnVuY3Rpb24gJG4ocil7aWYodHlwZW9mIHIhPVwibnVtYmVyXCIpdGhyb3cgbmV3IFR5cGVFcnJvcihcbidcInNpemVcIiBhcmd1bWVudCBtdXN0IGJlIG9mIHR5cGUgbnVtYmVyJyk7aWYocjwwKXRocm93IG5ldyBSYW5nZUVycm9yKCdUaGUgdmFsdWUgXCInK3IrJ1wiIGlzIGludmFsaWQgXFxcbmZvciBvcHRpb24gXCJzaXplXCInKX1hKCRuLFwiYXNzZXJ0U2l6ZVwiKTtmdW5jdGlvbiBrbyhyLGUsdCl7cmV0dXJuICRuKHIpLHI8PTA/cGUocik6ZSE9PXZvaWQgMD90eXBlb2YgdD09XG5cInN0cmluZ1wiP3BlKHIpLmZpbGwoZSx0KTpwZShyKS5maWxsKGUpOnBlKHIpfWEoa28sXCJhbGxvY1wiKTtoLmFsbG9jPWZ1bmN0aW9uKHIsZSx0KXtyZXR1cm4ga28ocixlLHQpfTtcbmZ1bmN0aW9uIGp0KHIpe3JldHVybiAkbihyKSxwZShyPDA/MDpXdChyKXwwKX1hKGp0LFwiYWxsb2NVbnNhZmVcIik7aC5hbGxvY1Vuc2FmZT1mdW5jdGlvbihyKXtyZXR1cm4ganQoXG5yKX07aC5hbGxvY1Vuc2FmZVNsb3c9ZnVuY3Rpb24ocil7cmV0dXJuIGp0KHIpfTtmdW5jdGlvbiBNbyhyLGUpe2lmKCh0eXBlb2YgZSE9XCJzdHJpbmdcInx8ZT09PVwiXCIpJiYoZT1cblwidXRmOFwiKSwhaC5pc0VuY29kaW5nKGUpKXRocm93IG5ldyBUeXBlRXJyb3IoXCJVbmtub3duIGVuY29kaW5nOiBcIitlKTtsZXQgdD1HbihyLGUpfDAsbj1wZSh0KSxpPW4ud3JpdGUoXG5yLGUpO3JldHVybiBpIT09dCYmKG49bi5zbGljZSgwLGkpKSxufWEoTW8sXCJmcm9tU3RyaW5nXCIpO2Z1bmN0aW9uIHF0KHIpe2xldCBlPXIubGVuZ3RoPDA/MDpXdChyLmxlbmd0aCl8XG4wLHQ9cGUoZSk7Zm9yKGxldCBuPTA7bjxlO24rPTEpdFtuXT1yW25dJjI1NTtyZXR1cm4gdH1hKHF0LFwiZnJvbUFycmF5TGlrZVwiKTtmdW5jdGlvbiBVbyhyKXtpZihjZShyLFVpbnQ4QXJyYXkpKXtcbmxldCBlPW5ldyBVaW50OEFycmF5KHIpO3JldHVybiBRdChlLmJ1ZmZlcixlLmJ5dGVPZmZzZXQsZS5ieXRlTGVuZ3RoKX1yZXR1cm4gcXQocil9YShVbyxcImZyb21BcnJheVZpXFxcbmV3XCIpO2Z1bmN0aW9uIFF0KHIsZSx0KXtpZihlPDB8fHIuYnl0ZUxlbmd0aDxlKXRocm93IG5ldyBSYW5nZUVycm9yKCdcIm9mZnNldFwiIGlzIG91dHNpZGUgb2YgYnVmZmVyIGJcXFxub3VuZHMnKTtpZihyLmJ5dGVMZW5ndGg8ZSsodHx8MCkpdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ1wibGVuZ3RoXCIgaXMgb3V0c2lkZSBvZiBidWZmZXIgYm91bmRzJyk7bGV0IG47XG5yZXR1cm4gZT09PXZvaWQgMCYmdD09PXZvaWQgMD9uPW5ldyBVaW50OEFycmF5KHIpOnQ9PT12b2lkIDA/bj1uZXcgVWludDhBcnJheShyLGUpOm49bmV3IFVpbnQ4QXJyYXkoXG5yLGUsdCksT2JqZWN0LnNldFByb3RvdHlwZU9mKG4saC5wcm90b3R5cGUpLG59YShRdCxcImZyb21BcnJheUJ1ZmZlclwiKTtmdW5jdGlvbiBEbyhyKXtpZihoLmlzQnVmZmVyKHIpKXtcbmxldCBlPVd0KHIubGVuZ3RoKXwwLHQ9cGUoZSk7cmV0dXJuIHQubGVuZ3RoPT09MHx8ci5jb3B5KHQsMCwwLGUpLHR9aWYoci5sZW5ndGghPT12b2lkIDApcmV0dXJuIHR5cGVvZiByLlxubGVuZ3RoIT1cIm51bWJlclwifHwkdChyLmxlbmd0aCk/cGUoMCk6cXQocik7aWYoci50eXBlPT09XCJCdWZmZXJcIiYmQXJyYXkuaXNBcnJheShyLmRhdGEpKXJldHVybiBxdChyLmRhdGEpfVxuYShEbyxcImZyb21PYmplY3RcIik7ZnVuY3Rpb24gV3Qocil7aWYocj49bHQpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJBdHRlbXB0IHRvIGFsbG9jYXRlIEJ1ZmZlciBsYXJnZXIgdFxcXG5oYW4gbWF4aW11bSBzaXplOiAweFwiK2x0LnRvU3RyaW5nKDE2KStcIiBieXRlc1wiKTtyZXR1cm4gcnwwfWEoV3QsXCJjaGVja2VkXCIpO2Z1bmN0aW9uIE9vKHIpe3JldHVybityIT1cbnImJihyPTApLGguYWxsb2MoK3IpfWEoT28sXCJTbG93QnVmZmVyXCIpO2guaXNCdWZmZXI9YShmdW5jdGlvbihlKXtyZXR1cm4gZSE9bnVsbCYmZS5faXNCdWZmZXI9PT0hMCYmZSE9PVxuaC5wcm90b3R5cGV9LFwiaXNCdWZmZXJcIik7aC5jb21wYXJlPWEoZnVuY3Rpb24oZSx0KXtpZihjZShlLFVpbnQ4QXJyYXkpJiYoZT1oLmZyb20oZSxlLm9mZnNldCxlLmJ5dGVMZW5ndGgpKSxcbmNlKHQsVWludDhBcnJheSkmJih0PWguZnJvbSh0LHQub2Zmc2V0LHQuYnl0ZUxlbmd0aCkpLCFoLmlzQnVmZmVyKGUpfHwhaC5pc0J1ZmZlcih0KSl0aHJvdyBuZXcgVHlwZUVycm9yKFxuJ1RoZSBcImJ1ZjFcIiwgXCJidWYyXCIgYXJndW1lbnRzIG11c3QgYmUgb25lIG9mIHR5cGUgQnVmZmVyIG9yIFVpbnQ4QXJyYXknKTtpZihlPT09dClyZXR1cm4gMDtsZXQgbj1lLmxlbmd0aCxcbmk9dC5sZW5ndGg7Zm9yKGxldCBzPTAsbz1NYXRoLm1pbihuLGkpO3M8bzsrK3MpaWYoZVtzXSE9PXRbc10pe249ZVtzXSxpPXRbc107YnJlYWt9cmV0dXJuIG48aT8tMTppPG4/XG4xOjB9LFwiY29tcGFyZVwiKTtoLmlzRW5jb2Rpbmc9YShmdW5jdGlvbihlKXtzd2l0Y2goU3RyaW5nKGUpLnRvTG93ZXJDYXNlKCkpe2Nhc2VcImhleFwiOmNhc2VcInV0ZjhcIjpjYXNlXCJcXFxudXRmLThcIjpjYXNlXCJhc2NpaVwiOmNhc2VcImxhdGluMVwiOmNhc2VcImJpbmFyeVwiOmNhc2VcImJhc2U2NFwiOmNhc2VcInVjczJcIjpjYXNlXCJ1Y3MtMlwiOmNhc2VcInV0ZjE2bGVcIjpjYXNlXCJcXFxudXRmLTE2bGVcIjpyZXR1cm4hMDtkZWZhdWx0OnJldHVybiExfX0sXCJpc0VuY29kaW5nXCIpO2guY29uY2F0PWEoZnVuY3Rpb24oZSx0KXtpZighQXJyYXkuaXNBcnJheShlKSl0aHJvdyBuZXcgVHlwZUVycm9yKFxuJ1wibGlzdFwiIGFyZ3VtZW50IG11c3QgYmUgYW4gQXJyYXkgb2YgQnVmZmVycycpO2lmKGUubGVuZ3RoPT09MClyZXR1cm4gaC5hbGxvYygwKTtsZXQgbjtpZih0PT09dm9pZCAwKVxuZm9yKHQ9MCxuPTA7bjxlLmxlbmd0aDsrK24pdCs9ZVtuXS5sZW5ndGg7bGV0IGk9aC5hbGxvY1Vuc2FmZSh0KSxzPTA7Zm9yKG49MDtuPGUubGVuZ3RoOysrbil7bGV0IG89ZVtuXTtcbmlmKGNlKG8sVWludDhBcnJheSkpcytvLmxlbmd0aD5pLmxlbmd0aD8oaC5pc0J1ZmZlcihvKXx8KG89aC5mcm9tKG8pKSxvLmNvcHkoaSxzKSk6VWludDhBcnJheS5wcm90b3R5cGUuXG5zZXQuY2FsbChpLG8scyk7ZWxzZSBpZihoLmlzQnVmZmVyKG8pKW8uY29weShpLHMpO2Vsc2UgdGhyb3cgbmV3IFR5cGVFcnJvcignXCJsaXN0XCIgYXJndW1lbnQgbXVzdCBiZSBcXFxuYW4gQXJyYXkgb2YgQnVmZmVycycpO3MrPW8ubGVuZ3RofXJldHVybiBpfSxcImNvbmNhdFwiKTtmdW5jdGlvbiBHbihyLGUpe2lmKGguaXNCdWZmZXIocikpcmV0dXJuIHIubGVuZ3RoO1xuaWYoQXJyYXlCdWZmZXIuaXNWaWV3KHIpfHxjZShyLEFycmF5QnVmZmVyKSlyZXR1cm4gci5ieXRlTGVuZ3RoO2lmKHR5cGVvZiByIT1cInN0cmluZ1wiKXRocm93IG5ldyBUeXBlRXJyb3IoXG4nVGhlIFwic3RyaW5nXCIgYXJndW1lbnQgbXVzdCBiZSBvbmUgb2YgdHlwZSBzdHJpbmcsIEJ1ZmZlciwgb3IgQXJyYXlCdWZmZXIuIFJlY2VpdmVkIHR5cGUgJyt0eXBlb2Ygcik7XG5sZXQgdD1yLmxlbmd0aCxuPWFyZ3VtZW50cy5sZW5ndGg+MiYmYXJndW1lbnRzWzJdPT09ITA7aWYoIW4mJnQ9PT0wKXJldHVybiAwO2xldCBpPSExO2Zvcig7Oylzd2l0Y2goZSl7Y2FzZVwiXFxcbmFzY2lpXCI6Y2FzZVwibGF0aW4xXCI6Y2FzZVwiYmluYXJ5XCI6cmV0dXJuIHQ7Y2FzZVwidXRmOFwiOmNhc2VcInV0Zi04XCI6cmV0dXJuIE50KHIpLmxlbmd0aDtjYXNlXCJ1Y3MyXCI6Y2FzZVwiXFxcbnVjcy0yXCI6Y2FzZVwidXRmMTZsZVwiOmNhc2VcInV0Zi0xNmxlXCI6cmV0dXJuIHQqMjtjYXNlXCJoZXhcIjpyZXR1cm4gdD4+PjE7Y2FzZVwiYmFzZTY0XCI6cmV0dXJuIHRpKHIpLmxlbmd0aDtkZWZhdWx0OlxuaWYoaSlyZXR1cm4gbj8tMTpOdChyKS5sZW5ndGg7ZT0oXCJcIitlKS50b0xvd2VyQ2FzZSgpLGk9ITB9fWEoR24sXCJieXRlTGVuZ3RoXCIpO2guYnl0ZUxlbmd0aD1HbjtmdW5jdGlvbiBxbyhyLGUsdCl7XG5sZXQgbj0hMTtpZigoZT09PXZvaWQgMHx8ZTwwKSYmKGU9MCksZT50aGlzLmxlbmd0aHx8KCh0PT09dm9pZCAwfHx0PnRoaXMubGVuZ3RoKSYmKHQ9dGhpcy5sZW5ndGgpLHQ8PVxuMCl8fCh0Pj4+PTAsZT4+Pj0wLHQ8PWUpKXJldHVyblwiXCI7Zm9yKHJ8fChyPVwidXRmOFwiKTs7KXN3aXRjaChyKXtjYXNlXCJoZXhcIjpyZXR1cm4gS28odGhpcyxlLHQpO2Nhc2VcInVcXFxudGY4XCI6Y2FzZVwidXRmLThcIjpyZXR1cm4gem4odGhpcyxlLHQpO2Nhc2VcImFzY2lpXCI6cmV0dXJuIFZvKHRoaXMsZSx0KTtjYXNlXCJsYXRpbjFcIjpjYXNlXCJiaW5hcnlcIjpyZXR1cm4gem8oXG50aGlzLGUsdCk7Y2FzZVwiYmFzZTY0XCI6cmV0dXJuICRvKHRoaXMsZSx0KTtjYXNlXCJ1Y3MyXCI6Y2FzZVwidWNzLTJcIjpjYXNlXCJ1dGYxNmxlXCI6Y2FzZVwidXRmLTE2bGVcIjpyZXR1cm4gWW8oXG50aGlzLGUsdCk7ZGVmYXVsdDppZihuKXRocm93IG5ldyBUeXBlRXJyb3IoXCJVbmtub3duIGVuY29kaW5nOiBcIityKTtyPShyK1wiXCIpLnRvTG93ZXJDYXNlKCksbj0hMH19YShxbyxcblwic2xvd1RvU3RyaW5nXCIpO2gucHJvdG90eXBlLl9pc0J1ZmZlcj0hMDtmdW5jdGlvbiBFZShyLGUsdCl7bGV0IG49cltlXTtyW2VdPXJbdF0sclt0XT1ufWEoRWUsXCJzd2FwXCIpO1xuaC5wcm90b3R5cGUuc3dhcDE2PWEoZnVuY3Rpb24oKXtsZXQgZT10aGlzLmxlbmd0aDtpZihlJTIhPT0wKXRocm93IG5ldyBSYW5nZUVycm9yKFwiQnVmZmVyIHNpemUgbXVzdCBcXFxuYmUgYSBtdWx0aXBsZSBvZiAxNi1iaXRzXCIpO2ZvcihsZXQgdD0wO3Q8ZTt0Kz0yKUVlKHRoaXMsdCx0KzEpO3JldHVybiB0aGlzfSxcInN3YXAxNlwiKTtoLnByb3RvdHlwZS5zd2FwMzI9XG5hKGZ1bmN0aW9uKCl7bGV0IGU9dGhpcy5sZW5ndGg7aWYoZSU0IT09MCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIkJ1ZmZlciBzaXplIG11c3QgYmUgYSBtdWx0aXBsZSBvZiAzMlxcXG4tYml0c1wiKTtmb3IobGV0IHQ9MDt0PGU7dCs9NClFZSh0aGlzLHQsdCszKSxFZSh0aGlzLHQrMSx0KzIpO3JldHVybiB0aGlzfSxcInN3YXAzMlwiKTtoLnByb3RvdHlwZS5zd2FwNjQ9XG5hKGZ1bmN0aW9uKCl7bGV0IGU9dGhpcy5sZW5ndGg7aWYoZSU4IT09MCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIkJ1ZmZlciBzaXplIG11c3QgYmUgYSBtdWx0aXBsZSBvZiA2NFxcXG4tYml0c1wiKTtmb3IobGV0IHQ9MDt0PGU7dCs9OClFZSh0aGlzLHQsdCs3KSxFZSh0aGlzLHQrMSx0KzYpLEVlKHRoaXMsdCsyLHQrNSksRWUodGhpcyx0KzMsdCs0KTtyZXR1cm4gdGhpc30sXG5cInN3YXA2NFwiKTtoLnByb3RvdHlwZS50b1N0cmluZz1hKGZ1bmN0aW9uKCl7bGV0IGU9dGhpcy5sZW5ndGg7cmV0dXJuIGU9PT0wP1wiXCI6YXJndW1lbnRzLmxlbmd0aD09PTA/em4oXG50aGlzLDAsZSk6cW8uYXBwbHkodGhpcyxhcmd1bWVudHMpfSxcInRvU3RyaW5nXCIpO2gucHJvdG90eXBlLnRvTG9jYWxlU3RyaW5nPWgucHJvdG90eXBlLnRvU3RyaW5nO2gucHJvdG90eXBlLlxuZXF1YWxzPWEoZnVuY3Rpb24oZSl7aWYoIWguaXNCdWZmZXIoZSkpdGhyb3cgbmV3IFR5cGVFcnJvcihcIkFyZ3VtZW50IG11c3QgYmUgYSBCdWZmZXJcIik7cmV0dXJuIHRoaXM9PT1cbmU/ITA6aC5jb21wYXJlKHRoaXMsZSk9PT0wfSxcImVxdWFsc1wiKTtoLnByb3RvdHlwZS5pbnNwZWN0PWEoZnVuY3Rpb24oKXtsZXQgZT1cIlwiLHQ9QmUuSU5TUEVDVF9NQVhfQllURVM7XG5yZXR1cm4gZT10aGlzLnRvU3RyaW5nKFwiaGV4XCIsMCx0KS5yZXBsYWNlKC8oLnsyfSkvZyxcIiQxIFwiKS50cmltKCksdGhpcy5sZW5ndGg+dCYmKGUrPVwiIC4uLiBcIiksXCI8QnVmZlxcXG5lciBcIitlK1wiPlwifSxcImluc3BlY3RcIik7UW4mJihoLnByb3RvdHlwZVtRbl09aC5wcm90b3R5cGUuaW5zcGVjdCk7aC5wcm90b3R5cGUuY29tcGFyZT1hKGZ1bmN0aW9uKGUsdCxuLGkscyl7XG5pZihjZShlLFVpbnQ4QXJyYXkpJiYoZT1oLmZyb20oZSxlLm9mZnNldCxlLmJ5dGVMZW5ndGgpKSwhaC5pc0J1ZmZlcihlKSl0aHJvdyBuZXcgVHlwZUVycm9yKCdUaGUgXCJ0YVxcXG5yZ2V0XCIgYXJndW1lbnQgbXVzdCBiZSBvbmUgb2YgdHlwZSBCdWZmZXIgb3IgVWludDhBcnJheS4gUmVjZWl2ZWQgdHlwZSAnK3R5cGVvZiBlKTtpZih0PT09dm9pZCAwJiYodD1cbjApLG49PT12b2lkIDAmJihuPWU/ZS5sZW5ndGg6MCksaT09PXZvaWQgMCYmKGk9MCkscz09PXZvaWQgMCYmKHM9dGhpcy5sZW5ndGgpLHQ8MHx8bj5lLmxlbmd0aHx8aTwwfHxcbnM+dGhpcy5sZW5ndGgpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJvdXQgb2YgcmFuZ2UgaW5kZXhcIik7aWYoaT49cyYmdD49bilyZXR1cm4gMDtpZihpPj1zKXJldHVybi0xO2lmKHQ+PVxubilyZXR1cm4gMTtpZih0Pj4+PTAsbj4+Pj0wLGk+Pj49MCxzPj4+PTAsdGhpcz09PWUpcmV0dXJuIDA7bGV0IG89cy1pLHU9bi10LGM9TWF0aC5taW4obyx1KSxsPXRoaXMuc2xpY2UoXG5pLHMpLGY9ZS5zbGljZSh0LG4pO2ZvcihsZXQgeT0wO3k8YzsrK3kpaWYobFt5XSE9PWZbeV0pe289bFt5XSx1PWZbeV07YnJlYWt9cmV0dXJuIG88dT8tMTp1PG8/MTowfSxcIlxcXG5jb21wYXJlXCIpO2Z1bmN0aW9uIFZuKHIsZSx0LG4saSl7aWYoci5sZW5ndGg9PT0wKXJldHVybi0xO2lmKHR5cGVvZiB0PT1cInN0cmluZ1wiPyhuPXQsdD0wKTp0PjIxNDc0ODM2NDc/XG50PTIxNDc0ODM2NDc6dDwtMjE0NzQ4MzY0OCYmKHQ9LTIxNDc0ODM2NDgpLHQ9K3QsJHQodCkmJih0PWk/MDpyLmxlbmd0aC0xKSx0PDAmJih0PXIubGVuZ3RoK3QpLHQ+PXIuXG5sZW5ndGgpe2lmKGkpcmV0dXJuLTE7dD1yLmxlbmd0aC0xfWVsc2UgaWYodDwwKWlmKGkpdD0wO2Vsc2UgcmV0dXJuLTE7aWYodHlwZW9mIGU9PVwic3RyaW5nXCImJihlPWguZnJvbShcbmUsbikpLGguaXNCdWZmZXIoZSkpcmV0dXJuIGUubGVuZ3RoPT09MD8tMTpObihyLGUsdCxuLGkpO2lmKHR5cGVvZiBlPT1cIm51bWJlclwiKXJldHVybiBlPWUmMjU1LHR5cGVvZiBVaW50OEFycmF5LlxucHJvdG90eXBlLmluZGV4T2Y9PVwiZnVuY3Rpb25cIj9pP1VpbnQ4QXJyYXkucHJvdG90eXBlLmluZGV4T2YuY2FsbChyLGUsdCk6VWludDhBcnJheS5wcm90b3R5cGUubGFzdEluZGV4T2YuXG5jYWxsKHIsZSx0KTpObihyLFtlXSx0LG4saSk7dGhyb3cgbmV3IFR5cGVFcnJvcihcInZhbCBtdXN0IGJlIHN0cmluZywgbnVtYmVyIG9yIEJ1ZmZlclwiKX1hKFZuLFwiYmlkaXJlXFxcbmN0aW9uYWxJbmRleE9mXCIpO2Z1bmN0aW9uIE5uKHIsZSx0LG4saSl7bGV0IHM9MSxvPXIubGVuZ3RoLHU9ZS5sZW5ndGg7aWYobiE9PXZvaWQgMCYmKG49U3RyaW5nKG4pLnRvTG93ZXJDYXNlKCksXG5uPT09XCJ1Y3MyXCJ8fG49PT1cInVjcy0yXCJ8fG49PT1cInV0ZjE2bGVcInx8bj09PVwidXRmLTE2bGVcIikpe2lmKHIubGVuZ3RoPDJ8fGUubGVuZ3RoPDIpcmV0dXJuLTE7cz0yLG8vPTIsXG51Lz0yLHQvPTJ9ZnVuY3Rpb24gYyhmLHkpe3JldHVybiBzPT09MT9mW3ldOmYucmVhZFVJbnQxNkJFKHkqcyl9YShjLFwicmVhZFwiKTtsZXQgbDtpZihpKXtsZXQgZj0tMTtmb3IobD1cbnQ7bDxvO2wrKylpZihjKHIsbCk9PT1jKGUsZj09PS0xPzA6bC1mKSl7aWYoZj09PS0xJiYoZj1sKSxsLWYrMT09PXUpcmV0dXJuIGYqc31lbHNlIGYhPT0tMSYmKGwtPWwtZiksXG5mPS0xfWVsc2UgZm9yKHQrdT5vJiYodD1vLXUpLGw9dDtsPj0wO2wtLSl7bGV0IGY9ITA7Zm9yKGxldCB5PTA7eTx1O3krKylpZihjKHIsbCt5KSE9PWMoZSx5KSl7Zj0hMTticmVha31cbmlmKGYpcmV0dXJuIGx9cmV0dXJuLTF9YShObixcImFycmF5SW5kZXhPZlwiKTtoLnByb3RvdHlwZS5pbmNsdWRlcz1hKGZ1bmN0aW9uKGUsdCxuKXtyZXR1cm4gdGhpcy5pbmRleE9mKFxuZSx0LG4pIT09LTF9LFwiaW5jbHVkZXNcIik7aC5wcm90b3R5cGUuaW5kZXhPZj1hKGZ1bmN0aW9uKGUsdCxuKXtyZXR1cm4gVm4odGhpcyxlLHQsbiwhMCl9LFwiaW5kZXhPZlwiKTtcbmgucHJvdG90eXBlLmxhc3RJbmRleE9mPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBWbih0aGlzLGUsdCxuLCExKX0sXCJsYXN0SW5kZXhPZlwiKTtmdW5jdGlvbiBRbyhyLGUsdCxuKXtcbnQ9TnVtYmVyKHQpfHwwO2xldCBpPXIubGVuZ3RoLXQ7bj8obj1OdW1iZXIobiksbj5pJiYobj1pKSk6bj1pO2xldCBzPWUubGVuZ3RoO24+cy8yJiYobj1zLzIpO2xldCBvO2ZvcihvPVxuMDtvPG47KytvKXtsZXQgdT1wYXJzZUludChlLnN1YnN0cihvKjIsMiksMTYpO2lmKCR0KHUpKXJldHVybiBvO3JbdCtvXT11fXJldHVybiBvfWEoUW8sXCJoZXhXcml0ZVwiKTtmdW5jdGlvbiBObyhyLGUsdCxuKXtcbnJldHVybiBmdChOdChlLHIubGVuZ3RoLXQpLHIsdCxuKX1hKE5vLFwidXRmOFdyaXRlXCIpO2Z1bmN0aW9uIGpvKHIsZSx0LG4pe3JldHVybiBmdChlYShlKSxyLHQsbil9YShqbyxcblwiYXNjaWlXcml0ZVwiKTtmdW5jdGlvbiBXbyhyLGUsdCxuKXtyZXR1cm4gZnQodGkoZSkscix0LG4pfWEoV28sXCJiYXNlNjRXcml0ZVwiKTtmdW5jdGlvbiBIbyhyLGUsdCxuKXtyZXR1cm4gZnQoXG50YShlLHIubGVuZ3RoLXQpLHIsdCxuKX1hKEhvLFwidWNzMldyaXRlXCIpO2gucHJvdG90eXBlLndyaXRlPWEoZnVuY3Rpb24oZSx0LG4saSl7aWYodD09PXZvaWQgMClpPVwidXRmXFxcbjhcIixuPXRoaXMubGVuZ3RoLHQ9MDtlbHNlIGlmKG49PT12b2lkIDAmJnR5cGVvZiB0PT1cInN0cmluZ1wiKWk9dCxuPXRoaXMubGVuZ3RoLHQ9MDtlbHNlIGlmKGlzRmluaXRlKHQpKVxudD10Pj4+MCxpc0Zpbml0ZShuKT8obj1uPj4+MCxpPT09dm9pZCAwJiYoaT1cInV0ZjhcIikpOihpPW4sbj12b2lkIDApO2Vsc2UgdGhyb3cgbmV3IEVycm9yKFwiQnVmZmVyLndyaVxcXG50ZShzdHJpbmcsIGVuY29kaW5nLCBvZmZzZXRbLCBsZW5ndGhdKSBpcyBubyBsb25nZXIgc3VwcG9ydGVkXCIpO2xldCBzPXRoaXMubGVuZ3RoLXQ7aWYoKG49PT12b2lkIDB8fFxubj5zKSYmKG49cyksZS5sZW5ndGg+MCYmKG48MHx8dDwwKXx8dD50aGlzLmxlbmd0aCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIkF0dGVtcHQgdG8gd3JpdGUgb3V0c2lkZSBidWZcXFxuZmVyIGJvdW5kc1wiKTtpfHwoaT1cInV0ZjhcIik7bGV0IG89ITE7Zm9yKDs7KXN3aXRjaChpKXtjYXNlXCJoZXhcIjpyZXR1cm4gUW8odGhpcyxlLHQsbik7Y2FzZVwidXRmOFwiOmNhc2VcIlxcXG51dGYtOFwiOnJldHVybiBObyh0aGlzLGUsdCxuKTtjYXNlXCJhc2NpaVwiOmNhc2VcImxhdGluMVwiOmNhc2VcImJpbmFyeVwiOnJldHVybiBqbyh0aGlzLGUsdCxuKTtjYXNlXCJiYXNlNjRcIjpcbnJldHVybiBXbyh0aGlzLGUsdCxuKTtjYXNlXCJ1Y3MyXCI6Y2FzZVwidWNzLTJcIjpjYXNlXCJ1dGYxNmxlXCI6Y2FzZVwidXRmLTE2bGVcIjpyZXR1cm4gSG8odGhpcyxlLHQsbik7ZGVmYXVsdDpcbmlmKG8pdGhyb3cgbmV3IFR5cGVFcnJvcihcIlVua25vd24gZW5jb2Rpbmc6IFwiK2kpO2k9KFwiXCIraSkudG9Mb3dlckNhc2UoKSxvPSEwfX0sXCJ3cml0ZVwiKTtoLnByb3RvdHlwZS5cbnRvSlNPTj1hKGZ1bmN0aW9uKCl7cmV0dXJue3R5cGU6XCJCdWZmZXJcIixkYXRhOkFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKHRoaXMuX2Fycnx8dGhpcywwKX19LFwidG9KU09cXFxuTlwiKTtmdW5jdGlvbiAkbyhyLGUsdCl7cmV0dXJuIGU9PT0wJiZ0PT09ci5sZW5ndGg/T3QuZnJvbUJ5dGVBcnJheShyKTpPdC5mcm9tQnl0ZUFycmF5KHIuc2xpY2UoZSx0KSl9XG5hKCRvLFwiYmFzZTY0U2xpY2VcIik7ZnVuY3Rpb24gem4ocixlLHQpe3Q9TWF0aC5taW4oci5sZW5ndGgsdCk7bGV0IG49W10saT1lO2Zvcig7aTx0Oyl7bGV0IHM9cltpXSxvPW51bGwsXG51PXM+MjM5PzQ6cz4yMjM/MzpzPjE5MT8yOjE7aWYoaSt1PD10KXtsZXQgYyxsLGYseTtzd2l0Y2godSl7Y2FzZSAxOnM8MTI4JiYobz1zKTticmVhaztjYXNlIDI6Yz1yW2krXG4xXSwoYyYxOTIpPT09MTI4JiYoeT0ocyYzMSk8PDZ8YyY2Myx5PjEyNyYmKG89eSkpO2JyZWFrO2Nhc2UgMzpjPXJbaSsxXSxsPXJbaSsyXSwoYyYxOTIpPT09MTI4JiYobCYxOTIpPT09XG4xMjgmJih5PShzJjE1KTw8MTJ8KGMmNjMpPDw2fGwmNjMseT4yMDQ3JiYoeTw1NTI5Nnx8eT41NzM0MykmJihvPXkpKTticmVhaztjYXNlIDQ6Yz1yW2krMV0sbD1yW2krMl0sXG5mPXJbaSszXSwoYyYxOTIpPT09MTI4JiYobCYxOTIpPT09MTI4JiYoZiYxOTIpPT09MTI4JiYoeT0ocyYxNSk8PDE4fChjJjYzKTw8MTJ8KGwmNjMpPDw2fGYmNjMseT42NTUzNSYmXG55PDExMTQxMTImJihvPXkpKX19bz09PW51bGw/KG89NjU1MzMsdT0xKTpvPjY1NTM1JiYoby09NjU1MzYsbi5wdXNoKG8+Pj4xMCYxMDIzfDU1Mjk2KSxvPTU2MzIwfG8mMTAyMyksXG5uLnB1c2gobyksaSs9dX1yZXR1cm4gR28obil9YSh6bixcInV0ZjhTbGljZVwiKTt2YXIgam49NDA5NjtmdW5jdGlvbiBHbyhyKXtsZXQgZT1yLmxlbmd0aDtpZihlPD1qbilyZXR1cm4gU3RyaW5nLlxuZnJvbUNoYXJDb2RlLmFwcGx5KFN0cmluZyxyKTtsZXQgdD1cIlwiLG49MDtmb3IoO248ZTspdCs9U3RyaW5nLmZyb21DaGFyQ29kZS5hcHBseShTdHJpbmcsci5zbGljZShuLG4rPVxuam4pKTtyZXR1cm4gdH1hKEdvLFwiZGVjb2RlQ29kZVBvaW50c0FycmF5XCIpO2Z1bmN0aW9uIFZvKHIsZSx0KXtsZXQgbj1cIlwiO3Q9TWF0aC5taW4oci5sZW5ndGgsdCk7Zm9yKGxldCBpPWU7aTxcbnQ7KytpKW4rPVN0cmluZy5mcm9tQ2hhckNvZGUocltpXSYxMjcpO3JldHVybiBufWEoVm8sXCJhc2NpaVNsaWNlXCIpO2Z1bmN0aW9uIHpvKHIsZSx0KXtsZXQgbj1cIlwiO3Q9TWF0aC5cbm1pbihyLmxlbmd0aCx0KTtmb3IobGV0IGk9ZTtpPHQ7KytpKW4rPVN0cmluZy5mcm9tQ2hhckNvZGUocltpXSk7cmV0dXJuIG59YSh6byxcImxhdGluMVNsaWNlXCIpO2Z1bmN0aW9uIEtvKHIsZSx0KXtcbmxldCBuPXIubGVuZ3RoOyghZXx8ZTwwKSYmKGU9MCksKCF0fHx0PDB8fHQ+bikmJih0PW4pO2xldCBpPVwiXCI7Zm9yKGxldCBzPWU7czx0OysrcylpKz1yYVtyW3NdXTtyZXR1cm4gaX1cbmEoS28sXCJoZXhTbGljZVwiKTtmdW5jdGlvbiBZbyhyLGUsdCl7bGV0IG49ci5zbGljZShlLHQpLGk9XCJcIjtmb3IobGV0IHM9MDtzPG4ubGVuZ3RoLTE7cys9MilpKz1TdHJpbmcuXG5mcm9tQ2hhckNvZGUobltzXStuW3MrMV0qMjU2KTtyZXR1cm4gaX1hKFlvLFwidXRmMTZsZVNsaWNlXCIpO2gucHJvdG90eXBlLnNsaWNlPWEoZnVuY3Rpb24oZSx0KXtsZXQgbj10aGlzLlxubGVuZ3RoO2U9fn5lLHQ9dD09PXZvaWQgMD9uOn5+dCxlPDA/KGUrPW4sZTwwJiYoZT0wKSk6ZT5uJiYoZT1uKSx0PDA/KHQrPW4sdDwwJiYodD0wKSk6dD5uJiYodD1uKSx0PFxuZSYmKHQ9ZSk7bGV0IGk9dGhpcy5zdWJhcnJheShlLHQpO3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2YoaSxoLnByb3RvdHlwZSksaX0sXCJzbGljZVwiKTtmdW5jdGlvbiBPKHIsZSx0KXtcbmlmKHIlMSE9PTB8fHI8MCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIm9mZnNldCBpcyBub3QgdWludFwiKTtpZihyK2U+dCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIlRyeWluZyB0byBcXFxuYWNjZXNzIGJleW9uZCBidWZmZXIgbGVuZ3RoXCIpfWEoTyxcImNoZWNrT2Zmc2V0XCIpO2gucHJvdG90eXBlLnJlYWRVaW50TEU9aC5wcm90b3R5cGUucmVhZFVJbnRMRT1hKGZ1bmN0aW9uKGUsdCxuKXtcbmU9ZT4+PjAsdD10Pj4+MCxufHxPKGUsdCx0aGlzLmxlbmd0aCk7bGV0IGk9dGhpc1tlXSxzPTEsbz0wO2Zvcig7KytvPHQmJihzKj0yNTYpOylpKz10aGlzW2Urb10qcztyZXR1cm4gaX0sXG5cInJlYWRVSW50TEVcIik7aC5wcm90b3R5cGUucmVhZFVpbnRCRT1oLnByb3RvdHlwZS5yZWFkVUludEJFPWEoZnVuY3Rpb24oZSx0LG4pe2U9ZT4+PjAsdD10Pj4+MCxufHxPKGUsXG50LHRoaXMubGVuZ3RoKTtsZXQgaT10aGlzW2UrLS10XSxzPTE7Zm9yKDt0PjAmJihzKj0yNTYpOylpKz10aGlzW2UrLS10XSpzO3JldHVybiBpfSxcInJlYWRVSW50QkVcIik7aC5cbnByb3RvdHlwZS5yZWFkVWludDg9aC5wcm90b3R5cGUucmVhZFVJbnQ4PWEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZT1lPj4+MCx0fHxPKGUsMSx0aGlzLmxlbmd0aCksdGhpc1tlXX0sXG5cInJlYWRVSW50OFwiKTtoLnByb3RvdHlwZS5yZWFkVWludDE2TEU9aC5wcm90b3R5cGUucmVhZFVJbnQxNkxFPWEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZT1lPj4+MCx0fHxPKGUsXG4yLHRoaXMubGVuZ3RoKSx0aGlzW2VdfHRoaXNbZSsxXTw8OH0sXCJyZWFkVUludDE2TEVcIik7aC5wcm90b3R5cGUucmVhZFVpbnQxNkJFPWgucHJvdG90eXBlLnJlYWRVSW50MTZCRT1cbmEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZT1lPj4+MCx0fHxPKGUsMix0aGlzLmxlbmd0aCksdGhpc1tlXTw8OHx0aGlzW2UrMV19LFwicmVhZFVJbnQxNkJFXCIpO2gucHJvdG90eXBlLlxucmVhZFVpbnQzMkxFPWgucHJvdG90eXBlLnJlYWRVSW50MzJMRT1hKGZ1bmN0aW9uKGUsdCl7cmV0dXJuIGU9ZT4+PjAsdHx8TyhlLDQsdGhpcy5sZW5ndGgpLCh0aGlzW2VdfFxudGhpc1tlKzFdPDw4fHRoaXNbZSsyXTw8MTYpK3RoaXNbZSszXSoxNjc3NzIxNn0sXCJyZWFkVUludDMyTEVcIik7aC5wcm90b3R5cGUucmVhZFVpbnQzMkJFPWgucHJvdG90eXBlLlxucmVhZFVJbnQzMkJFPWEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZT1lPj4+MCx0fHxPKGUsNCx0aGlzLmxlbmd0aCksdGhpc1tlXSoxNjc3NzIxNisodGhpc1tlKzFdPDwxNnx0aGlzW2UrXG4yXTw8OHx0aGlzW2UrM10pfSxcInJlYWRVSW50MzJCRVwiKTtoLnByb3RvdHlwZS5yZWFkQmlnVUludDY0TEU9eWUoYShmdW5jdGlvbihlKXtlPWU+Pj4wLFJlKGUsXCJvZmZzZXRcIik7XG5sZXQgdD10aGlzW2VdLG49dGhpc1tlKzddOyh0PT09dm9pZCAwfHxuPT09dm9pZCAwKSYmamUoZSx0aGlzLmxlbmd0aC04KTtsZXQgaT10K3RoaXNbKytlXSoyKio4K3RoaXNbKytlXSpcbjIqKjE2K3RoaXNbKytlXSoyKioyNCxzPXRoaXNbKytlXSt0aGlzWysrZV0qMioqOCt0aGlzWysrZV0qMioqMTYrbioyKioyNDtyZXR1cm4gQmlnSW50KGkpKyhCaWdJbnQocyk8PFxuQmlnSW50KDMyKSl9LFwicmVhZEJpZ1VJbnQ2NExFXCIpKTtoLnByb3RvdHlwZS5yZWFkQmlnVUludDY0QkU9eWUoYShmdW5jdGlvbihlKXtlPWU+Pj4wLFJlKGUsXCJvZmZzZXRcIik7XG5sZXQgdD10aGlzW2VdLG49dGhpc1tlKzddOyh0PT09dm9pZCAwfHxuPT09dm9pZCAwKSYmamUoZSx0aGlzLmxlbmd0aC04KTtsZXQgaT10KjIqKjI0K3RoaXNbKytlXSoyKioxNitcbnRoaXNbKytlXSoyKio4K3RoaXNbKytlXSxzPXRoaXNbKytlXSoyKioyNCt0aGlzWysrZV0qMioqMTYrdGhpc1srK2VdKjIqKjgrbjtyZXR1cm4oQmlnSW50KGkpPDxCaWdJbnQoXG4zMikpK0JpZ0ludChzKX0sXCJyZWFkQmlnVUludDY0QkVcIikpO2gucHJvdG90eXBlLnJlYWRJbnRMRT1hKGZ1bmN0aW9uKGUsdCxuKXtlPWU+Pj4wLHQ9dD4+PjAsbnx8TyhlLHQsXG50aGlzLmxlbmd0aCk7bGV0IGk9dGhpc1tlXSxzPTEsbz0wO2Zvcig7KytvPHQmJihzKj0yNTYpOylpKz10aGlzW2Urb10qcztyZXR1cm4gcyo9MTI4LGk+PXMmJihpLT1NYXRoLlxucG93KDIsOCp0KSksaX0sXCJyZWFkSW50TEVcIik7aC5wcm90b3R5cGUucmVhZEludEJFPWEoZnVuY3Rpb24oZSx0LG4pe2U9ZT4+PjAsdD10Pj4+MCxufHxPKGUsdCx0aGlzLmxlbmd0aCk7XG5sZXQgaT10LHM9MSxvPXRoaXNbZSstLWldO2Zvcig7aT4wJiYocyo9MjU2KTspbys9dGhpc1tlKy0taV0qcztyZXR1cm4gcyo9MTI4LG8+PXMmJihvLT1NYXRoLnBvdygyLDgqXG50KSksb30sXCJyZWFkSW50QkVcIik7aC5wcm90b3R5cGUucmVhZEludDg9YShmdW5jdGlvbihlLHQpe3JldHVybiBlPWU+Pj4wLHR8fE8oZSwxLHRoaXMubGVuZ3RoKSx0aGlzW2VdJlxuMTI4PygyNTUtdGhpc1tlXSsxKSotMTp0aGlzW2VdfSxcInJlYWRJbnQ4XCIpO2gucHJvdG90eXBlLnJlYWRJbnQxNkxFPWEoZnVuY3Rpb24oZSx0KXtlPWU+Pj4wLHR8fE8oZSwyLFxudGhpcy5sZW5ndGgpO2xldCBuPXRoaXNbZV18dGhpc1tlKzFdPDw4O3JldHVybiBuJjMyNzY4P258NDI5NDkwMTc2MDpufSxcInJlYWRJbnQxNkxFXCIpO2gucHJvdG90eXBlLnJlYWRJbnQxNkJFPVxuYShmdW5jdGlvbihlLHQpe2U9ZT4+PjAsdHx8TyhlLDIsdGhpcy5sZW5ndGgpO2xldCBuPXRoaXNbZSsxXXx0aGlzW2VdPDw4O3JldHVybiBuJjMyNzY4P258NDI5NDkwMTc2MDpcbm59LFwicmVhZEludDE2QkVcIik7aC5wcm90b3R5cGUucmVhZEludDMyTEU9YShmdW5jdGlvbihlLHQpe3JldHVybiBlPWU+Pj4wLHR8fE8oZSw0LHRoaXMubGVuZ3RoKSx0aGlzW2VdfFxudGhpc1tlKzFdPDw4fHRoaXNbZSsyXTw8MTZ8dGhpc1tlKzNdPDwyNH0sXCJyZWFkSW50MzJMRVwiKTtoLnByb3RvdHlwZS5yZWFkSW50MzJCRT1hKGZ1bmN0aW9uKGUsdCl7cmV0dXJuIGU9XG5lPj4+MCx0fHxPKGUsNCx0aGlzLmxlbmd0aCksdGhpc1tlXTw8MjR8dGhpc1tlKzFdPDwxNnx0aGlzW2UrMl08PDh8dGhpc1tlKzNdfSxcInJlYWRJbnQzMkJFXCIpO2gucHJvdG90eXBlLlxucmVhZEJpZ0ludDY0TEU9eWUoYShmdW5jdGlvbihlKXtlPWU+Pj4wLFJlKGUsXCJvZmZzZXRcIik7bGV0IHQ9dGhpc1tlXSxuPXRoaXNbZSs3XTsodD09PXZvaWQgMHx8bj09PXZvaWQgMCkmJlxuamUoZSx0aGlzLmxlbmd0aC04KTtsZXQgaT10aGlzW2UrNF0rdGhpc1tlKzVdKjIqKjgrdGhpc1tlKzZdKjIqKjE2KyhuPDwyNCk7cmV0dXJuKEJpZ0ludChpKTw8QmlnSW50KFxuMzIpKStCaWdJbnQodCt0aGlzWysrZV0qMioqOCt0aGlzWysrZV0qMioqMTYrdGhpc1srK2VdKjIqKjI0KX0sXCJyZWFkQmlnSW50NjRMRVwiKSk7aC5wcm90b3R5cGUucmVhZEJpZ0ludDY0QkU9XG55ZShhKGZ1bmN0aW9uKGUpe2U9ZT4+PjAsUmUoZSxcIm9mZnNldFwiKTtsZXQgdD10aGlzW2VdLG49dGhpc1tlKzddOyh0PT09dm9pZCAwfHxuPT09dm9pZCAwKSYmamUoZSx0aGlzLlxubGVuZ3RoLTgpO2xldCBpPSh0PDwyNCkrdGhpc1srK2VdKjIqKjE2K3RoaXNbKytlXSoyKio4K3RoaXNbKytlXTtyZXR1cm4oQmlnSW50KGkpPDxCaWdJbnQoMzIpKStCaWdJbnQoXG50aGlzWysrZV0qMioqMjQrdGhpc1srK2VdKjIqKjE2K3RoaXNbKytlXSoyKio4K24pfSxcInJlYWRCaWdJbnQ2NEJFXCIpKTtoLnByb3RvdHlwZS5yZWFkRmxvYXRMRT1hKGZ1bmN0aW9uKGUsdCl7XG5yZXR1cm4gZT1lPj4+MCx0fHxPKGUsNCx0aGlzLmxlbmd0aCksUGUucmVhZCh0aGlzLGUsITAsMjMsNCl9LFwicmVhZEZsb2F0TEVcIik7aC5wcm90b3R5cGUucmVhZEZsb2F0QkU9XG5hKGZ1bmN0aW9uKGUsdCl7cmV0dXJuIGU9ZT4+PjAsdHx8TyhlLDQsdGhpcy5sZW5ndGgpLFBlLnJlYWQodGhpcyxlLCExLDIzLDQpfSxcInJlYWRGbG9hdEJFXCIpO2gucHJvdG90eXBlLlxucmVhZERvdWJsZUxFPWEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZT1lPj4+MCx0fHxPKGUsOCx0aGlzLmxlbmd0aCksUGUucmVhZCh0aGlzLGUsITAsNTIsOCl9LFwicmVhZERvdWJcXFxubGVMRVwiKTtoLnByb3RvdHlwZS5yZWFkRG91YmxlQkU9YShmdW5jdGlvbihlLHQpe3JldHVybiBlPWU+Pj4wLHR8fE8oZSw4LHRoaXMubGVuZ3RoKSxQZS5yZWFkKHRoaXMsZSxcbiExLDUyLDgpfSxcInJlYWREb3VibGVCRVwiKTtmdW5jdGlvbiBLKHIsZSx0LG4saSxzKXtpZighaC5pc0J1ZmZlcihyKSl0aHJvdyBuZXcgVHlwZUVycm9yKCdcImJ1ZmZlclwiIGFyXFxcbmd1bWVudCBtdXN0IGJlIGEgQnVmZmVyIGluc3RhbmNlJyk7aWYoZT5pfHxlPHMpdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ1widmFsdWVcIiBhcmd1bWVudCBpcyBvdXQgb2YgYm91blxcXG5kcycpO2lmKHQrbj5yLmxlbmd0aCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIkluZGV4IG91dCBvZiByYW5nZVwiKX1hKEssXCJjaGVja0ludFwiKTtoLnByb3RvdHlwZS53cml0ZVVpbnRMRT1cbmgucHJvdG90eXBlLndyaXRlVUludExFPWEoZnVuY3Rpb24oZSx0LG4saSl7aWYoZT0rZSx0PXQ+Pj4wLG49bj4+PjAsIWkpe2xldCB1PU1hdGgucG93KDIsOCpuKS0xO0sodGhpcyxcbmUsdCxuLHUsMCl9bGV0IHM9MSxvPTA7Zm9yKHRoaXNbdF09ZSYyNTU7KytvPG4mJihzKj0yNTYpOyl0aGlzW3Qrb109ZS9zJjI1NTtyZXR1cm4gdCtufSxcIndyaXRlVUludExFXCIpO1xuaC5wcm90b3R5cGUud3JpdGVVaW50QkU9aC5wcm90b3R5cGUud3JpdGVVSW50QkU9YShmdW5jdGlvbihlLHQsbixpKXtpZihlPStlLHQ9dD4+PjAsbj1uPj4+MCwhaSl7bGV0IHU9TWF0aC5cbnBvdygyLDgqbiktMTtLKHRoaXMsZSx0LG4sdSwwKX1sZXQgcz1uLTEsbz0xO2Zvcih0aGlzW3Qrc109ZSYyNTU7LS1zPj0wJiYobyo9MjU2KTspdGhpc1t0K3NdPWUvbyYyNTU7XG5yZXR1cm4gdCtufSxcIndyaXRlVUludEJFXCIpO2gucHJvdG90eXBlLndyaXRlVWludDg9aC5wcm90b3R5cGUud3JpdGVVSW50OD1hKGZ1bmN0aW9uKGUsdCxuKXtyZXR1cm4gZT1cbitlLHQ9dD4+PjAsbnx8Syh0aGlzLGUsdCwxLDI1NSwwKSx0aGlzW3RdPWUmMjU1LHQrMX0sXCJ3cml0ZVVJbnQ4XCIpO2gucHJvdG90eXBlLndyaXRlVWludDE2TEU9aC5wcm90b3R5cGUuXG53cml0ZVVJbnQxNkxFPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBlPStlLHQ9dD4+PjAsbnx8Syh0aGlzLGUsdCwyLDY1NTM1LDApLHRoaXNbdF09ZSYyNTUsdGhpc1t0KzFdPVxuZT4+PjgsdCsyfSxcIndyaXRlVUludDE2TEVcIik7aC5wcm90b3R5cGUud3JpdGVVaW50MTZCRT1oLnByb3RvdHlwZS53cml0ZVVJbnQxNkJFPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBlPVxuK2UsdD10Pj4+MCxufHxLKHRoaXMsZSx0LDIsNjU1MzUsMCksdGhpc1t0XT1lPj4+OCx0aGlzW3QrMV09ZSYyNTUsdCsyfSxcIndyaXRlVUludDE2QkVcIik7aC5wcm90b3R5cGUuXG53cml0ZVVpbnQzMkxFPWgucHJvdG90eXBlLndyaXRlVUludDMyTEU9YShmdW5jdGlvbihlLHQsbil7cmV0dXJuIGU9K2UsdD10Pj4+MCxufHxLKHRoaXMsZSx0LDQsNDI5NDk2NzI5NSxcbjApLHRoaXNbdCszXT1lPj4+MjQsdGhpc1t0KzJdPWU+Pj4xNix0aGlzW3QrMV09ZT4+PjgsdGhpc1t0XT1lJjI1NSx0KzR9LFwid3JpdGVVSW50MzJMRVwiKTtoLnByb3RvdHlwZS5cbndyaXRlVWludDMyQkU9aC5wcm90b3R5cGUud3JpdGVVSW50MzJCRT1hKGZ1bmN0aW9uKGUsdCxuKXtyZXR1cm4gZT0rZSx0PXQ+Pj4wLG58fEsodGhpcyxlLHQsNCw0Mjk0OTY3Mjk1LFxuMCksdGhpc1t0XT1lPj4+MjQsdGhpc1t0KzFdPWU+Pj4xNix0aGlzW3QrMl09ZT4+PjgsdGhpc1t0KzNdPWUmMjU1LHQrNH0sXCJ3cml0ZVVJbnQzMkJFXCIpO2Z1bmN0aW9uIEtuKHIsZSx0LG4saSl7XG5laShlLG4saSxyLHQsNyk7bGV0IHM9TnVtYmVyKGUmQmlnSW50KDQyOTQ5NjcyOTUpKTtyW3QrK109cyxzPXM+Pjgsclt0KytdPXMscz1zPj44LHJbdCsrXT1zLHM9cz4+OCxyW3QrK109XG5zO2xldCBvPU51bWJlcihlPj5CaWdJbnQoMzIpJkJpZ0ludCg0Mjk0OTY3Mjk1KSk7cmV0dXJuIHJbdCsrXT1vLG89bz4+OCxyW3QrK109byxvPW8+Pjgsclt0KytdPW8sbz1vPj5cbjgsclt0KytdPW8sdH1hKEtuLFwid3J0QmlnVUludDY0TEVcIik7ZnVuY3Rpb24gWW4ocixlLHQsbixpKXtlaShlLG4saSxyLHQsNyk7bGV0IHM9TnVtYmVyKGUmQmlnSW50KDQyOTQ5NjcyOTUpKTtcbnJbdCs3XT1zLHM9cz4+OCxyW3QrNl09cyxzPXM+Pjgsclt0KzVdPXMscz1zPj44LHJbdCs0XT1zO2xldCBvPU51bWJlcihlPj5CaWdJbnQoMzIpJkJpZ0ludCg0Mjk0OTY3Mjk1KSk7XG5yZXR1cm4gclt0KzNdPW8sbz1vPj44LHJbdCsyXT1vLG89bz4+OCxyW3QrMV09byxvPW8+Pjgsclt0XT1vLHQrOH1hKFluLFwid3J0QmlnVUludDY0QkVcIik7aC5wcm90b3R5cGUuXG53cml0ZUJpZ1VJbnQ2NExFPXllKGEoZnVuY3Rpb24oZSx0PTApe3JldHVybiBLbih0aGlzLGUsdCxCaWdJbnQoMCksQmlnSW50KFwiMHhmZmZmZmZmZmZmZmZmZmZmXCIpKX0sXCJ3XFxcbnJpdGVCaWdVSW50NjRMRVwiKSk7aC5wcm90b3R5cGUud3JpdGVCaWdVSW50NjRCRT15ZShhKGZ1bmN0aW9uKGUsdD0wKXtyZXR1cm4gWW4odGhpcyxlLHQsQmlnSW50KDApLEJpZ0ludChcblwiMHhmZmZmZmZmZmZmZmZmZmZmXCIpKX0sXCJ3cml0ZUJpZ1VJbnQ2NEJFXCIpKTtoLnByb3RvdHlwZS53cml0ZUludExFPWEoZnVuY3Rpb24oZSx0LG4saSl7aWYoZT0rZSx0PXQ+Pj5cbjAsIWkpe2xldCBjPU1hdGgucG93KDIsOCpuLTEpO0sodGhpcyxlLHQsbixjLTEsLWMpfWxldCBzPTAsbz0xLHU9MDtmb3IodGhpc1t0XT1lJjI1NTsrK3M8biYmKG8qPTI1Nik7KVxuZTwwJiZ1PT09MCYmdGhpc1t0K3MtMV0hPT0wJiYodT0xKSx0aGlzW3Qrc109KGUvbz4+MCktdSYyNTU7cmV0dXJuIHQrbn0sXCJ3cml0ZUludExFXCIpO2gucHJvdG90eXBlLndyaXRlSW50QkU9XG5hKGZ1bmN0aW9uKGUsdCxuLGkpe2lmKGU9K2UsdD10Pj4+MCwhaSl7bGV0IGM9TWF0aC5wb3coMiw4Km4tMSk7Syh0aGlzLGUsdCxuLGMtMSwtYyl9bGV0IHM9bi0xLG89MSx1PTA7XG5mb3IodGhpc1t0K3NdPWUmMjU1Oy0tcz49MCYmKG8qPTI1Nik7KWU8MCYmdT09PTAmJnRoaXNbdCtzKzFdIT09MCYmKHU9MSksdGhpc1t0K3NdPShlL28+PjApLXUmMjU1O3JldHVybiB0K1xubn0sXCJ3cml0ZUludEJFXCIpO2gucHJvdG90eXBlLndyaXRlSW50OD1hKGZ1bmN0aW9uKGUsdCxuKXtyZXR1cm4gZT0rZSx0PXQ+Pj4wLG58fEsodGhpcyxlLHQsMSwxMjcsLTEyOCksXG5lPDAmJihlPTI1NStlKzEpLHRoaXNbdF09ZSYyNTUsdCsxfSxcIndyaXRlSW50OFwiKTtoLnByb3RvdHlwZS53cml0ZUludDE2TEU9YShmdW5jdGlvbihlLHQsbil7cmV0dXJuIGU9XG4rZSx0PXQ+Pj4wLG58fEsodGhpcyxlLHQsMiwzMjc2NywtMzI3NjgpLHRoaXNbdF09ZSYyNTUsdGhpc1t0KzFdPWU+Pj44LHQrMn0sXCJ3cml0ZUludDE2TEVcIik7aC5wcm90b3R5cGUuXG53cml0ZUludDE2QkU9YShmdW5jdGlvbihlLHQsbil7cmV0dXJuIGU9K2UsdD10Pj4+MCxufHxLKHRoaXMsZSx0LDIsMzI3NjcsLTMyNzY4KSx0aGlzW3RdPWU+Pj44LHRoaXNbdCtcbjFdPWUmMjU1LHQrMn0sXCJ3cml0ZUludDE2QkVcIik7aC5wcm90b3R5cGUud3JpdGVJbnQzMkxFPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBlPStlLHQ9dD4+PjAsbnx8Syh0aGlzLFxuZSx0LDQsMjE0NzQ4MzY0NywtMjE0NzQ4MzY0OCksdGhpc1t0XT1lJjI1NSx0aGlzW3QrMV09ZT4+PjgsdGhpc1t0KzJdPWU+Pj4xNix0aGlzW3QrM109ZT4+PjI0LHQrNH0sXCJcXFxud3JpdGVJbnQzMkxFXCIpO2gucHJvdG90eXBlLndyaXRlSW50MzJCRT1hKGZ1bmN0aW9uKGUsdCxuKXtyZXR1cm4gZT0rZSx0PXQ+Pj4wLG58fEsodGhpcyxlLHQsNCwyMTQ3NDgzNjQ3LFxuLTIxNDc0ODM2NDgpLGU8MCYmKGU9NDI5NDk2NzI5NStlKzEpLHRoaXNbdF09ZT4+PjI0LHRoaXNbdCsxXT1lPj4+MTYsdGhpc1t0KzJdPWU+Pj44LHRoaXNbdCszXT1lJjI1NSxcbnQrNH0sXCJ3cml0ZUludDMyQkVcIik7aC5wcm90b3R5cGUud3JpdGVCaWdJbnQ2NExFPXllKGEoZnVuY3Rpb24oZSx0PTApe3JldHVybiBLbih0aGlzLGUsdCwtQmlnSW50KFwiMHhcXFxuODAwMDAwMDAwMDAwMDAwMFwiKSxCaWdJbnQoXCIweDdmZmZmZmZmZmZmZmZmZmZcIikpfSxcIndyaXRlQmlnSW50NjRMRVwiKSk7aC5wcm90b3R5cGUud3JpdGVCaWdJbnQ2NEJFPXllKFxuYShmdW5jdGlvbihlLHQ9MCl7cmV0dXJuIFluKHRoaXMsZSx0LC1CaWdJbnQoXCIweDgwMDAwMDAwMDAwMDAwMDBcIiksQmlnSW50KFwiMHg3ZmZmZmZmZmZmZmZmZmZmXCIpKX0sXCJ3XFxcbnJpdGVCaWdJbnQ2NEJFXCIpKTtmdW5jdGlvbiBabihyLGUsdCxuLGkscyl7aWYodCtuPnIubGVuZ3RoKXRocm93IG5ldyBSYW5nZUVycm9yKFwiSW5kZXggb3V0IG9mIHJhbmdlXCIpO1xuaWYodDwwKXRocm93IG5ldyBSYW5nZUVycm9yKFwiSW5kZXggb3V0IG9mIHJhbmdlXCIpfWEoWm4sXCJjaGVja0lFRUU3NTRcIik7ZnVuY3Rpb24gSm4ocixlLHQsbixpKXtyZXR1cm4gZT1cbitlLHQ9dD4+PjAsaXx8Wm4ocixlLHQsNCwzNDAyODIzNDY2Mzg1Mjg4NmUyMiwtMzQwMjgyMzQ2NjM4NTI4ODZlMjIpLFBlLndyaXRlKHIsZSx0LG4sMjMsNCksdCs0fWEoSm4sXG5cIndyaXRlRmxvYXRcIik7aC5wcm90b3R5cGUud3JpdGVGbG9hdExFPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBKbih0aGlzLGUsdCwhMCxuKX0sXCJ3cml0ZUZsb2F0TEVcIik7aC5cbnByb3RvdHlwZS53cml0ZUZsb2F0QkU9YShmdW5jdGlvbihlLHQsbil7cmV0dXJuIEpuKHRoaXMsZSx0LCExLG4pfSxcIndyaXRlRmxvYXRCRVwiKTtmdW5jdGlvbiBYbihyLGUsdCxuLGkpe1xucmV0dXJuIGU9K2UsdD10Pj4+MCxpfHxabihyLGUsdCw4LDE3OTc2OTMxMzQ4NjIzMTU3ZTI5MiwtMTc5NzY5MzEzNDg2MjMxNTdlMjkyKSxQZS53cml0ZShyLGUsdCxuLDUyLFxuOCksdCs4fWEoWG4sXCJ3cml0ZURvdWJsZVwiKTtoLnByb3RvdHlwZS53cml0ZURvdWJsZUxFPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBYbih0aGlzLGUsdCwhMCxuKX0sXCJ3clxcXG5pdGVEb3VibGVMRVwiKTtoLnByb3RvdHlwZS53cml0ZURvdWJsZUJFPWEoZnVuY3Rpb24oZSx0LG4pe3JldHVybiBYbih0aGlzLGUsdCwhMSxuKX0sXCJ3cml0ZURvdWJsZUJFXCIpO1xuaC5wcm90b3R5cGUuY29weT1hKGZ1bmN0aW9uKGUsdCxuLGkpe2lmKCFoLmlzQnVmZmVyKGUpKXRocm93IG5ldyBUeXBlRXJyb3IoXCJhcmd1bWVudCBzaG91bGQgYmUgYSBCdWZcXFxuZmVyXCIpO2lmKG58fChuPTApLCFpJiZpIT09MCYmKGk9dGhpcy5sZW5ndGgpLHQ+PWUubGVuZ3RoJiYodD1lLmxlbmd0aCksdHx8KHQ9MCksaT4wJiZpPG4mJihpPW4pLGk9PT1cbm58fGUubGVuZ3RoPT09MHx8dGhpcy5sZW5ndGg9PT0wKXJldHVybiAwO2lmKHQ8MCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcInRhcmdldFN0YXJ0IG91dCBvZiBib3VuZHNcIik7aWYobjxcbjB8fG4+PXRoaXMubGVuZ3RoKXRocm93IG5ldyBSYW5nZUVycm9yKFwiSW5kZXggb3V0IG9mIHJhbmdlXCIpO2lmKGk8MCl0aHJvdyBuZXcgUmFuZ2VFcnJvcihcInNvdXJjZUVuZCBcXFxub3V0IG9mIGJvdW5kc1wiKTtpPnRoaXMubGVuZ3RoJiYoaT10aGlzLmxlbmd0aCksZS5sZW5ndGgtdDxpLW4mJihpPWUubGVuZ3RoLXQrbik7bGV0IHM9aS1uO3JldHVybiB0aGlzPT09XG5lJiZ0eXBlb2YgVWludDhBcnJheS5wcm90b3R5cGUuY29weVdpdGhpbj09XCJmdW5jdGlvblwiP3RoaXMuY29weVdpdGhpbih0LG4saSk6VWludDhBcnJheS5wcm90b3R5cGUuc2V0LlxuY2FsbChlLHRoaXMuc3ViYXJyYXkobixpKSx0KSxzfSxcImNvcHlcIik7aC5wcm90b3R5cGUuZmlsbD1hKGZ1bmN0aW9uKGUsdCxuLGkpe2lmKHR5cGVvZiBlPT1cInN0cmluZ1wiKXtcbmlmKHR5cGVvZiB0PT1cInN0cmluZ1wiPyhpPXQsdD0wLG49dGhpcy5sZW5ndGgpOnR5cGVvZiBuPT1cInN0cmluZ1wiJiYoaT1uLG49dGhpcy5sZW5ndGgpLGkhPT12b2lkIDAmJnR5cGVvZiBpIT1cblwic3RyaW5nXCIpdGhyb3cgbmV3IFR5cGVFcnJvcihcImVuY29kaW5nIG11c3QgYmUgYSBzdHJpbmdcIik7aWYodHlwZW9mIGk9PVwic3RyaW5nXCImJiFoLmlzRW5jb2RpbmcoaSkpdGhyb3cgbmV3IFR5cGVFcnJvcihcblwiVW5rbm93biBlbmNvZGluZzogXCIraSk7aWYoZS5sZW5ndGg9PT0xKXtsZXQgbz1lLmNoYXJDb2RlQXQoMCk7KGk9PT1cInV0ZjhcIiYmbzwxMjh8fGk9PT1cImxhdGluMVwiKSYmKGU9XG5vKX19ZWxzZSB0eXBlb2YgZT09XCJudW1iZXJcIj9lPWUmMjU1OnR5cGVvZiBlPT1cImJvb2xlYW5cIiYmKGU9TnVtYmVyKGUpKTtpZih0PDB8fHRoaXMubGVuZ3RoPHR8fHRoaXMubGVuZ3RoPFxubil0aHJvdyBuZXcgUmFuZ2VFcnJvcihcIk91dCBvZiByYW5nZSBpbmRleFwiKTtpZihuPD10KXJldHVybiB0aGlzO3Q9dD4+PjAsbj1uPT09dm9pZCAwP3RoaXMubGVuZ3RoOm4+Pj5cbjAsZXx8KGU9MCk7bGV0IHM7aWYodHlwZW9mIGU9PVwibnVtYmVyXCIpZm9yKHM9dDtzPG47KytzKXRoaXNbc109ZTtlbHNle2xldCBvPWguaXNCdWZmZXIoZSk/ZTpoLmZyb20oZSxcbmkpLHU9by5sZW5ndGg7aWYodT09PTApdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIHZhbHVlIFwiJytlKydcIiBpcyBpbnZhbGlkIGZvciBhcmd1bWVudCBcInZhbHVlXCInKTtmb3Iocz1cbjA7czxuLXQ7KytzKXRoaXNbcyt0XT1vW3MldV19cmV0dXJuIHRoaXN9LFwiZmlsbFwiKTt2YXIgSWU9e307ZnVuY3Rpb24gSHQocixlLHQpe3ZhciBuO0llW3JdPShuPWNsYXNzIGV4dGVuZHMgdHtjb25zdHJ1Y3Rvcigpe1xuc3VwZXIoKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcyxcIm1lc3NhZ2VcIix7dmFsdWU6ZS5hcHBseSh0aGlzLGFyZ3VtZW50cyksd3JpdGFibGU6ITAsY29uZmlndXJhYmxlOiEwfSksXG50aGlzLm5hbWU9YCR7dGhpcy5uYW1lfSBbJHtyfV1gLHRoaXMuc3RhY2ssZGVsZXRlIHRoaXMubmFtZX1nZXQgY29kZSgpe3JldHVybiByfXNldCBjb2RlKHMpe09iamVjdC5kZWZpbmVQcm9wZXJ0eShcbnRoaXMsXCJjb2RlXCIse2NvbmZpZ3VyYWJsZTohMCxlbnVtZXJhYmxlOiEwLHZhbHVlOnMsd3JpdGFibGU6ITB9KX10b1N0cmluZygpe3JldHVybmAke3RoaXMubmFtZX0gWyR7cn1cXFxuXTogJHt0aGlzLm1lc3NhZ2V9YH19LGEobixcIk5vZGVFcnJvclwiKSxuKX1hKEh0LFwiRVwiKTtIdChcIkVSUl9CVUZGRVJfT1VUX09GX0JPVU5EU1wiLGZ1bmN0aW9uKHIpe3JldHVybiByP1xuYCR7cn0gaXMgb3V0c2lkZSBvZiBidWZmZXIgYm91bmRzYDpcIkF0dGVtcHQgdG8gYWNjZXNzIG1lbW9yeSBvdXRzaWRlIGJ1ZmZlciBib3VuZHNcIn0sUmFuZ2VFcnJvcik7SHQoXG5cIkVSUl9JTlZBTElEX0FSR19UWVBFXCIsZnVuY3Rpb24ocixlKXtyZXR1cm5gVGhlIFwiJHtyfVwiIGFyZ3VtZW50IG11c3QgYmUgb2YgdHlwZSBudW1iZXIuIFJlY2VpdmVkIHR5cFxcXG5lICR7dHlwZW9mIGV9YH0sVHlwZUVycm9yKTtIdChcIkVSUl9PVVRfT0ZfUkFOR0VcIixmdW5jdGlvbihyLGUsdCl7bGV0IG49YFRoZSB2YWx1ZSBvZiBcIiR7cn1cIiBpcyBvdXQgb1xcXG5mIHJhbmdlLmAsaT10O3JldHVybiBOdW1iZXIuaXNJbnRlZ2VyKHQpJiZNYXRoLmFicyh0KT4yKiozMj9pPVduKFN0cmluZyh0KSk6dHlwZW9mIHQ9PVwiYmlnaW50XCImJihpPVN0cmluZyhcbnQpLCh0PkJpZ0ludCgyKSoqQmlnSW50KDMyKXx8dDwtKEJpZ0ludCgyKSoqQmlnSW50KDMyKSkpJiYoaT1XbihpKSksaSs9XCJuXCIpLG4rPWAgSXQgbXVzdCBiZSAke2V9LiBSZVxcXG5jZWl2ZWQgJHtpfWAsbn0sUmFuZ2VFcnJvcik7ZnVuY3Rpb24gV24ocil7bGV0IGU9XCJcIix0PXIubGVuZ3RoLG49clswXT09PVwiLVwiPzE6MDtmb3IoO3Q+PW4rNDt0LT0zKWU9YFxcXG5fJHtyLnNsaWNlKHQtMyx0KX0ke2V9YDtyZXR1cm5gJHtyLnNsaWNlKDAsdCl9JHtlfWB9YShXbixcImFkZE51bWVyaWNhbFNlcGFyYXRvclwiKTtmdW5jdGlvbiBabyhyLGUsdCl7XG5SZShlLFwib2Zmc2V0XCIpLChyW2VdPT09dm9pZCAwfHxyW2UrdF09PT12b2lkIDApJiZqZShlLHIubGVuZ3RoLSh0KzEpKX1hKFpvLFwiY2hlY2tCb3VuZHNcIik7ZnVuY3Rpb24gZWkocixlLHQsbixpLHMpe1xuaWYocj50fHxyPGUpe2xldCBvPXR5cGVvZiBlPT1cImJpZ2ludFwiP1wiblwiOlwiXCIsdTt0aHJvdyBzPjM/ZT09PTB8fGU9PT1CaWdJbnQoMCk/dT1gPj0gMCR7b30gYW5kIDwgMiR7b31cXFxuICoqICR7KHMrMSkqOH0ke299YDp1PWA+PSAtKDIke299ICoqICR7KHMrMSkqOC0xfSR7b30pIGFuZCA8IDIgKiogJHsocysxKSo4LTF9JHtvfWA6dT1gPj0gJHtlfSR7b30gYVxcXG5uZCA8PSAke3R9JHtvfWAsbmV3IEllLkVSUl9PVVRfT0ZfUkFOR0UoXCJ2YWx1ZVwiLHUscil9Wm8obixpLHMpfWEoZWksXCJjaGVja0ludEJJXCIpO2Z1bmN0aW9uIFJlKHIsZSl7aWYodHlwZW9mIHIhPVxuXCJudW1iZXJcIil0aHJvdyBuZXcgSWUuRVJSX0lOVkFMSURfQVJHX1RZUEUoZSxcIm51bWJlclwiLHIpfWEoUmUsXCJ2YWxpZGF0ZU51bWJlclwiKTtmdW5jdGlvbiBqZShyLGUsdCl7dGhyb3cgTWF0aC5cbmZsb29yKHIpIT09cj8oUmUocix0KSxuZXcgSWUuRVJSX09VVF9PRl9SQU5HRSh0fHxcIm9mZnNldFwiLFwiYW4gaW50ZWdlclwiLHIpKTplPDA/bmV3IEllLkVSUl9CVUZGRVJfT1VUX09GX0JPVU5EUzpcbm5ldyBJZS5FUlJfT1VUX09GX1JBTkdFKHR8fFwib2Zmc2V0XCIsYD49ICR7dD8xOjB9IGFuZCA8PSAke2V9YCxyKX1hKGplLFwiYm91bmRzRXJyb3JcIik7dmFyIEpvPS9bXisvMC05QS1aYS16LV9dL2c7XG5mdW5jdGlvbiBYbyhyKXtpZihyPXIuc3BsaXQoXCI9XCIpWzBdLHI9ci50cmltKCkucmVwbGFjZShKbyxcIlwiKSxyLmxlbmd0aDwyKXJldHVyblwiXCI7Zm9yKDtyLmxlbmd0aCU0IT09XG4wOylyPXIrXCI9XCI7cmV0dXJuIHJ9YShYbyxcImJhc2U2NGNsZWFuXCIpO2Z1bmN0aW9uIE50KHIsZSl7ZT1lfHwxLzA7bGV0IHQsbj1yLmxlbmd0aCxpPW51bGwscz1bXTtmb3IobGV0IG89MDtvPFxubjsrK28pe2lmKHQ9ci5jaGFyQ29kZUF0KG8pLHQ+NTUyOTUmJnQ8NTczNDQpe2lmKCFpKXtpZih0PjU2MzE5KXsoZS09Myk+LTEmJnMucHVzaCgyMzksMTkxLDE4OSk7Y29udGludWV9ZWxzZSBpZihvK1xuMT09PW4peyhlLT0zKT4tMSYmcy5wdXNoKDIzOSwxOTEsMTg5KTtjb250aW51ZX1pPXQ7Y29udGludWV9aWYodDw1NjMyMCl7KGUtPTMpPi0xJiZzLnB1c2goMjM5LDE5MSwxODkpLFxuaT10O2NvbnRpbnVlfXQ9KGktNTUyOTY8PDEwfHQtNTYzMjApKzY1NTM2fWVsc2UgaSYmKGUtPTMpPi0xJiZzLnB1c2goMjM5LDE5MSwxODkpO2lmKGk9bnVsbCx0PDEyOCl7aWYoKGUtPVxuMSk8MClicmVhaztzLnB1c2godCl9ZWxzZSBpZih0PDIwNDgpe2lmKChlLT0yKTwwKWJyZWFrO3MucHVzaCh0Pj42fDE5Mix0JjYzfDEyOCl9ZWxzZSBpZih0PDY1NTM2KXtpZigoZS09XG4zKTwwKWJyZWFrO3MucHVzaCh0Pj4xMnwyMjQsdD4+NiY2M3wxMjgsdCY2M3wxMjgpfWVsc2UgaWYodDwxMTE0MTEyKXtpZigoZS09NCk8MClicmVhaztzLnB1c2godD4+MTh8XG4yNDAsdD4+MTImNjN8MTI4LHQ+PjYmNjN8MTI4LHQmNjN8MTI4KX1lbHNlIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgY29kZSBwb2ludFwiKX1yZXR1cm4gc31hKE50LFwidXRmXFxcbjhUb0J5dGVzXCIpO2Z1bmN0aW9uIGVhKHIpe2xldCBlPVtdO2ZvcihsZXQgdD0wO3Q8ci5sZW5ndGg7Kyt0KWUucHVzaChyLmNoYXJDb2RlQXQodCkmMjU1KTtyZXR1cm4gZX1hKFxuZWEsXCJhc2NpaVRvQnl0ZXNcIik7ZnVuY3Rpb24gdGEocixlKXtsZXQgdCxuLGkscz1bXTtmb3IobGV0IG89MDtvPHIubGVuZ3RoJiYhKChlLT0yKTwwKTsrK28pdD1yLmNoYXJDb2RlQXQoXG5vKSxuPXQ+PjgsaT10JTI1NixzLnB1c2goaSkscy5wdXNoKG4pO3JldHVybiBzfWEodGEsXCJ1dGYxNmxlVG9CeXRlc1wiKTtmdW5jdGlvbiB0aShyKXtyZXR1cm4gT3QudG9CeXRlQXJyYXkoXG5YbyhyKSl9YSh0aSxcImJhc2U2NFRvQnl0ZXNcIik7ZnVuY3Rpb24gZnQocixlLHQsbil7bGV0IGk7Zm9yKGk9MDtpPG4mJiEoaSt0Pj1lLmxlbmd0aHx8aT49ci5sZW5ndGgpOysraSlcbmVbaSt0XT1yW2ldO3JldHVybiBpfWEoZnQsXCJibGl0QnVmZmVyXCIpO2Z1bmN0aW9uIGNlKHIsZSl7cmV0dXJuIHIgaW5zdGFuY2VvZiBlfHxyIT1udWxsJiZyLmNvbnN0cnVjdG9yIT1cbm51bGwmJnIuY29uc3RydWN0b3IubmFtZSE9bnVsbCYmci5jb25zdHJ1Y3Rvci5uYW1lPT09ZS5uYW1lfWEoY2UsXCJpc0luc3RhbmNlXCIpO2Z1bmN0aW9uICR0KHIpe3JldHVybiByIT09XG5yfWEoJHQsXCJudW1iZXJJc05hTlwiKTt2YXIgcmE9ZnVuY3Rpb24oKXtsZXQgcj1cIjAxMjM0NTY3ODlhYmNkZWZcIixlPW5ldyBBcnJheSgyNTYpO2ZvcihsZXQgdD0wO3Q8MTY7Kyt0KXtcbmxldCBuPXQqMTY7Zm9yKGxldCBpPTA7aTwxNjsrK2kpZVtuK2ldPXJbdF0rcltpXX1yZXR1cm4gZX0oKTtmdW5jdGlvbiB5ZShyKXtyZXR1cm4gdHlwZW9mIEJpZ0ludD5cInVcIj9cbm5hOnJ9YSh5ZSxcImRlZmluZUJpZ0ludE1ldGhvZFwiKTtmdW5jdGlvbiBuYSgpe3Rocm93IG5ldyBFcnJvcihcIkJpZ0ludCBub3Qgc3VwcG9ydGVkXCIpfWEobmEsXCJCdWZmZXJCaVxcXG5nSW50Tm90RGVmaW5lZFwiKX0pO3ZhciB3LGIsdixkLG0scD16KCgpPT57XCJ1c2Ugc3RyaWN0XCI7dz1nbG9iYWxUaGlzLGI9Z2xvYmFsVGhpcy5zZXRJbW1lZGlhdGU/PyhyPT5zZXRUaW1lb3V0KHIsMCkpLHY9Z2xvYmFsVGhpcy5cbmNsZWFySW1tZWRpYXRlPz8ocj0+Y2xlYXJUaW1lb3V0KHIpKSxkPXR5cGVvZiBnbG9iYWxUaGlzLkJ1ZmZlcj09XCJmdW5jdGlvblwiJiZ0eXBlb2YgZ2xvYmFsVGhpcy5CdWZmZXIuXG5hbGxvY1Vuc2FmZT09XCJmdW5jdGlvblwiP2dsb2JhbFRoaXMuQnVmZmVyOnJpKCkuQnVmZmVyLG09Z2xvYmFsVGhpcy5wcm9jZXNzPz97fTttLmVudj8/KG0uZW52PXt9KTt0cnl7XG5tLm5leHRUaWNrKCgpPT57fSl9Y2F0Y2h7bGV0IGU9UHJvbWlzZS5yZXNvbHZlKCk7bS5uZXh0VGljaz1lLnRoZW4uYmluZChlKX19KTt2YXIgbWU9SSgoUGwsR3QpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciBMZT10eXBlb2YgUmVmbGVjdD09XCJvYmplY3RcIj9SZWZsZWN0Om51bGwsbmk9TGUmJnR5cGVvZiBMZS5hcHBseT09XG5cImZ1bmN0aW9uXCI/TGUuYXBwbHk6YShmdW5jdGlvbihlLHQsbil7cmV0dXJuIEZ1bmN0aW9uLnByb3RvdHlwZS5hcHBseS5jYWxsKGUsdCxuKX0sXCJSZWZsZWN0QXBwbHlcIiksaHQ7XG5MZSYmdHlwZW9mIExlLm93bktleXM9PVwiZnVuY3Rpb25cIj9odD1MZS5vd25LZXlzOk9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHM/aHQ9YShmdW5jdGlvbihlKXtyZXR1cm4gT2JqZWN0LlxuZ2V0T3duUHJvcGVydHlOYW1lcyhlKS5jb25jYXQoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhlKSl9LFwiUmVmbGVjdE93bktleXNcIik6aHQ9YShmdW5jdGlvbihlKXtyZXR1cm4gT2JqZWN0LlxuZ2V0T3duUHJvcGVydHlOYW1lcyhlKX0sXCJSZWZsZWN0T3duS2V5c1wiKTtmdW5jdGlvbiBpYShyKXtjb25zb2xlJiZjb25zb2xlLndhcm4mJmNvbnNvbGUud2FybihyKX1hKGlhLFxuXCJQcm9jZXNzRW1pdFdhcm5pbmdcIik7dmFyIHNpPU51bWJlci5pc05hTnx8YShmdW5jdGlvbihlKXtyZXR1cm4gZSE9PWV9LFwiTnVtYmVySXNOYU5cIik7ZnVuY3Rpb24gUigpe1IuXG5pbml0LmNhbGwodGhpcyl9YShSLFwiRXZlbnRFbWl0dGVyXCIpO0d0LmV4cG9ydHM9UjtHdC5leHBvcnRzLm9uY2U9dWE7Ui5FdmVudEVtaXR0ZXI9UjtSLnByb3RvdHlwZS5fZXZlbnRzPVxudm9pZCAwO1IucHJvdG90eXBlLl9ldmVudHNDb3VudD0wO1IucHJvdG90eXBlLl9tYXhMaXN0ZW5lcnM9dm9pZCAwO3ZhciBpaT0xMDtmdW5jdGlvbiBwdChyKXtpZih0eXBlb2YgciE9XG5cImZ1bmN0aW9uXCIpdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIFwibGlzdGVuZXJcIiBhcmd1bWVudCBtdXN0IGJlIG9mIHR5cGUgRnVuY3Rpb24uIFJlY2VpdmVkIHR5cGUgJyt0eXBlb2Ygcil9XG5hKHB0LFwiY2hlY2tMaXN0ZW5lclwiKTtPYmplY3QuZGVmaW5lUHJvcGVydHkoUixcImRlZmF1bHRNYXhMaXN0ZW5lcnNcIix7ZW51bWVyYWJsZTohMCxnZXQ6YShmdW5jdGlvbigpe1xucmV0dXJuIGlpfSxcImdldFwiKSxzZXQ6YShmdW5jdGlvbihyKXtpZih0eXBlb2YgciE9XCJudW1iZXJcInx8cjwwfHxzaShyKSl0aHJvdyBuZXcgUmFuZ2VFcnJvcignVGhlIHZhbHVcXFxuZSBvZiBcImRlZmF1bHRNYXhMaXN0ZW5lcnNcIiBpcyBvdXQgb2YgcmFuZ2UuIEl0IG11c3QgYmUgYSBub24tbmVnYXRpdmUgbnVtYmVyLiBSZWNlaXZlZCAnK3IrXCIuXCIpO2lpPXJ9LFxuXCJzZXRcIil9KTtSLmluaXQ9ZnVuY3Rpb24oKXsodGhpcy5fZXZlbnRzPT09dm9pZCAwfHx0aGlzLl9ldmVudHM9PT1PYmplY3QuZ2V0UHJvdG90eXBlT2YodGhpcykuX2V2ZW50cykmJlxuKHRoaXMuX2V2ZW50cz1PYmplY3QuY3JlYXRlKG51bGwpLHRoaXMuX2V2ZW50c0NvdW50PTApLHRoaXMuX21heExpc3RlbmVycz10aGlzLl9tYXhMaXN0ZW5lcnN8fHZvaWQgMH07XG5SLnByb3RvdHlwZS5zZXRNYXhMaXN0ZW5lcnM9YShmdW5jdGlvbihlKXtpZih0eXBlb2YgZSE9XCJudW1iZXJcInx8ZTwwfHxzaShlKSl0aHJvdyBuZXcgUmFuZ2VFcnJvcignVGhcXFxuZSB2YWx1ZSBvZiBcIm5cIiBpcyBvdXQgb2YgcmFuZ2UuIEl0IG11c3QgYmUgYSBub24tbmVnYXRpdmUgbnVtYmVyLiBSZWNlaXZlZCAnK2UrXCIuXCIpO3JldHVybiB0aGlzLl9tYXhMaXN0ZW5lcnM9XG5lLHRoaXN9LFwic2V0TWF4TGlzdGVuZXJzXCIpO2Z1bmN0aW9uIG9pKHIpe3JldHVybiByLl9tYXhMaXN0ZW5lcnM9PT12b2lkIDA/Ui5kZWZhdWx0TWF4TGlzdGVuZXJzOnIuX21heExpc3RlbmVyc31cbmEob2ksXCJfZ2V0TWF4TGlzdGVuZXJzXCIpO1IucHJvdG90eXBlLmdldE1heExpc3RlbmVycz1hKGZ1bmN0aW9uKCl7cmV0dXJuIG9pKHRoaXMpfSxcImdldE1heExpc3RlbmVyc1wiKTtcblIucHJvdG90eXBlLmVtaXQ9YShmdW5jdGlvbihlKXtmb3IodmFyIHQ9W10sbj0xO248YXJndW1lbnRzLmxlbmd0aDtuKyspdC5wdXNoKGFyZ3VtZW50c1tuXSk7dmFyIGk9ZT09PVxuXCJlcnJvclwiLHM9dGhpcy5fZXZlbnRzO2lmKHMhPT12b2lkIDApaT1pJiZzLmVycm9yPT09dm9pZCAwO2Vsc2UgaWYoIWkpcmV0dXJuITE7aWYoaSl7dmFyIG87aWYodC5sZW5ndGg+XG4wJiYobz10WzBdKSxvIGluc3RhbmNlb2YgRXJyb3IpdGhyb3cgbzt2YXIgdT1uZXcgRXJyb3IoXCJVbmhhbmRsZWQgZXJyb3IuXCIrKG8/XCIgKFwiK28ubWVzc2FnZStcIilcIjpcIlwiKSk7XG50aHJvdyB1LmNvbnRleHQ9byx1fXZhciBjPXNbZV07aWYoYz09PXZvaWQgMClyZXR1cm4hMTtpZih0eXBlb2YgYz09XCJmdW5jdGlvblwiKW5pKGMsdGhpcyx0KTtlbHNlIGZvcih2YXIgbD1jLlxubGVuZ3RoLGY9ZmkoYyxsKSxuPTA7bjxsOysrbiluaShmW25dLHRoaXMsdCk7cmV0dXJuITB9LFwiZW1pdFwiKTtmdW5jdGlvbiBhaShyLGUsdCxuKXt2YXIgaSxzLG87aWYocHQoXG50KSxzPXIuX2V2ZW50cyxzPT09dm9pZCAwPyhzPXIuX2V2ZW50cz1PYmplY3QuY3JlYXRlKG51bGwpLHIuX2V2ZW50c0NvdW50PTApOihzLm5ld0xpc3RlbmVyIT09dm9pZCAwJiZcbihyLmVtaXQoXCJuZXdMaXN0ZW5lclwiLGUsdC5saXN0ZW5lcj90Lmxpc3RlbmVyOnQpLHM9ci5fZXZlbnRzKSxvPXNbZV0pLG89PT12b2lkIDApbz1zW2VdPXQsKytyLl9ldmVudHNDb3VudDtlbHNlIGlmKHR5cGVvZiBvPT1cblwiZnVuY3Rpb25cIj9vPXNbZV09bj9bdCxvXTpbbyx0XTpuP28udW5zaGlmdCh0KTpvLnB1c2godCksaT1vaShyKSxpPjAmJm8ubGVuZ3RoPmkmJiFvLndhcm5lZCl7by53YXJuZWQ9XG4hMDt2YXIgdT1uZXcgRXJyb3IoXCJQb3NzaWJsZSBFdmVudEVtaXR0ZXIgbWVtb3J5IGxlYWsgZGV0ZWN0ZWQuIFwiK28ubGVuZ3RoK1wiIFwiK1N0cmluZyhlKStcIiBsaXN0ZW5lcnNcXFxuIGFkZGVkLiBVc2UgZW1pdHRlci5zZXRNYXhMaXN0ZW5lcnMoKSB0byBpbmNyZWFzZSBsaW1pdFwiKTt1Lm5hbWU9XCJNYXhMaXN0ZW5lcnNFeGNlZWRlZFdhcm5pbmdcIix1LmVtaXR0ZXI9XG5yLHUudHlwZT1lLHUuY291bnQ9by5sZW5ndGgsaWEodSl9cmV0dXJuIHJ9YShhaSxcIl9hZGRMaXN0ZW5lclwiKTtSLnByb3RvdHlwZS5hZGRMaXN0ZW5lcj1hKGZ1bmN0aW9uKGUsdCl7XG5yZXR1cm4gYWkodGhpcyxlLHQsITEpfSxcImFkZExpc3RlbmVyXCIpO1IucHJvdG90eXBlLm9uPVIucHJvdG90eXBlLmFkZExpc3RlbmVyO1IucHJvdG90eXBlLnByZXBlbmRMaXN0ZW5lcj1cbmEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gYWkodGhpcyxlLHQsITApfSxcInByZXBlbmRMaXN0ZW5lclwiKTtmdW5jdGlvbiBzYSgpe2lmKCF0aGlzLmZpcmVkKXJldHVybiB0aGlzLlxudGFyZ2V0LnJlbW92ZUxpc3RlbmVyKHRoaXMudHlwZSx0aGlzLndyYXBGbiksdGhpcy5maXJlZD0hMCxhcmd1bWVudHMubGVuZ3RoPT09MD90aGlzLmxpc3RlbmVyLmNhbGwodGhpcy5cbnRhcmdldCk6dGhpcy5saXN0ZW5lci5hcHBseSh0aGlzLnRhcmdldCxhcmd1bWVudHMpfWEoc2EsXCJvbmNlV3JhcHBlclwiKTtmdW5jdGlvbiB1aShyLGUsdCl7dmFyIG49e2ZpcmVkOiExLFxud3JhcEZuOnZvaWQgMCx0YXJnZXQ6cix0eXBlOmUsbGlzdGVuZXI6dH0saT1zYS5iaW5kKG4pO3JldHVybiBpLmxpc3RlbmVyPXQsbi53cmFwRm49aSxpfWEodWksXCJfb25jZVdcXFxucmFwXCIpO1IucHJvdG90eXBlLm9uY2U9YShmdW5jdGlvbihlLHQpe3JldHVybiBwdCh0KSx0aGlzLm9uKGUsdWkodGhpcyxlLHQpKSx0aGlzfSxcIm9uY2VcIik7Ui5wcm90b3R5cGUuXG5wcmVwZW5kT25jZUxpc3RlbmVyPWEoZnVuY3Rpb24oZSx0KXtyZXR1cm4gcHQodCksdGhpcy5wcmVwZW5kTGlzdGVuZXIoZSx1aSh0aGlzLGUsdCkpLHRoaXN9LFwicHJlcGVuZFxcXG5PbmNlTGlzdGVuZXJcIik7Ui5wcm90b3R5cGUucmVtb3ZlTGlzdGVuZXI9YShmdW5jdGlvbihlLHQpe3ZhciBuLGkscyxvLHU7aWYocHQodCksaT10aGlzLl9ldmVudHMsaT09PVxudm9pZCAwKXJldHVybiB0aGlzO2lmKG49aVtlXSxuPT09dm9pZCAwKXJldHVybiB0aGlzO2lmKG49PT10fHxuLmxpc3RlbmVyPT09dCktLXRoaXMuX2V2ZW50c0NvdW50PT09MD9cbnRoaXMuX2V2ZW50cz1PYmplY3QuY3JlYXRlKG51bGwpOihkZWxldGUgaVtlXSxpLnJlbW92ZUxpc3RlbmVyJiZ0aGlzLmVtaXQoXCJyZW1vdmVMaXN0ZW5lclwiLGUsbi5saXN0ZW5lcnx8XG50KSk7ZWxzZSBpZih0eXBlb2YgbiE9XCJmdW5jdGlvblwiKXtmb3Iocz0tMSxvPW4ubGVuZ3RoLTE7bz49MDtvLS0paWYobltvXT09PXR8fG5bb10ubGlzdGVuZXI9PT10KXt1PW5bb10uXG5saXN0ZW5lcixzPW87YnJlYWt9aWYoczwwKXJldHVybiB0aGlzO3M9PT0wP24uc2hpZnQoKTpvYShuLHMpLG4ubGVuZ3RoPT09MSYmKGlbZV09blswXSksaS5yZW1vdmVMaXN0ZW5lciE9PVxudm9pZCAwJiZ0aGlzLmVtaXQoXCJyZW1vdmVMaXN0ZW5lclwiLGUsdXx8dCl9cmV0dXJuIHRoaXN9LFwicmVtb3ZlTGlzdGVuZXJcIik7Ui5wcm90b3R5cGUub2ZmPVIucHJvdG90eXBlLlxucmVtb3ZlTGlzdGVuZXI7Ui5wcm90b3R5cGUucmVtb3ZlQWxsTGlzdGVuZXJzPWEoZnVuY3Rpb24oZSl7dmFyIHQsbixpO2lmKG49dGhpcy5fZXZlbnRzLG49PT12b2lkIDApcmV0dXJuIHRoaXM7XG5pZihuLnJlbW92ZUxpc3RlbmVyPT09dm9pZCAwKXJldHVybiBhcmd1bWVudHMubGVuZ3RoPT09MD8odGhpcy5fZXZlbnRzPU9iamVjdC5jcmVhdGUobnVsbCksdGhpcy5fZXZlbnRzQ291bnQ9XG4wKTpuW2VdIT09dm9pZCAwJiYoLS10aGlzLl9ldmVudHNDb3VudD09PTA/dGhpcy5fZXZlbnRzPU9iamVjdC5jcmVhdGUobnVsbCk6ZGVsZXRlIG5bZV0pLHRoaXM7aWYoYXJndW1lbnRzLlxubGVuZ3RoPT09MCl7dmFyIHM9T2JqZWN0LmtleXMobiksbztmb3IoaT0wO2k8cy5sZW5ndGg7KytpKW89c1tpXSxvIT09XCJyZW1vdmVMaXN0ZW5lclwiJiZ0aGlzLnJlbW92ZUFsbExpc3RlbmVycyhcbm8pO3JldHVybiB0aGlzLnJlbW92ZUFsbExpc3RlbmVycyhcInJlbW92ZUxpc3RlbmVyXCIpLHRoaXMuX2V2ZW50cz1PYmplY3QuY3JlYXRlKG51bGwpLHRoaXMuX2V2ZW50c0NvdW50PVxuMCx0aGlzfWlmKHQ9bltlXSx0eXBlb2YgdD09XCJmdW5jdGlvblwiKXRoaXMucmVtb3ZlTGlzdGVuZXIoZSx0KTtlbHNlIGlmKHQhPT12b2lkIDApZm9yKGk9dC5sZW5ndGgtMTtpPj1cbjA7aS0tKXRoaXMucmVtb3ZlTGlzdGVuZXIoZSx0W2ldKTtyZXR1cm4gdGhpc30sXCJyZW1vdmVBbGxMaXN0ZW5lcnNcIik7ZnVuY3Rpb24gY2kocixlLHQpe3ZhciBuPXIuX2V2ZW50cztcbmlmKG49PT12b2lkIDApcmV0dXJuW107dmFyIGk9bltlXTtyZXR1cm4gaT09PXZvaWQgMD9bXTp0eXBlb2YgaT09XCJmdW5jdGlvblwiP3Q/W2kubGlzdGVuZXJ8fGldOltpXTp0P1xuYWEoaSk6ZmkoaSxpLmxlbmd0aCl9YShjaSxcIl9saXN0ZW5lcnNcIik7Ui5wcm90b3R5cGUubGlzdGVuZXJzPWEoZnVuY3Rpb24oZSl7cmV0dXJuIGNpKHRoaXMsZSwhMCl9LFwibFxcXG5pc3RlbmVyc1wiKTtSLnByb3RvdHlwZS5yYXdMaXN0ZW5lcnM9YShmdW5jdGlvbihlKXtyZXR1cm4gY2kodGhpcyxlLCExKX0sXCJyYXdMaXN0ZW5lcnNcIik7Ui5saXN0ZW5lckNvdW50PVxuZnVuY3Rpb24ocixlKXtyZXR1cm4gdHlwZW9mIHIubGlzdGVuZXJDb3VudD09XCJmdW5jdGlvblwiP3IubGlzdGVuZXJDb3VudChlKTpsaS5jYWxsKHIsZSl9O1IucHJvdG90eXBlLlxubGlzdGVuZXJDb3VudD1saTtmdW5jdGlvbiBsaShyKXt2YXIgZT10aGlzLl9ldmVudHM7aWYoZSE9PXZvaWQgMCl7dmFyIHQ9ZVtyXTtpZih0eXBlb2YgdD09XCJmdW5jdGlvblwiKVxucmV0dXJuIDE7aWYodCE9PXZvaWQgMClyZXR1cm4gdC5sZW5ndGh9cmV0dXJuIDB9YShsaSxcImxpc3RlbmVyQ291bnRcIik7Ui5wcm90b3R5cGUuZXZlbnROYW1lcz1hKGZ1bmN0aW9uKCl7XG5yZXR1cm4gdGhpcy5fZXZlbnRzQ291bnQ+MD9odCh0aGlzLl9ldmVudHMpOltdfSxcImV2ZW50TmFtZXNcIik7ZnVuY3Rpb24gZmkocixlKXtmb3IodmFyIHQ9bmV3IEFycmF5KGUpLFxubj0wO248ZTsrK24pdFtuXT1yW25dO3JldHVybiB0fWEoZmksXCJhcnJheUNsb25lXCIpO2Z1bmN0aW9uIG9hKHIsZSl7Zm9yKDtlKzE8ci5sZW5ndGg7ZSsrKXJbZV09cltlKzFdO1xuci5wb3AoKX1hKG9hLFwic3BsaWNlT25lXCIpO2Z1bmN0aW9uIGFhKHIpe2Zvcih2YXIgZT1uZXcgQXJyYXkoci5sZW5ndGgpLHQ9MDt0PGUubGVuZ3RoOysrdCllW3RdPXJbdF0uXG5saXN0ZW5lcnx8clt0XTtyZXR1cm4gZX1hKGFhLFwidW53cmFwTGlzdGVuZXJzXCIpO2Z1bmN0aW9uIHVhKHIsZSl7cmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uKHQsbil7ZnVuY3Rpb24gaShvKXtcbnIucmVtb3ZlTGlzdGVuZXIoZSxzKSxuKG8pfWEoaSxcImVycm9yTGlzdGVuZXJcIik7ZnVuY3Rpb24gcygpe3R5cGVvZiByLnJlbW92ZUxpc3RlbmVyPT1cImZ1bmN0aW9uXCImJnIuXG5yZW1vdmVMaXN0ZW5lcihcImVycm9yXCIsaSksdChbXS5zbGljZS5jYWxsKGFyZ3VtZW50cykpfWEocyxcInJlc29sdmVyXCIpLGhpKHIsZSxzLHtvbmNlOiEwfSksZSE9PVwiZXJyb3JcIiYmXG5jYShyLGkse29uY2U6ITB9KX0pfWEodWEsXCJvbmNlXCIpO2Z1bmN0aW9uIGNhKHIsZSx0KXt0eXBlb2Ygci5vbj09XCJmdW5jdGlvblwiJiZoaShyLFwiZXJyb3JcIixlLHQpfWEoY2EsXG5cImFkZEVycm9ySGFuZGxlcklmRXZlbnRFbWl0dGVyXCIpO2Z1bmN0aW9uIGhpKHIsZSx0LG4pe2lmKHR5cGVvZiByLm9uPT1cImZ1bmN0aW9uXCIpbi5vbmNlP3Iub25jZShlLHQpOlxuci5vbihlLHQpO2Vsc2UgaWYodHlwZW9mIHIuYWRkRXZlbnRMaXN0ZW5lcj09XCJmdW5jdGlvblwiKXIuYWRkRXZlbnRMaXN0ZW5lcihlLGEoZnVuY3Rpb24gaShzKXtuLm9uY2UmJlxuci5yZW1vdmVFdmVudExpc3RlbmVyKGUsaSksdChzKX0sXCJ3cmFwTGlzdGVuZXJcIikpO2Vsc2UgdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIFwiZW1pdHRlclwiIGFyZ3VtZW50IG1cXFxudXN0IGJlIG9mIHR5cGUgRXZlbnRFbWl0dGVyLiBSZWNlaXZlZCB0eXBlICcrdHlwZW9mIHIpfWEoaGksXCJldmVudFRhcmdldEFnbm9zdGljQWRkTGlzdGVuZXJcIil9KTt2YXIgeWk9e307bmUoeWkse1NvY2tldDooKT0+Z2UsaXNJUDooKT0+bGF9KTtmdW5jdGlvbiBsYShyKXtyZXR1cm4gMH12YXIgZGkscGksUyxnZSxXZT16KCgpPT57XCJ1c2Ugc1xcXG50cmljdFwiO3AoKTtkaT14ZShtZSgpLDEpO2EobGEsXCJpc0lQXCIpO3BpPS9eW14uXStcXC4vLFM9Y2xhc3MgUyBleHRlbmRzIGRpLkV2ZW50RW1pdHRlcntjb25zdHJ1Y3Rvcigpe1xuc3VwZXIoLi4uYXJndW1lbnRzKTtFKHRoaXMsXCJvcHRzXCIse30pO0UodGhpcyxcImNvbm5lY3RpbmdcIiwhMSk7RSh0aGlzLFwicGVuZGluZ1wiLCEwKTtFKHRoaXMsXCJ3cml0YWJsZVwiLFxuITApO0UodGhpcyxcImVuY3J5cHRlZFwiLCExKTtFKHRoaXMsXCJhdXRob3JpemVkXCIsITEpO0UodGhpcyxcImRlc3Ryb3llZFwiLCExKTtFKHRoaXMsXCJ3c1wiLG51bGwpO0UodGhpcyxcIlxcXG53cml0ZUJ1ZmZlclwiKTtFKHRoaXMsXCJ0bHNTdGF0ZVwiLDApO0UodGhpcyxcInRsc1JlYWRcIik7RSh0aGlzLFwidGxzV3JpdGVcIil9c3RhdGljIGdldCBwb29sUXVlcnlWaWFGZXRjaCgpe1xucmV0dXJuIFMub3B0cy5wb29sUXVlcnlWaWFGZXRjaD8/Uy5kZWZhdWx0cy5wb29sUXVlcnlWaWFGZXRjaH1zdGF0aWMgc2V0IHBvb2xRdWVyeVZpYUZldGNoKHQpe1Mub3B0cy5cbnBvb2xRdWVyeVZpYUZldGNoPXR9c3RhdGljIGdldCBmZXRjaEVuZHBvaW50KCl7cmV0dXJuIFMub3B0cy5mZXRjaEVuZHBvaW50Pz9TLmRlZmF1bHRzLmZldGNoRW5kcG9pbnR9c3RhdGljIHNldCBmZXRjaEVuZHBvaW50KHQpe1xuUy5vcHRzLmZldGNoRW5kcG9pbnQ9dH1zdGF0aWMgZ2V0IGZldGNoQ29ubmVjdGlvbkNhY2hlKCl7cmV0dXJuITB9c3RhdGljIHNldCBmZXRjaENvbm5lY3Rpb25DYWNoZSh0KXtcbmNvbnNvbGUud2FybihcIlRoZSBgZmV0Y2hDb25uZWN0aW9uQ2FjaGVgIG9wdGlvbiBpcyBkZXByZWNhdGVkIChub3cgYWx3YXlzIGB0cnVlYClcIil9c3RhdGljIGdldCBmZXRjaEZ1bmN0aW9uKCl7XG5yZXR1cm4gUy5vcHRzLmZldGNoRnVuY3Rpb24/P1MuZGVmYXVsdHMuZmV0Y2hGdW5jdGlvbn1zdGF0aWMgc2V0IGZldGNoRnVuY3Rpb24odCl7Uy5vcHRzLmZldGNoRnVuY3Rpb249XG50fXN0YXRpYyBnZXQgd2ViU29ja2V0Q29uc3RydWN0b3IoKXtyZXR1cm4gUy5vcHRzLndlYlNvY2tldENvbnN0cnVjdG9yPz9TLmRlZmF1bHRzLndlYlNvY2tldENvbnN0cnVjdG9yfXN0YXRpYyBzZXQgd2ViU29ja2V0Q29uc3RydWN0b3IodCl7XG5TLm9wdHMud2ViU29ja2V0Q29uc3RydWN0b3I9dH1nZXQgd2ViU29ja2V0Q29uc3RydWN0b3IoKXtyZXR1cm4gdGhpcy5vcHRzLndlYlNvY2tldENvbnN0cnVjdG9yPz9TLndlYlNvY2tldENvbnN0cnVjdG9yfXNldCB3ZWJTb2NrZXRDb25zdHJ1Y3Rvcih0KXtcbnRoaXMub3B0cy53ZWJTb2NrZXRDb25zdHJ1Y3Rvcj10fXN0YXRpYyBnZXQgd3NQcm94eSgpe3JldHVybiBTLm9wdHMud3NQcm94eT8/Uy5kZWZhdWx0cy53c1Byb3h5fXN0YXRpYyBzZXQgd3NQcm94eSh0KXtcblMub3B0cy53c1Byb3h5PXR9Z2V0IHdzUHJveHkoKXtyZXR1cm4gdGhpcy5vcHRzLndzUHJveHk/P1Mud3NQcm94eX1zZXQgd3NQcm94eSh0KXt0aGlzLm9wdHMud3NQcm94eT1cbnR9c3RhdGljIGdldCBjb2FsZXNjZVdyaXRlcygpe3JldHVybiBTLm9wdHMuY29hbGVzY2VXcml0ZXM/P1MuZGVmYXVsdHMuY29hbGVzY2VXcml0ZXN9c3RhdGljIHNldCBjb2FsZXNjZVdyaXRlcyh0KXtcblMub3B0cy5jb2FsZXNjZVdyaXRlcz10fWdldCBjb2FsZXNjZVdyaXRlcygpe3JldHVybiB0aGlzLm9wdHMuY29hbGVzY2VXcml0ZXM/P1MuY29hbGVzY2VXcml0ZXN9c2V0IGNvYWxlc2NlV3JpdGVzKHQpe1xudGhpcy5vcHRzLmNvYWxlc2NlV3JpdGVzPXR9c3RhdGljIGdldCB1c2VTZWN1cmVXZWJTb2NrZXQoKXtyZXR1cm4gUy5vcHRzLnVzZVNlY3VyZVdlYlNvY2tldD8/Uy5kZWZhdWx0cy5cbnVzZVNlY3VyZVdlYlNvY2tldH1zdGF0aWMgc2V0IHVzZVNlY3VyZVdlYlNvY2tldCh0KXtTLm9wdHMudXNlU2VjdXJlV2ViU29ja2V0PXR9Z2V0IHVzZVNlY3VyZVdlYlNvY2tldCgpe1xucmV0dXJuIHRoaXMub3B0cy51c2VTZWN1cmVXZWJTb2NrZXQ/P1MudXNlU2VjdXJlV2ViU29ja2V0fXNldCB1c2VTZWN1cmVXZWJTb2NrZXQodCl7dGhpcy5vcHRzLnVzZVNlY3VyZVdlYlNvY2tldD1cbnR9c3RhdGljIGdldCBmb3JjZURpc2FibGVQZ1NTTCgpe3JldHVybiBTLm9wdHMuZm9yY2VEaXNhYmxlUGdTU0w/P1MuZGVmYXVsdHMuZm9yY2VEaXNhYmxlUGdTU0x9c3RhdGljIHNldCBmb3JjZURpc2FibGVQZ1NTTCh0KXtcblMub3B0cy5mb3JjZURpc2FibGVQZ1NTTD10fWdldCBmb3JjZURpc2FibGVQZ1NTTCgpe3JldHVybiB0aGlzLm9wdHMuZm9yY2VEaXNhYmxlUGdTU0w/P1MuZm9yY2VEaXNhYmxlUGdTU0x9c2V0IGZvcmNlRGlzYWJsZVBnU1NMKHQpe1xudGhpcy5vcHRzLmZvcmNlRGlzYWJsZVBnU1NMPXR9c3RhdGljIGdldCBkaXNhYmxlU05JKCl7cmV0dXJuIFMub3B0cy5kaXNhYmxlU05JPz9TLmRlZmF1bHRzLmRpc2FibGVTTkl9c3RhdGljIHNldCBkaXNhYmxlU05JKHQpe1xuUy5vcHRzLmRpc2FibGVTTkk9dH1nZXQgZGlzYWJsZVNOSSgpe3JldHVybiB0aGlzLm9wdHMuZGlzYWJsZVNOST8/Uy5kaXNhYmxlU05JfXNldCBkaXNhYmxlU05JKHQpe3RoaXMuXG5vcHRzLmRpc2FibGVTTkk9dH1zdGF0aWMgZ2V0IHBpcGVsaW5lQ29ubmVjdCgpe3JldHVybiBTLm9wdHMucGlwZWxpbmVDb25uZWN0Pz9TLmRlZmF1bHRzLnBpcGVsaW5lQ29ubmVjdH1zdGF0aWMgc2V0IHBpcGVsaW5lQ29ubmVjdCh0KXtcblMub3B0cy5waXBlbGluZUNvbm5lY3Q9dH1nZXQgcGlwZWxpbmVDb25uZWN0KCl7cmV0dXJuIHRoaXMub3B0cy5waXBlbGluZUNvbm5lY3Q/P1MucGlwZWxpbmVDb25uZWN0fXNldCBwaXBlbGluZUNvbm5lY3QodCl7XG50aGlzLm9wdHMucGlwZWxpbmVDb25uZWN0PXR9c3RhdGljIGdldCBzdWJ0bHMoKXtyZXR1cm4gUy5vcHRzLnN1YnRscz8/Uy5kZWZhdWx0cy5zdWJ0bHN9c3RhdGljIHNldCBzdWJ0bHModCl7XG5TLm9wdHMuc3VidGxzPXR9Z2V0IHN1YnRscygpe3JldHVybiB0aGlzLm9wdHMuc3VidGxzPz9TLnN1YnRsc31zZXQgc3VidGxzKHQpe3RoaXMub3B0cy5zdWJ0bHM9dH1zdGF0aWMgZ2V0IHBpcGVsaW5lVExTKCl7XG5yZXR1cm4gUy5vcHRzLnBpcGVsaW5lVExTPz9TLmRlZmF1bHRzLnBpcGVsaW5lVExTfXN0YXRpYyBzZXQgcGlwZWxpbmVUTFModCl7Uy5vcHRzLnBpcGVsaW5lVExTPXR9Z2V0IHBpcGVsaW5lVExTKCl7XG5yZXR1cm4gdGhpcy5vcHRzLnBpcGVsaW5lVExTPz9TLnBpcGVsaW5lVExTfXNldCBwaXBlbGluZVRMUyh0KXt0aGlzLm9wdHMucGlwZWxpbmVUTFM9dH1zdGF0aWMgZ2V0IHJvb3RDZXJ0cygpe1xucmV0dXJuIFMub3B0cy5yb290Q2VydHM/P1MuZGVmYXVsdHMucm9vdENlcnRzfXN0YXRpYyBzZXQgcm9vdENlcnRzKHQpe1Mub3B0cy5yb290Q2VydHM9dH1nZXQgcm9vdENlcnRzKCl7XG5yZXR1cm4gdGhpcy5vcHRzLnJvb3RDZXJ0cz8/Uy5yb290Q2VydHN9c2V0IHJvb3RDZXJ0cyh0KXt0aGlzLm9wdHMucm9vdENlcnRzPXR9d3NQcm94eUFkZHJGb3JIb3N0KHQsbil7XG5sZXQgaT10aGlzLndzUHJveHk7aWYoaT09PXZvaWQgMCl0aHJvdyBuZXcgRXJyb3IoXCJObyBXZWJTb2NrZXQgcHJveHkgaXMgY29uZmlndXJlZC4gUGxlYXNlIHNlZSBodHRwc1xcXG46Ly9naXRodWIuY29tL25lb25kYXRhYmFzZS9zZXJ2ZXJsZXNzL2Jsb2IvbWFpbi9DT05GSUcubWQjd3Nwcm94eS1zdHJpbmctLWhvc3Qtc3RyaW5nLXBvcnQtbnVtYmVyLS1zXFxcbnRyaW5nLS1zdHJpbmdcIik7cmV0dXJuIHR5cGVvZiBpPT1cImZ1bmN0aW9uXCI/aSh0LG4pOmAke2l9P2FkZHJlc3M9JHt0fToke259YH1zZXROb0RlbGF5KCl7cmV0dXJuIHRoaXN9c2V0S2VlcEFsaXZlKCl7XG5yZXR1cm4gdGhpc31yZWYoKXtyZXR1cm4gdGhpc311bnJlZigpe3JldHVybiB0aGlzfWNvbm5lY3QodCxuLGkpe3RoaXMuY29ubmVjdGluZz0hMCxpJiZ0aGlzLm9uY2UoXCJjb1xcXG5ubmVjdFwiLGkpO2xldCBzPWEoKCk9Pnt0aGlzLmNvbm5lY3Rpbmc9ITEsdGhpcy5wZW5kaW5nPSExLHRoaXMuZW1pdChcImNvbm5lY3RcIiksdGhpcy5lbWl0KFwicmVhZHlcIil9LFwiXFxcbmhhbmRsZVdlYlNvY2tldE9wZW5cIiksbz1hKChjLGw9ITEpPT57Yy5iaW5hcnlUeXBlPVwiYXJyYXlidWZmZXJcIixjLmFkZEV2ZW50TGlzdGVuZXIoXCJlcnJvclwiLGY9Pnt0aGlzLlxuZW1pdChcImVycm9yXCIsZiksdGhpcy5lbWl0KFwiY2xvc2VcIil9KSxjLmFkZEV2ZW50TGlzdGVuZXIoXCJtZXNzYWdlXCIsZj0+e2lmKHRoaXMudGxzU3RhdGU9PT0wKXtsZXQgeT1kLlxuZnJvbShmLmRhdGEpO3RoaXMuZW1pdChcImRhdGFcIix5KX19KSxjLmFkZEV2ZW50TGlzdGVuZXIoXCJjbG9zZVwiLCgpPT57dGhpcy5lbWl0KFwiY2xvc2VcIil9KSxsP3MoKTpjLmFkZEV2ZW50TGlzdGVuZXIoXG5cIm9wZW5cIixzKX0sXCJjb25maWd1cmVXZWJTb2NrZXRcIiksdTt0cnl7dT10aGlzLndzUHJveHlBZGRyRm9ySG9zdChuLHR5cGVvZiB0PT1cInN0cmluZ1wiP3BhcnNlSW50KHQsMTApOlxudCl9Y2F0Y2goYyl7dGhpcy5lbWl0KFwiZXJyb3JcIixjKSx0aGlzLmVtaXQoXCJjbG9zZVwiKTtyZXR1cm59dHJ5e2xldCBsPSh0aGlzLnVzZVNlY3VyZVdlYlNvY2tldD9cIndzczpcIjpcblwid3M6XCIpK1wiLy9cIit1O2lmKHRoaXMud2ViU29ja2V0Q29uc3RydWN0b3IhPT12b2lkIDApdGhpcy53cz1uZXcgdGhpcy53ZWJTb2NrZXRDb25zdHJ1Y3RvcihsKSxvKHRoaXMuXG53cyk7ZWxzZSB0cnl7dGhpcy53cz1uZXcgV2ViU29ja2V0KGwpLG8odGhpcy53cyl9Y2F0Y2h7dGhpcy53cz1uZXcgX191bnN0YWJsZV9XZWJTb2NrZXQobCksbyh0aGlzLndzKX19Y2F0Y2goYyl7XG5sZXQgZj0odGhpcy51c2VTZWN1cmVXZWJTb2NrZXQ/XCJodHRwczpcIjpcImh0dHA6XCIpK1wiLy9cIit1O2ZldGNoKGYse2hlYWRlcnM6e1VwZ3JhZGU6XCJ3ZWJzb2NrZXRcIn19KS50aGVuKFxueT0+e2lmKHRoaXMud3M9eS53ZWJTb2NrZXQsdGhpcy53cz09bnVsbCl0aHJvdyBjO3RoaXMud3MuYWNjZXB0KCksbyh0aGlzLndzLCEwKX0pLmNhdGNoKHk9Pnt0aGlzLmVtaXQoXG5cImVycm9yXCIsbmV3IEVycm9yKGBBbGwgYXR0ZW1wdHMgdG8gb3BlbiBhIFdlYlNvY2tldCB0byBjb25uZWN0IHRvIHRoZSBkYXRhYmFzZSBmYWlsZWQuIFBsZWFzZSByZWZlciBcXFxudG8gaHR0cHM6Ly9naXRodWIuY29tL25lb25kYXRhYmFzZS9zZXJ2ZXJsZXNzL2Jsb2IvbWFpbi9DT05GSUcubWQjd2Vic29ja2V0Y29uc3RydWN0b3ItdHlwZW9mLXdlYnNvY1xcXG5rZXQtLXVuZGVmaW5lZC4gRGV0YWlsczogJHt5fWApKSx0aGlzLmVtaXQoXCJjbG9zZVwiKX0pfX1hc3luYyBzdGFydFRscyh0KXtpZih0aGlzLnN1YnRscz09PXZvaWQgMCl0aHJvdyBuZXcgRXJyb3IoXG5cIkZvciBQb3N0Z3JlcyBTU0wgY29ubmVjdGlvbnMsIHlvdSBtdXN0IHNldCBgbmVvbkNvbmZpZy5zdWJ0bHNgIHRvIHRoZSBzdWJ0bHMgbGlicmFyeS4gU2VlIGh0dHBzOi8vZ1xcXG5pdGh1Yi5jb20vbmVvbmRhdGFiYXNlL3NlcnZlcmxlc3MvYmxvYi9tYWluL0NPTkZJRy5tZCBmb3IgbW9yZSBpbmZvcm1hdGlvbi5cIik7dGhpcy50bHNTdGF0ZT0xO2xldCBuPWF3YWl0IHRoaXMuXG5zdWJ0bHMuVHJ1c3RlZENlcnQuZGF0YWJhc2VGcm9tUEVNKHRoaXMucm9vdENlcnRzKSxpPW5ldyB0aGlzLnN1YnRscy5XZWJTb2NrZXRSZWFkUXVldWUodGhpcy53cykscz1pLlxucmVhZC5iaW5kKGkpLG89dGhpcy5yYXdXcml0ZS5iaW5kKHRoaXMpLHtyZWFkOnUsd3JpdGU6Y309YXdhaXQgdGhpcy5zdWJ0bHMuc3RhcnRUbHModCxuLHMsbyx7dXNlU05JOiF0aGlzLlxuZGlzYWJsZVNOSSxleHBlY3RQcmVEYXRhOnRoaXMucGlwZWxpbmVUTFM/bmV3IFVpbnQ4QXJyYXkoWzgzXSk6dm9pZCAwfSk7dGhpcy50bHNSZWFkPXUsdGhpcy50bHNXcml0ZT1cbmMsdGhpcy50bHNTdGF0ZT0yLHRoaXMuZW5jcnlwdGVkPSEwLHRoaXMuYXV0aG9yaXplZD0hMCx0aGlzLmVtaXQoXCJzZWN1cmVDb25uZWN0aW9uXCIsdGhpcyksdGhpcy50bHNSZWFkTG9vcCgpfWFzeW5jIHRsc1JlYWRMb29wKCl7XG5mb3IoOzspe2xldCB0PWF3YWl0IHRoaXMudGxzUmVhZCgpO2lmKHQ9PT12b2lkIDApYnJlYWs7e2xldCBuPWQuZnJvbSh0KTt0aGlzLmVtaXQoXCJkYXRhXCIsbil9fX1yYXdXcml0ZSh0KXtcbmlmKCF0aGlzLmNvYWxlc2NlV3JpdGVzKXt0aGlzLndzLnNlbmQodCk7cmV0dXJufWlmKHRoaXMud3JpdGVCdWZmZXI9PT12b2lkIDApdGhpcy53cml0ZUJ1ZmZlcj10LHNldFRpbWVvdXQoXG4oKT0+e3RoaXMud3Muc2VuZCh0aGlzLndyaXRlQnVmZmVyKSx0aGlzLndyaXRlQnVmZmVyPXZvaWQgMH0sMCk7ZWxzZXtsZXQgbj1uZXcgVWludDhBcnJheSh0aGlzLndyaXRlQnVmZmVyLlxubGVuZ3RoK3QubGVuZ3RoKTtuLnNldCh0aGlzLndyaXRlQnVmZmVyKSxuLnNldCh0LHRoaXMud3JpdGVCdWZmZXIubGVuZ3RoKSx0aGlzLndyaXRlQnVmZmVyPW59fXdyaXRlKHQsbj1cIlxcXG51dGY4XCIsaT1zPT57fSl7cmV0dXJuIHQubGVuZ3RoPT09MD8oaSgpLCEwKToodHlwZW9mIHQ9PVwic3RyaW5nXCImJih0PWQuZnJvbSh0LG4pKSx0aGlzLnRsc1N0YXRlPT09MD8odGhpcy5cbnJhd1dyaXRlKHQpLGkoKSk6dGhpcy50bHNTdGF0ZT09PTE/dGhpcy5vbmNlKFwic2VjdXJlQ29ubmVjdGlvblwiLCgpPT57dGhpcy53cml0ZSh0LG4saSl9KToodGhpcy50bHNXcml0ZShcbnQpLGkoKSksITApfWVuZCh0PWQuYWxsb2MoMCksbj1cInV0ZjhcIixpPSgpPT57fSl7cmV0dXJuIHRoaXMud3JpdGUodCxuLCgpPT57dGhpcy53cy5jbG9zZSgpLGkoKX0pLHRoaXN9ZGVzdHJveSgpe1xucmV0dXJuIHRoaXMuZGVzdHJveWVkPSEwLHRoaXMuZW5kKCl9fTthKFMsXCJTb2NrZXRcIiksRShTLFwiZGVmYXVsdHNcIix7cG9vbFF1ZXJ5VmlhRmV0Y2g6ITEsZmV0Y2hFbmRwb2ludDphKFxuKHQsbixpKT0+e2xldCBzO3JldHVybiBpPy5qd3RBdXRoP3M9dC5yZXBsYWNlKHBpLFwiYXBpYXV0aC5cIik6cz10LnJlcGxhY2UocGksXCJhcGkuXCIpLFwiaHR0cHM6Ly9cIitzK1wiL3NcXFxucWxcIn0sXCJmZXRjaEVuZHBvaW50XCIpLGZldGNoQ29ubmVjdGlvbkNhY2hlOiEwLGZldGNoRnVuY3Rpb246dm9pZCAwLHdlYlNvY2tldENvbnN0cnVjdG9yOnZvaWQgMCx3c1Byb3h5OmEoXG50PT50K1wiL3YyXCIsXCJ3c1Byb3h5XCIpLHVzZVNlY3VyZVdlYlNvY2tldDohMCxmb3JjZURpc2FibGVQZ1NTTDohMCxjb2FsZXNjZVdyaXRlczohMCxwaXBlbGluZUNvbm5lY3Q6XCJcXFxucGFzc3dvcmRcIixzdWJ0bHM6dm9pZCAwLHJvb3RDZXJ0czpcIlwiLHBpcGVsaW5lVExTOiExLGRpc2FibGVTTkk6ITF9KSxFKFMsXCJvcHRzXCIse30pO2dlPVN9KTt2YXIgbWk9e307bmUobWkse3BhcnNlOigpPT5WdH0pO2Z1bmN0aW9uIFZ0KHIsZT0hMSl7bGV0e3Byb3RvY29sOnR9PW5ldyBVUkwociksbj1cImh0dHA6XCIrci5zdWJzdHJpbmcoXG50Lmxlbmd0aCkse3VzZXJuYW1lOmkscGFzc3dvcmQ6cyxob3N0Om8saG9zdG5hbWU6dSxwb3J0OmMscGF0aG5hbWU6bCxzZWFyY2g6ZixzZWFyY2hQYXJhbXM6eSxoYXNoOmd9PW5ldyBVUkwoXG5uKTtzPWRlY29kZVVSSUNvbXBvbmVudChzKSxpPWRlY29kZVVSSUNvbXBvbmVudChpKSxsPWRlY29kZVVSSUNvbXBvbmVudChsKTtsZXQgQT1pK1wiOlwiK3MsQz1lP09iamVjdC5cbmZyb21FbnRyaWVzKHkuZW50cmllcygpKTpmO3JldHVybntocmVmOnIscHJvdG9jb2w6dCxhdXRoOkEsdXNlcm5hbWU6aSxwYXNzd29yZDpzLGhvc3Q6byxob3N0bmFtZTp1LHBvcnQ6YyxcbnBhdGhuYW1lOmwsc2VhcmNoOmYscXVlcnk6QyxoYXNoOmd9fXZhciB6dD16KCgpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO2EoVnQsXCJwYXJzZVwiKX0pO3ZhciBKdD1JKFNpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO1NpLnBhcnNlPWZ1bmN0aW9uKHIsZSl7cmV0dXJuIG5ldyBadChyLGUpLnBhcnNlKCl9O3ZhciB3dD1jbGFzcyB3dHtjb25zdHJ1Y3RvcihlLHQpe1xudGhpcy5zb3VyY2U9ZSx0aGlzLnRyYW5zZm9ybT10fHxFYSx0aGlzLnBvc2l0aW9uPTAsdGhpcy5lbnRyaWVzPVtdLHRoaXMucmVjb3JkZWQ9W10sdGhpcy5kaW1lbnNpb249MH1pc0VvZigpe1xucmV0dXJuIHRoaXMucG9zaXRpb24+PXRoaXMuc291cmNlLmxlbmd0aH1uZXh0Q2hhcmFjdGVyKCl7dmFyIGU9dGhpcy5zb3VyY2VbdGhpcy5wb3NpdGlvbisrXTtyZXR1cm4gZT09PVxuXCJcXFxcXCI/e3ZhbHVlOnRoaXMuc291cmNlW3RoaXMucG9zaXRpb24rK10sZXNjYXBlZDohMH06e3ZhbHVlOmUsZXNjYXBlZDohMX19cmVjb3JkKGUpe3RoaXMucmVjb3JkZWQucHVzaChcbmUpfW5ld0VudHJ5KGUpe3ZhciB0Oyh0aGlzLnJlY29yZGVkLmxlbmd0aD4wfHxlKSYmKHQ9dGhpcy5yZWNvcmRlZC5qb2luKFwiXCIpLHQ9PT1cIk5VTExcIiYmIWUmJih0PW51bGwpLFxudCE9PW51bGwmJih0PXRoaXMudHJhbnNmb3JtKHQpKSx0aGlzLmVudHJpZXMucHVzaCh0KSx0aGlzLnJlY29yZGVkPVtdKX1jb25zdW1lRGltZW5zaW9ucygpe2lmKHRoaXMuc291cmNlWzBdPT09XG5cIltcIilmb3IoOyF0aGlzLmlzRW9mKCk7KXt2YXIgZT10aGlzLm5leHRDaGFyYWN0ZXIoKTtpZihlLnZhbHVlPT09XCI9XCIpYnJlYWt9fXBhcnNlKGUpe3ZhciB0LG4saTtmb3IodGhpcy5cbmNvbnN1bWVEaW1lbnNpb25zKCk7IXRoaXMuaXNFb2YoKTspaWYodD10aGlzLm5leHRDaGFyYWN0ZXIoKSx0LnZhbHVlPT09XCJ7XCImJiFpKXRoaXMuZGltZW5zaW9uKyssdGhpcy5cbmRpbWVuc2lvbj4xJiYobj1uZXcgd3QodGhpcy5zb3VyY2Uuc3Vic3RyKHRoaXMucG9zaXRpb24tMSksdGhpcy50cmFuc2Zvcm0pLHRoaXMuZW50cmllcy5wdXNoKG4ucGFyc2UoXG4hMCkpLHRoaXMucG9zaXRpb24rPW4ucG9zaXRpb24tMik7ZWxzZSBpZih0LnZhbHVlPT09XCJ9XCImJiFpKXtpZih0aGlzLmRpbWVuc2lvbi0tLCF0aGlzLmRpbWVuc2lvbiYmKHRoaXMuXG5uZXdFbnRyeSgpLGUpKXJldHVybiB0aGlzLmVudHJpZXN9ZWxzZSB0LnZhbHVlPT09J1wiJyYmIXQuZXNjYXBlZD8oaSYmdGhpcy5uZXdFbnRyeSghMCksaT0haSk6dC52YWx1ZT09PVxuXCIsXCImJiFpP3RoaXMubmV3RW50cnkoKTp0aGlzLnJlY29yZCh0LnZhbHVlKTtpZih0aGlzLmRpbWVuc2lvbiE9PTApdGhyb3cgbmV3IEVycm9yKFwiYXJyYXkgZGltZW5zaW9uIFxcXG5ub3QgYmFsYW5jZWRcIik7cmV0dXJuIHRoaXMuZW50cmllc319O2Eod3QsXCJBcnJheVBhcnNlclwiKTt2YXIgWnQ9d3Q7ZnVuY3Rpb24gRWEocil7cmV0dXJuIHJ9YShFYSxcImlkZVxcXG5udGl0eVwiKX0pO3ZhciBYdD1JKChHbCxFaSk9PntwKCk7dmFyIEFhPUp0KCk7RWkuZXhwb3J0cz17Y3JlYXRlOmEoZnVuY3Rpb24ocixlKXtyZXR1cm57cGFyc2U6YShmdW5jdGlvbigpe3JldHVybiBBYS5cbnBhcnNlKHIsZSl9LFwicGFyc2VcIil9fSxcImNyZWF0ZVwiKX19KTt2YXIgX2k9SSgoS2wsQ2kpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciBDYT0vKFxcZHsxLH0pLShcXGR7Mn0pLShcXGR7Mn0pIChcXGR7Mn0pOihcXGR7Mn0pOihcXGR7Mn0pKFxcLlxcZHsxLH0pPy4qPyggQkMpPyQvLFxuX2E9L14oXFxkezEsfSktKFxcZHsyfSktKFxcZHsyfSkoIEJDKT8kLyxUYT0vKFtaKy1dKShcXGR7Mn0pPzo/KFxcZHsyfSk/Oj8oXFxkezJ9KT8vLElhPS9eLT9pbmZpbml0eSQvO0NpLlxuZXhwb3J0cz1hKGZ1bmN0aW9uKGUpe2lmKElhLnRlc3QoZSkpcmV0dXJuIE51bWJlcihlLnJlcGxhY2UoXCJpXCIsXCJJXCIpKTt2YXIgdD1DYS5leGVjKGUpO2lmKCF0KXJldHVybiBQYShcbmUpfHxudWxsO3ZhciBuPSEhdFs4XSxpPXBhcnNlSW50KHRbMV0sMTApO24mJihpPUFpKGkpKTt2YXIgcz1wYXJzZUludCh0WzJdLDEwKS0xLG89dFszXSx1PXBhcnNlSW50KHRbNF0sXG4xMCksYz1wYXJzZUludCh0WzVdLDEwKSxsPXBhcnNlSW50KHRbNl0sMTApLGY9dFs3XTtmPWY/MWUzKnBhcnNlRmxvYXQoZik6MDt2YXIgeSxnPVJhKGUpO3JldHVybiBnIT1udWxsP1xuKHk9bmV3IERhdGUoRGF0ZS5VVEMoaSxzLG8sdSxjLGwsZikpLGVyKGkpJiZ5LnNldFVUQ0Z1bGxZZWFyKGkpLGchPT0wJiZ5LnNldFRpbWUoeS5nZXRUaW1lKCktZykpOih5PVxubmV3IERhdGUoaSxzLG8sdSxjLGwsZiksZXIoaSkmJnkuc2V0RnVsbFllYXIoaSkpLHl9LFwicGFyc2VEYXRlXCIpO2Z1bmN0aW9uIFBhKHIpe3ZhciBlPV9hLmV4ZWMocik7aWYoZSl7XG52YXIgdD1wYXJzZUludChlWzFdLDEwKSxuPSEhZVs0XTtuJiYodD1BaSh0KSk7dmFyIGk9cGFyc2VJbnQoZVsyXSwxMCktMSxzPWVbM10sbz1uZXcgRGF0ZSh0LGkscyk7cmV0dXJuIGVyKFxudCkmJm8uc2V0RnVsbFllYXIodCksb319YShQYSxcImdldERhdGVcIik7ZnVuY3Rpb24gUmEocil7aWYoci5lbmRzV2l0aChcIiswMFwiKSlyZXR1cm4gMDt2YXIgZT1UYS5leGVjKHIuXG5zcGxpdChcIiBcIilbMV0pO2lmKGUpe3ZhciB0PWVbMV07aWYodD09PVwiWlwiKXJldHVybiAwO3ZhciBuPXQ9PT1cIi1cIj8tMToxLGk9cGFyc2VJbnQoZVsyXSwxMCkqMzYwMCtwYXJzZUludChcbmVbM118fDAsMTApKjYwK3BhcnNlSW50KGVbNF18fDAsMTApO3JldHVybiBpKm4qMWUzfX1hKFJhLFwidGltZVpvbmVPZmZzZXRcIik7ZnVuY3Rpb24gQWkocil7cmV0dXJuLShyLVxuMSl9YShBaSxcImJjWWVhclRvTmVnYXRpdmVZZWFyXCIpO2Z1bmN0aW9uIGVyKHIpe3JldHVybiByPj0wJiZyPDEwMH1hKGVyLFwiaXMwVG85OVwiKX0pO3ZhciBJaT1JKChKbCxUaSk9PntwKCk7VGkuZXhwb3J0cz1MYTt2YXIgQmE9T2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtmdW5jdGlvbiBMYShyKXtmb3IodmFyIGU9MTtlPFxuYXJndW1lbnRzLmxlbmd0aDtlKyspe3ZhciB0PWFyZ3VtZW50c1tlXTtmb3IodmFyIG4gaW4gdClCYS5jYWxsKHQsbikmJihyW25dPXRbbl0pfXJldHVybiByfWEoTGEsXCJleHRcXFxuZW5kXCIpfSk7dmFyIEJpPUkoKHRmLFJpKT0+e1widXNlIHN0cmljdFwiO3AoKTt2YXIgRmE9SWkoKTtSaS5leHBvcnRzPUZlO2Z1bmN0aW9uIEZlKHIpe2lmKCEodGhpcyBpbnN0YW5jZW9mIEZlKSlcbnJldHVybiBuZXcgRmUocik7RmEodGhpcywkYShyKSl9YShGZSxcIlBvc3RncmVzSW50ZXJ2YWxcIik7dmFyIGthPVtcInNlY29uZHNcIixcIm1pbnV0ZXNcIixcImhvdXJzXCIsXCJkYXlzXCIsXG5cIm1vbnRoc1wiLFwieWVhcnNcIl07RmUucHJvdG90eXBlLnRvUG9zdGdyZXM9ZnVuY3Rpb24oKXt2YXIgcj1rYS5maWx0ZXIodGhpcy5oYXNPd25Qcm9wZXJ0eSx0aGlzKTtyZXR1cm4gdGhpcy5cbm1pbGxpc2Vjb25kcyYmci5pbmRleE9mKFwic2Vjb25kc1wiKTwwJiZyLnB1c2goXCJzZWNvbmRzXCIpLHIubGVuZ3RoPT09MD9cIjBcIjpyLm1hcChmdW5jdGlvbihlKXt2YXIgdD10aGlzW2VdfHxcbjA7cmV0dXJuIGU9PT1cInNlY29uZHNcIiYmdGhpcy5taWxsaXNlY29uZHMmJih0PSh0K3RoaXMubWlsbGlzZWNvbmRzLzFlMykudG9GaXhlZCg2KS5yZXBsYWNlKC9cXC4/MCskLyxcblwiXCIpKSx0K1wiIFwiK2V9LHRoaXMpLmpvaW4oXCIgXCIpfTt2YXIgTWE9e3llYXJzOlwiWVwiLG1vbnRoczpcIk1cIixkYXlzOlwiRFwiLGhvdXJzOlwiSFwiLG1pbnV0ZXM6XCJNXCIsc2Vjb25kczpcIlxcXG5TXCJ9LFVhPVtcInllYXJzXCIsXCJtb250aHNcIixcImRheXNcIl0sRGE9W1wiaG91cnNcIixcIm1pbnV0ZXNcIixcInNlY29uZHNcIl07RmUucHJvdG90eXBlLnRvSVNPU3RyaW5nPUZlLnByb3RvdHlwZS5cbnRvSVNPPWZ1bmN0aW9uKCl7dmFyIHI9VWEubWFwKHQsdGhpcykuam9pbihcIlwiKSxlPURhLm1hcCh0LHRoaXMpLmpvaW4oXCJcIik7cmV0dXJuXCJQXCIrcitcIlRcIitlO2Z1bmN0aW9uIHQobil7XG52YXIgaT10aGlzW25dfHwwO3JldHVybiBuPT09XCJzZWNvbmRzXCImJnRoaXMubWlsbGlzZWNvbmRzJiYoaT0oaSt0aGlzLm1pbGxpc2Vjb25kcy8xZTMpLnRvRml4ZWQoNikucmVwbGFjZShcbi8wKyQvLFwiXCIpKSxpK01hW25dfX07dmFyIHRyPVwiKFsrLV0/XFxcXGQrKVwiLE9hPXRyK1wiXFxcXHMreWVhcnM/XCIscWE9dHIrXCJcXFxccyttb25zP1wiLFFhPXRyK1wiXFxcXHMrZGF5cz9cIixOYT1cIlxcXG4oWystXSk/KFtcXFxcZF0qKTooXFxcXGRcXFxcZCk6KFxcXFxkXFxcXGQpXFxcXC4/KFxcXFxkezEsNn0pP1wiLGphPW5ldyBSZWdFeHAoW09hLHFhLFFhLE5hXS5tYXAoZnVuY3Rpb24ocil7cmV0dXJuXCJcXFxuKFwiK3IrXCIpP1wifSkuam9pbihcIlxcXFxzKlwiKSksUGk9e3llYXJzOjIsbW9udGhzOjQsZGF5czo2LGhvdXJzOjksbWludXRlczoxMCxzZWNvbmRzOjExLG1pbGxpc2Vjb25kczoxMn0sXG5XYT1bXCJob3Vyc1wiLFwibWludXRlc1wiLFwic2Vjb25kc1wiLFwibWlsbGlzZWNvbmRzXCJdO2Z1bmN0aW9uIEhhKHIpe3ZhciBlPXIrXCIwMDAwMDBcIi5zbGljZShyLmxlbmd0aCk7cmV0dXJuIHBhcnNlSW50KFxuZSwxMCkvMWUzfWEoSGEsXCJwYXJzZU1pbGxpc2Vjb25kc1wiKTtmdW5jdGlvbiAkYShyKXtpZighcilyZXR1cm57fTt2YXIgZT1qYS5leGVjKHIpLHQ9ZVs4XT09PVwiLVwiO3JldHVybiBPYmplY3QuXG5rZXlzKFBpKS5yZWR1Y2UoZnVuY3Rpb24obixpKXt2YXIgcz1QaVtpXSxvPWVbc107cmV0dXJuIW98fChvPWk9PT1cIm1pbGxpc2Vjb25kc1wiP0hhKG8pOnBhcnNlSW50KG8sMTApLFxuIW8pfHwodCYmfldhLmluZGV4T2YoaSkmJihvKj0tMSksbltpXT1vKSxufSx7fSl9YSgkYSxcInBhcnNlXCIpfSk7dmFyIEZpPUkoKHNmLExpKT0+e1widXNlIHN0cmljdFwiO3AoKTtMaS5leHBvcnRzPWEoZnVuY3Rpb24oZSl7aWYoL15cXFxceC8udGVzdChlKSlyZXR1cm4gbmV3IGQoZS5zdWJzdHIoXG4yKSxcImhleFwiKTtmb3IodmFyIHQ9XCJcIixuPTA7bjxlLmxlbmd0aDspaWYoZVtuXSE9PVwiXFxcXFwiKXQrPWVbbl0sKytuO2Vsc2UgaWYoL1swLTddezN9Ly50ZXN0KGUuc3Vic3RyKG4rXG4xLDMpKSl0Kz1TdHJpbmcuZnJvbUNoYXJDb2RlKHBhcnNlSW50KGUuc3Vic3RyKG4rMSwzKSw4KSksbis9NDtlbHNle2Zvcih2YXIgaT0xO24raTxlLmxlbmd0aCYmZVtuK2ldPT09XG5cIlxcXFxcIjspaSsrO2Zvcih2YXIgcz0wO3M8TWF0aC5mbG9vcihpLzIpOysrcyl0Kz1cIlxcXFxcIjtuKz1NYXRoLmZsb29yKGkvMikqMn1yZXR1cm4gbmV3IGQodCxcImJpbmFyeVwiKX0sXCJcXFxucGFyc2VCeXRlYVwiKX0pO3ZhciBRaT1JKCh1ZixxaSk9PntwKCk7dmFyIFZlPUp0KCksemU9WHQoKSxidD1faSgpLE1pPUJpKCksVWk9RmkoKTtmdW5jdGlvbiB2dChyKXtyZXR1cm4gYShmdW5jdGlvbih0KXtcbnJldHVybiB0PT09bnVsbD90OnIodCl9LFwibnVsbEFsbG93ZWRcIil9YSh2dCxcImFsbG93TnVsbFwiKTtmdW5jdGlvbiBEaShyKXtyZXR1cm4gcj09PW51bGw/cjpyPT09XCJUUlVFXCJ8fFxucj09PVwidFwifHxyPT09XCJ0cnVlXCJ8fHI9PT1cInlcInx8cj09PVwieWVzXCJ8fHI9PT1cIm9uXCJ8fHI9PT1cIjFcIn1hKERpLFwicGFyc2VCb29sXCIpO2Z1bmN0aW9uIEdhKHIpe3JldHVybiByP1xuVmUucGFyc2UocixEaSk6bnVsbH1hKEdhLFwicGFyc2VCb29sQXJyYXlcIik7ZnVuY3Rpb24gVmEocil7cmV0dXJuIHBhcnNlSW50KHIsMTApfWEoVmEsXCJwYXJzZUJhc2VUZW5JblxcXG50XCIpO2Z1bmN0aW9uIHJyKHIpe3JldHVybiByP1ZlLnBhcnNlKHIsdnQoVmEpKTpudWxsfWEocnIsXCJwYXJzZUludGVnZXJBcnJheVwiKTtmdW5jdGlvbiB6YShyKXtyZXR1cm4gcj9cblZlLnBhcnNlKHIsdnQoZnVuY3Rpb24oZSl7cmV0dXJuIE9pKGUpLnRyaW0oKX0pKTpudWxsfWEoemEsXCJwYXJzZUJpZ0ludGVnZXJBcnJheVwiKTt2YXIgS2E9YShmdW5jdGlvbihyKXtcbmlmKCFyKXJldHVybiBudWxsO3ZhciBlPXplLmNyZWF0ZShyLGZ1bmN0aW9uKHQpe3JldHVybiB0IT09bnVsbCYmKHQ9b3IodCkpLHR9KTtyZXR1cm4gZS5wYXJzZSgpfSxcInBhXFxcbnJzZVBvaW50QXJyYXlcIiksbnI9YShmdW5jdGlvbihyKXtpZighcilyZXR1cm4gbnVsbDt2YXIgZT16ZS5jcmVhdGUocixmdW5jdGlvbih0KXtyZXR1cm4gdCE9PW51bGwmJih0PVxucGFyc2VGbG9hdCh0KSksdH0pO3JldHVybiBlLnBhcnNlKCl9LFwicGFyc2VGbG9hdEFycmF5XCIpLHRlPWEoZnVuY3Rpb24ocil7aWYoIXIpcmV0dXJuIG51bGw7dmFyIGU9emUuXG5jcmVhdGUocik7cmV0dXJuIGUucGFyc2UoKX0sXCJwYXJzZVN0cmluZ0FycmF5XCIpLGlyPWEoZnVuY3Rpb24ocil7aWYoIXIpcmV0dXJuIG51bGw7dmFyIGU9emUuY3JlYXRlKHIsXG5mdW5jdGlvbih0KXtyZXR1cm4gdCE9PW51bGwmJih0PWJ0KHQpKSx0fSk7cmV0dXJuIGUucGFyc2UoKX0sXCJwYXJzZURhdGVBcnJheVwiKSxZYT1hKGZ1bmN0aW9uKHIpe2lmKCFyKVxucmV0dXJuIG51bGw7dmFyIGU9emUuY3JlYXRlKHIsZnVuY3Rpb24odCl7cmV0dXJuIHQhPT1udWxsJiYodD1NaSh0KSksdH0pO3JldHVybiBlLnBhcnNlKCl9LFwicGFyc2VJbnRcXFxuZXJ2YWxBcnJheVwiKSxaYT1hKGZ1bmN0aW9uKHIpe3JldHVybiByP1ZlLnBhcnNlKHIsdnQoVWkpKTpudWxsfSxcInBhcnNlQnl0ZUFBcnJheVwiKSxzcj1hKGZ1bmN0aW9uKHIpe1xucmV0dXJuIHBhcnNlSW50KHIsMTApfSxcInBhcnNlSW50ZWdlclwiKSxPaT1hKGZ1bmN0aW9uKHIpe3ZhciBlPVN0cmluZyhyKTtyZXR1cm4vXlxcZCskLy50ZXN0KGUpP2U6cn0sXCJcXFxucGFyc2VCaWdJbnRlZ2VyXCIpLGtpPWEoZnVuY3Rpb24ocil7cmV0dXJuIHI/VmUucGFyc2Uocix2dChKU09OLnBhcnNlKSk6bnVsbH0sXCJwYXJzZUpzb25BcnJheVwiKSxvcj1hKFxuZnVuY3Rpb24ocil7cmV0dXJuIHJbMF0hPT1cIihcIj9udWxsOihyPXIuc3Vic3RyaW5nKDEsci5sZW5ndGgtMSkuc3BsaXQoXCIsXCIpLHt4OnBhcnNlRmxvYXQoclswXSkseTpwYXJzZUZsb2F0KFxuclsxXSl9KX0sXCJwYXJzZVBvaW50XCIpLEphPWEoZnVuY3Rpb24ocil7aWYoclswXSE9PVwiPFwiJiZyWzFdIT09XCIoXCIpcmV0dXJuIG51bGw7Zm9yKHZhciBlPVwiKFwiLHQ9XCJcIixuPSExLFxuaT0yO2k8ci5sZW5ndGgtMTtpKyspe2lmKG58fChlKz1yW2ldKSxyW2ldPT09XCIpXCIpe249ITA7Y29udGludWV9ZWxzZSBpZighbiljb250aW51ZTtyW2ldIT09XCIsXCImJih0Kz1cbnJbaV0pfXZhciBzPW9yKGUpO3JldHVybiBzLnJhZGl1cz1wYXJzZUZsb2F0KHQpLHN9LFwicGFyc2VDaXJjbGVcIiksWGE9YShmdW5jdGlvbihyKXtyKDIwLE9pKSxyKDIxLHNyKSxcbnIoMjMsc3IpLHIoMjYsc3IpLHIoNzAwLHBhcnNlRmxvYXQpLHIoNzAxLHBhcnNlRmxvYXQpLHIoMTYsRGkpLHIoMTA4MixidCkscigxMTE0LGJ0KSxyKDExODQsYnQpLHIoNjAwLFxub3IpLHIoNjUxLHRlKSxyKDcxOCxKYSkscigxZTMsR2EpLHIoMTAwMSxaYSkscigxMDA1LHJyKSxyKDEwMDcscnIpLHIoMTAyOCxycikscigxMDE2LHphKSxyKDEwMTcsS2EpLFxucigxMDIxLG5yKSxyKDEwMjIsbnIpLHIoMTIzMSxucikscigxMDE0LHRlKSxyKDEwMTUsdGUpLHIoMTAwOCx0ZSkscigxMDA5LHRlKSxyKDEwNDAsdGUpLHIoMTA0MSx0ZSkscihcbjExMTUsaXIpLHIoMTE4MixpcikscigxMTg1LGlyKSxyKDExODYsTWkpLHIoMTE4NyxZYSkscigxNyxVaSkscigxMTQsSlNPTi5wYXJzZS5iaW5kKEpTT04pKSxyKDM4MDIsSlNPTi5cbnBhcnNlLmJpbmQoSlNPTikpLHIoMTk5LGtpKSxyKDM4MDcsa2kpLHIoMzkwNyx0ZSkscigyOTUxLHRlKSxyKDc5MSx0ZSkscigxMTgzLHRlKSxyKDEyNzAsdGUpfSxcImluaXRcIik7XG5xaS5leHBvcnRzPXtpbml0OlhhfX0pO3ZhciBqaT1JKChmZixOaSk9PntcInVzZSBzdHJpY3RcIjtwKCk7dmFyIFk9MWU2O2Z1bmN0aW9uIGV1KHIpe3ZhciBlPXIucmVhZEludDMyQkUoMCksdD1yLnJlYWRVSW50MzJCRShcbjQpLG49XCJcIjtlPDAmJihlPX5lKyh0PT09MCksdD1+dCsxPj4+MCxuPVwiLVwiKTt2YXIgaT1cIlwiLHMsbyx1LGMsbCxmO3tpZihzPWUlWSxlPWUvWT4+PjAsbz00Mjk0OTY3Mjk2KnMrXG50LHQ9by9ZPj4+MCx1PVwiXCIrKG8tWSp0KSx0PT09MCYmZT09PTApcmV0dXJuIG4rdStpO2ZvcihjPVwiXCIsbD02LXUubGVuZ3RoLGY9MDtmPGw7ZisrKWMrPVwiMFwiO2k9Yyt1K2l9XG57aWYocz1lJVksZT1lL1k+Pj4wLG89NDI5NDk2NzI5NipzK3QsdD1vL1k+Pj4wLHU9XCJcIisoby1ZKnQpLHQ9PT0wJiZlPT09MClyZXR1cm4gbit1K2k7Zm9yKGM9XCJcIixsPTYtdS5cbmxlbmd0aCxmPTA7ZjxsO2YrKyljKz1cIjBcIjtpPWMrdStpfXtpZihzPWUlWSxlPWUvWT4+PjAsbz00Mjk0OTY3Mjk2KnMrdCx0PW8vWT4+PjAsdT1cIlwiKyhvLVkqdCksdD09PTAmJlxuZT09PTApcmV0dXJuIG4rdStpO2ZvcihjPVwiXCIsbD02LXUubGVuZ3RoLGY9MDtmPGw7ZisrKWMrPVwiMFwiO2k9Yyt1K2l9cmV0dXJuIHM9ZSVZLG89NDI5NDk2NzI5NipzK3QsdT1cblwiXCIrbyVZLG4rdStpfWEoZXUsXCJyZWFkSW50OFwiKTtOaS5leHBvcnRzPWV1fSk7dmFyIFZpPUkoKGRmLEdpKT0+e3AoKTt2YXIgdHU9amkoKSxGPWEoZnVuY3Rpb24ocixlLHQsbixpKXt0PXR8fDAsbj1ufHwhMSxpPWl8fGZ1bmN0aW9uKEEsQyxRKXtyZXR1cm4gQSpcbk1hdGgucG93KDIsUSkrQ307dmFyIHM9dD4+MyxvPWEoZnVuY3Rpb24oQSl7cmV0dXJuIG4/fkEmMjU1OkF9LFwiaW52XCIpLHU9MjU1LGM9OC10JTg7ZTxjJiYodT0yNTU8PDgtZSZcbjI1NSxjPWUpLHQmJih1PXU+PnQlOCk7dmFyIGw9MDt0JTgrZT49OCYmKGw9aSgwLG8ocltzXSkmdSxjKSk7Zm9yKHZhciBmPWUrdD4+Myx5PXMrMTt5PGY7eSsrKWw9aShsLG8oXG5yW3ldKSw4KTt2YXIgZz0oZSt0KSU4O3JldHVybiBnPjAmJihsPWkobCxvKHJbZl0pPj44LWcsZykpLGx9LFwicGFyc2VCaXRzXCIpLCRpPWEoZnVuY3Rpb24ocixlLHQpe3ZhciBuPU1hdGguXG5wb3coMix0LTEpLTEsaT1GKHIsMSkscz1GKHIsdCwxKTtpZihzPT09MClyZXR1cm4gMDt2YXIgbz0xLHU9YShmdW5jdGlvbihsLGYseSl7bD09PTAmJihsPTEpO2Zvcih2YXIgZz0xO2c8PVxueTtnKyspby89MiwoZiYxPDx5LWcpPjAmJihsKz1vKTtyZXR1cm4gbH0sXCJwYXJzZVByZWNpc2lvbkJpdHNcIiksYz1GKHIsZSx0KzEsITEsdSk7cmV0dXJuIHM9PU1hdGgucG93KFxuMix0KzEpLTE/Yz09PTA/aT09PTA/MS8wOi0xLzA6TmFOOihpPT09MD8xOi0xKSpNYXRoLnBvdygyLHMtbikqY30sXCJwYXJzZUZsb2F0RnJvbUJpdHNcIikscnU9YShmdW5jdGlvbihyKXtcbnJldHVybiBGKHIsMSk9PTE/LTEqKEYociwxNSwxLCEwKSsxKTpGKHIsMTUsMSl9LFwicGFyc2VJbnQxNlwiKSxXaT1hKGZ1bmN0aW9uKHIpe3JldHVybiBGKHIsMSk9PTE/LTEqKEYoXG5yLDMxLDEsITApKzEpOkYociwzMSwxKX0sXCJwYXJzZUludDMyXCIpLG51PWEoZnVuY3Rpb24ocil7cmV0dXJuICRpKHIsMjMsOCl9LFwicGFyc2VGbG9hdDMyXCIpLGl1PWEoZnVuY3Rpb24ocil7XG5yZXR1cm4gJGkociw1MiwxMSl9LFwicGFyc2VGbG9hdDY0XCIpLHN1PWEoZnVuY3Rpb24ocil7dmFyIGU9RihyLDE2LDMyKTtpZihlPT00OTE1MilyZXR1cm4gTmFOO2Zvcih2YXIgdD1NYXRoLlxucG93KDFlNCxGKHIsMTYsMTYpKSxuPTAsaT1bXSxzPUYociwxNiksbz0wO288cztvKyspbis9RihyLDE2LDY0KzE2Km8pKnQsdC89MWU0O3ZhciB1PU1hdGgucG93KDEwLEYocixcbjE2LDQ4KSk7cmV0dXJuKGU9PT0wPzE6LTEpKk1hdGgucm91bmQobip1KS91fSxcInBhcnNlTnVtZXJpY1wiKSxIaT1hKGZ1bmN0aW9uKHIsZSl7dmFyIHQ9RihlLDEpLG49RihlLFxuNjMsMSksaT1uZXcgRGF0ZSgodD09PTA/MTotMSkqbi8xZTMrOTQ2Njg0OGU1KTtyZXR1cm4gcnx8aS5zZXRUaW1lKGkuZ2V0VGltZSgpK2kuZ2V0VGltZXpvbmVPZmZzZXQoKSpcbjZlNCksaS51c2VjPW4lMWUzLGkuZ2V0TWljcm9TZWNvbmRzPWZ1bmN0aW9uKCl7cmV0dXJuIHRoaXMudXNlY30saS5zZXRNaWNyb1NlY29uZHM9ZnVuY3Rpb24ocyl7dGhpcy5cbnVzZWM9c30saS5nZXRVVENNaWNyb1NlY29uZHM9ZnVuY3Rpb24oKXtyZXR1cm4gdGhpcy51c2VjfSxpfSxcInBhcnNlRGF0ZVwiKSxLZT1hKGZ1bmN0aW9uKHIpe2Zvcih2YXIgZT1GKFxuciwzMiksdD1GKHIsMzIsMzIpLG49RihyLDMyLDY0KSxpPTk2LHM9W10sbz0wO288ZTtvKyspc1tvXT1GKHIsMzIsaSksaSs9MzIsaSs9MzI7dmFyIHU9YShmdW5jdGlvbihsKXtcbnZhciBmPUYociwzMixpKTtpZihpKz0zMixmPT00Mjk0OTY3Mjk1KXJldHVybiBudWxsO3ZhciB5O2lmKGw9PTIzfHxsPT0yMClyZXR1cm4geT1GKHIsZio4LGkpLGkrPWYqOCxcbnk7aWYobD09MjUpcmV0dXJuIHk9ci50b1N0cmluZyh0aGlzLmVuY29kaW5nLGk+PjMsKGkrPWY8PDMpPj4zKSx5O2NvbnNvbGUubG9nKFwiRVJST1I6IEVsZW1lbnRUeXBlIG5vXFxcbnQgaW1wbGVtZW50ZWQ6IFwiK2wpfSxcInBhcnNlRWxlbWVudFwiKSxjPWEoZnVuY3Rpb24obCxmKXt2YXIgeT1bXSxnO2lmKGwubGVuZ3RoPjEpe3ZhciBBPWwuc2hpZnQoKTtmb3IoZz1cbjA7ZzxBO2crKyl5W2ddPWMobCxmKTtsLnVuc2hpZnQoQSl9ZWxzZSBmb3IoZz0wO2c8bFswXTtnKyspeVtnXT11KGYpO3JldHVybiB5fSxcInBhcnNlXCIpO3JldHVybiBjKHMsbil9LFxuXCJwYXJzZUFycmF5XCIpLG91PWEoZnVuY3Rpb24ocil7cmV0dXJuIHIudG9TdHJpbmcoXCJ1dGY4XCIpfSxcInBhcnNlVGV4dFwiKSxhdT1hKGZ1bmN0aW9uKHIpe3JldHVybiByPT09bnVsbD9cbm51bGw6RihyLDgpPjB9LFwicGFyc2VCb29sXCIpLHV1PWEoZnVuY3Rpb24ocil7cigyMCx0dSkscigyMSxydSkscigyMyxXaSkscigyNixXaSkscigxNzAwLHN1KSxyKDcwMCxudSksXG5yKDcwMSxpdSkscigxNixhdSkscigxMTE0LEhpLmJpbmQobnVsbCwhMSkpLHIoMTE4NCxIaS5iaW5kKG51bGwsITApKSxyKDFlMyxLZSkscigxMDA3LEtlKSxyKDEwMTYsS2UpLFxucigxMDA4LEtlKSxyKDEwMDksS2UpLHIoMjUsb3UpfSxcImluaXRcIik7R2kuZXhwb3J0cz17aW5pdDp1dX19KTt2YXIgS2k9SSgoZ2YsemkpPT57cCgpO3ppLmV4cG9ydHM9e0JPT0w6MTYsQllURUE6MTcsQ0hBUjoxOCxJTlQ4OjIwLElOVDI6MjEsSU5UNDoyMyxSRUdQUk9DOjI0LFRFWFQ6MjUsXG5PSUQ6MjYsVElEOjI3LFhJRDoyOCxDSUQ6MjksSlNPTjoxMTQsWE1MOjE0MixQR19OT0RFX1RSRUU6MTk0LFNNR1I6MjEwLFBBVEg6NjAyLFBPTFlHT046NjA0LENJRFI6NjUwLFxuRkxPQVQ0OjcwMCxGTE9BVDg6NzAxLEFCU1RJTUU6NzAyLFJFTFRJTUU6NzAzLFRJTlRFUlZBTDo3MDQsQ0lSQ0xFOjcxOCxNQUNBRERSODo3NzQsTU9ORVk6NzkwLE1BQ0FERFI6ODI5LFxuSU5FVDo4NjksQUNMSVRFTToxMDMzLEJQQ0hBUjoxMDQyLFZBUkNIQVI6MTA0MyxEQVRFOjEwODIsVElNRToxMDgzLFRJTUVTVEFNUDoxMTE0LFRJTUVTVEFNUFRaOjExODQsSU5URVJWQUw6MTE4NixcblRJTUVUWjoxMjY2LEJJVDoxNTYwLFZBUkJJVDoxNTYyLE5VTUVSSUM6MTcwMCxSRUZDVVJTT1I6MTc5MCxSRUdQUk9DRURVUkU6MjIwMixSRUdPUEVSOjIyMDMsUkVHT1BFUkFUT1I6MjIwNCxcblJFR0NMQVNTOjIyMDUsUkVHVFlQRToyMjA2LFVVSUQ6Mjk1MCxUWElEX1NOQVBTSE9UOjI5NzAsUEdfTFNOOjMyMjAsUEdfTkRJU1RJTkNUOjMzNjEsUEdfREVQRU5ERU5DSUVTOjM0MDIsXG5UU1ZFQ1RPUjozNjE0LFRTUVVFUlk6MzYxNSxHVFNWRUNUT1I6MzY0MixSRUdDT05GSUc6MzczNCxSRUdESUNUSU9OQVJZOjM3NjksSlNPTkI6MzgwMixSRUdOQU1FU1BBQ0U6NDA4OSxcblJFR1JPTEU6NDA5Nn19KTt2YXIgSmU9SShaZT0+e3AoKTt2YXIgY3U9UWkoKSxsdT1WaSgpLGZ1PVh0KCksaHU9S2koKTtaZS5nZXRUeXBlUGFyc2VyPXB1O1plLnNldFR5cGVQYXJzZXI9ZHU7WmUuYXJyYXlQYXJzZXI9XG5mdTtaZS5idWlsdGlucz1odTt2YXIgWWU9e3RleHQ6e30sYmluYXJ5Ont9fTtmdW5jdGlvbiBZaShyKXtyZXR1cm4gU3RyaW5nKHIpfWEoWWksXCJub1BhcnNlXCIpO2Z1bmN0aW9uIHB1KHIsZSl7XG5yZXR1cm4gZT1lfHxcInRleHRcIixZZVtlXSYmWWVbZV1bcl18fFlpfWEocHUsXCJnZXRUeXBlUGFyc2VyXCIpO2Z1bmN0aW9uIGR1KHIsZSx0KXt0eXBlb2YgZT09XCJmdW5jdGlvblwiJiZcbih0PWUsZT1cInRleHRcIiksWWVbZV1bcl09dH1hKGR1LFwic2V0VHlwZVBhcnNlclwiKTtjdS5pbml0KGZ1bmN0aW9uKHIsZSl7WWUudGV4dFtyXT1lfSk7bHUuaW5pdChmdW5jdGlvbihyLGUpe1xuWWUuYmluYXJ5W3JdPWV9KX0pO3ZhciBTdD1JKChTZixaaSk9PntcInVzZSBzdHJpY3RcIjtwKCk7dmFyIHl1PUplKCk7ZnVuY3Rpb24geHQocil7dGhpcy5fdHlwZXM9cnx8eXUsdGhpcy50ZXh0PXt9LHRoaXMuYmluYXJ5PVxue319YSh4dCxcIlR5cGVPdmVycmlkZXNcIik7eHQucHJvdG90eXBlLmdldE92ZXJyaWRlcz1mdW5jdGlvbihyKXtzd2l0Y2gocil7Y2FzZVwidGV4dFwiOnJldHVybiB0aGlzLnRleHQ7Y2FzZVwiXFxcbmJpbmFyeVwiOnJldHVybiB0aGlzLmJpbmFyeTtkZWZhdWx0OnJldHVybnt9fX07eHQucHJvdG90eXBlLnNldFR5cGVQYXJzZXI9ZnVuY3Rpb24ocixlLHQpe3R5cGVvZiBlPT1cIlxcXG5mdW5jdGlvblwiJiYodD1lLGU9XCJ0ZXh0XCIpLHRoaXMuZ2V0T3ZlcnJpZGVzKGUpW3JdPXR9O3h0LnByb3RvdHlwZS5nZXRUeXBlUGFyc2VyPWZ1bmN0aW9uKHIsZSl7cmV0dXJuIGU9XG5lfHxcInRleHRcIix0aGlzLmdldE92ZXJyaWRlcyhlKVtyXXx8dGhpcy5fdHlwZXMuZ2V0VHlwZVBhcnNlcihyLGUpfTtaaS5leHBvcnRzPXh0fSk7ZnVuY3Rpb24gWGUocil7bGV0IGU9MTc3OTAzMzcwMyx0PTMxNDQxMzQyNzcsbj0xMDEzOTA0MjQyLGk9Mjc3MzQ4MDc2MixzPTEzNTk4OTMxMTksbz0yNjAwODIyOTI0LHU9NTI4NzM0NjM1LFxuYz0xNTQxNDU5MjI1LGw9MCxmPTAseT1bMTExNjM1MjQwOCwxODk5NDQ3NDQxLDMwNDkzMjM0NzEsMzkyMTAwOTU3Myw5NjE5ODcxNjMsMTUwODk3MDk5MywyNDUzNjM1NzQ4LFxuMjg3MDc2MzIyMSwzNjI0MzgxMDgwLDMxMDU5ODQwMSw2MDcyMjUyNzgsMTQyNjg4MTk4NywxOTI1MDc4Mzg4LDIxNjIwNzgyMDYsMjYxNDg4ODEwMywzMjQ4MjIyNTgwLDM4MzUzOTA0MDEsXG40MDIyMjI0Nzc0LDI2NDM0NzA3OCw2MDQ4MDc2MjgsNzcwMjU1OTgzLDEyNDkxNTAxMjIsMTU1NTA4MTY5MiwxOTk2MDY0OTg2LDI1NTQyMjA4ODIsMjgyMTgzNDM0OSwyOTUyOTk2ODA4LFxuMzIxMDMxMzY3MSwzMzM2NTcxODkxLDM1ODQ1Mjg3MTEsMTEzOTI2OTkzLDMzODI0MTg5NSw2NjYzMDcyMDUsNzczNTI5OTEyLDEyOTQ3NTczNzIsMTM5NjE4MjI5MSwxNjk1MTgzNzAwLFxuMTk4NjY2MTA1MSwyMTc3MDI2MzUwLDI0NTY5NTYwMzcsMjczMDQ4NTkyMSwyODIwMzAyNDExLDMyNTk3MzA4MDAsMzM0NTc2NDc3MSwzNTE2MDY1ODE3LDM2MDAzNTI4MDQsNDA5NDU3MTkwOSxcbjI3NTQyMzM0NCw0MzAyMjc3MzQsNTA2OTQ4NjE2LDY1OTA2MDU1Niw4ODM5OTc4NzcsOTU4MTM5NTcxLDEzMjI4MjIyMTgsMTUzNzAwMjA2MywxNzQ3ODczNzc5LDE5NTU1NjIyMjIsXG4yMDI0MTA0ODE1LDIyMjc3MzA0NTIsMjM2MTg1MjQyNCwyNDI4NDM2NDc0LDI3NTY3MzQxODcsMzIwNDAzMTQ3OSwzMzI5MzI1Mjk4XSxnPWEoKF8seCk9Pl8+Pj54fF88PDMyLVxueCxcInJyb3RcIiksQT1uZXcgVWludDMyQXJyYXkoNjQpLEM9bmV3IFVpbnQ4QXJyYXkoNjQpLFE9YSgoKT0+e2ZvcihsZXQgQj0wLCQ9MDtCPDE2O0IrKywkKz00KUFbQl09Q1skXTw8XG4yNHxDWyQrMV08PDE2fENbJCsyXTw8OHxDWyQrM107Zm9yKGxldCBCPTE2O0I8NjQ7QisrKXtsZXQgJD1nKEFbQi0xNV0sNyleZyhBW0ItMTVdLDE4KV5BW0ItMTVdPj4+MyxmZT1nKFxuQVtCLTJdLDE3KV5nKEFbQi0yXSwxOSleQVtCLTJdPj4+MTA7QVtCXT1BW0ItMTZdKyQrQVtCLTddK2ZlfDB9bGV0IF89ZSx4PXQsSD1uLGxlPWksTj1zLGllPW8sc2U9dSxvZT1jO1xuZm9yKGxldCBCPTA7Qjw2NDtCKyspe2xldCAkPWcoTiw2KV5nKE4sMTEpXmcoTiwyNSksZmU9TiZpZV5+TiZzZSxDZT1vZSskK2ZlK3lbQl0rQVtCXXwwLGhlPWcoXywyKV5nKFxuXywxMyleZyhfLDIyKSxfZT1fJnheXyZIXngmSCxhZT1oZStfZXwwO29lPXNlLHNlPWllLGllPU4sTj1sZStDZXwwLGxlPUgsSD14LHg9XyxfPUNlK2FlfDB9ZT1lK198MCx0PVxudCt4fDAsbj1uK0h8MCxpPWkrbGV8MCxzPXMrTnwwLG89bytpZXwwLHU9dStzZXwwLGM9YytvZXwwLGY9MH0sXCJwcm9jZXNzXCIpLFA9YShfPT57dHlwZW9mIF89PVwic3RyaW5nXCImJlxuKF89bmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKF8pKTtmb3IobGV0IHg9MDt4PF8ubGVuZ3RoO3grKylDW2YrK109X1t4XSxmPT09NjQmJlEoKTtsKz1fLmxlbmd0aH0sXCJhZGRcIiksXG5MPWEoKCk9PntpZihDW2YrK109MTI4LGY9PTY0JiZRKCksZis4PjY0KXtmb3IoO2Y8NjQ7KUNbZisrXT0wO1EoKX1mb3IoO2Y8NTg7KUNbZisrXT0wO2xldCBfPWwqODtDW2YrK109XG5fLzEwOTk1MTE2Mjc3NzYmMjU1LENbZisrXT1fLzQyOTQ5NjcyOTYmMjU1LENbZisrXT1fPj4+MjQsQ1tmKytdPV8+Pj4xNiYyNTUsQ1tmKytdPV8+Pj44JjI1NSxDW2YrK109XG5fJjI1NSxRKCk7bGV0IHg9bmV3IFVpbnQ4QXJyYXkoMzIpO3JldHVybiB4WzBdPWU+Pj4yNCx4WzFdPWU+Pj4xNiYyNTUseFsyXT1lPj4+OCYyNTUseFszXT1lJjI1NSx4WzRdPVxudD4+PjI0LHhbNV09dD4+PjE2JjI1NSx4WzZdPXQ+Pj44JjI1NSx4WzddPXQmMjU1LHhbOF09bj4+PjI0LHhbOV09bj4+PjE2JjI1NSx4WzEwXT1uPj4+OCYyNTUseFsxMV09biZcbjI1NSx4WzEyXT1pPj4+MjQseFsxM109aT4+PjE2JjI1NSx4WzE0XT1pPj4+OCYyNTUseFsxNV09aSYyNTUseFsxNl09cz4+PjI0LHhbMTddPXM+Pj4xNiYyNTUseFsxOF09cz4+PlxuOCYyNTUseFsxOV09cyYyNTUseFsyMF09bz4+PjI0LHhbMjFdPW8+Pj4xNiYyNTUseFsyMl09bz4+PjgmMjU1LHhbMjNdPW8mMjU1LHhbMjRdPXU+Pj4yNCx4WzI1XT11Pj4+MTYmXG4yNTUseFsyNl09dT4+PjgmMjU1LHhbMjddPXUmMjU1LHhbMjhdPWM+Pj4yNCx4WzI5XT1jPj4+MTYmMjU1LHhbMzBdPWM+Pj44JjI1NSx4WzMxXT1jJjI1NSx4fSxcImRpZ2VzdFwiKTtcbnJldHVybiByPT09dm9pZCAwP3thZGQ6UCxkaWdlc3Q6TH06KFAociksTCgpKX12YXIgSmk9eigoKT0+e1widXNlIHN0cmljdFwiO3AoKTthKFhlLFwic2hhMjU2XCIpfSk7dmFyIFUsZXQsWGk9eigoKT0+e1widXNlIHN0cmljdFwiO3AoKTtVPWNsYXNzIFV7Y29uc3RydWN0b3IoKXtFKHRoaXMsXCJfZGF0YUxlbmd0aFwiLDApO0UodGhpcyxcIl9idWZmZXJMXFxcbmVuZ3RoXCIsMCk7RSh0aGlzLFwiX3N0YXRlXCIsbmV3IEludDMyQXJyYXkoNCkpO0UodGhpcyxcIl9idWZmZXJcIixuZXcgQXJyYXlCdWZmZXIoNjgpKTtFKHRoaXMsXCJfYnVmZmVyOFwiKTtcbkUodGhpcyxcIl9idWZmZXIzMlwiKTt0aGlzLl9idWZmZXI4PW5ldyBVaW50OEFycmF5KHRoaXMuX2J1ZmZlciwwLDY4KSx0aGlzLl9idWZmZXIzMj1uZXcgVWludDMyQXJyYXkodGhpcy5cbl9idWZmZXIsMCwxNyksdGhpcy5zdGFydCgpfXN0YXRpYyBoYXNoQnl0ZUFycmF5KGUsdD0hMSl7cmV0dXJuIHRoaXMub25lUGFzc0hhc2hlci5zdGFydCgpLmFwcGVuZEJ5dGVBcnJheShcbmUpLmVuZCh0KX1zdGF0aWMgaGFzaFN0cihlLHQ9ITEpe3JldHVybiB0aGlzLm9uZVBhc3NIYXNoZXIuc3RhcnQoKS5hcHBlbmRTdHIoZSkuZW5kKHQpfXN0YXRpYyBoYXNoQXNjaWlTdHIoZSx0PSExKXtcbnJldHVybiB0aGlzLm9uZVBhc3NIYXNoZXIuc3RhcnQoKS5hcHBlbmRBc2NpaVN0cihlKS5lbmQodCl9c3RhdGljIF9oZXgoZSl7bGV0IHQ9VS5oZXhDaGFycyxuPVUuaGV4T3V0LFxuaSxzLG8sdTtmb3IodT0wO3U8NDt1Kz0xKWZvcihzPXUqOCxpPWVbdV0sbz0wO288ODtvKz0yKW5bcysxK29dPXQuY2hhckF0KGkmMTUpLGk+Pj49NCxuW3MrMCtvXT10LmNoYXJBdChcbmkmMTUpLGk+Pj49NDtyZXR1cm4gbi5qb2luKFwiXCIpfXN0YXRpYyBfbWQ1Y3ljbGUoZSx0KXtsZXQgbj1lWzBdLGk9ZVsxXSxzPWVbMl0sbz1lWzNdO24rPShpJnN8fmkmbykrdFswXS1cbjY4MDg3NjkzNnwwLG49KG48PDd8bj4+PjI1KStpfDAsbys9KG4maXx+biZzKSt0WzFdLTM4OTU2NDU4NnwwLG89KG88PDEyfG8+Pj4yMCkrbnwwLHMrPShvJm58fm8maSkrdFsyXStcbjYwNjEwNTgxOXwwLHM9KHM8PDE3fHM+Pj4xNSkrb3wwLGkrPShzJm98fnMmbikrdFszXS0xMDQ0NTI1MzMwfDAsaT0oaTw8MjJ8aT4+PjEwKStzfDAsbis9KGkmc3x+aSZvKStcbnRbNF0tMTc2NDE4ODk3fDAsbj0objw8N3xuPj4+MjUpK2l8MCxvKz0obiZpfH5uJnMpK3RbNV0rMTIwMDA4MDQyNnwwLG89KG88PDEyfG8+Pj4yMCkrbnwwLHMrPShvJm58fm8mXG5pKSt0WzZdLTE0NzMyMzEzNDF8MCxzPShzPDwxN3xzPj4+MTUpK298MCxpKz0ocyZvfH5zJm4pK3RbN10tNDU3MDU5ODN8MCxpPShpPDwyMnxpPj4+MTApK3N8MCxuKz0oaSZzfFxufmkmbykrdFs4XSsxNzcwMDM1NDE2fDAsbj0objw8N3xuPj4+MjUpK2l8MCxvKz0obiZpfH5uJnMpK3RbOV0tMTk1ODQxNDQxN3wwLG89KG88PDEyfG8+Pj4yMCkrbnwwLHMrPVxuKG8mbnx+byZpKSt0WzEwXS00MjA2M3wwLHM9KHM8PDE3fHM+Pj4xNSkrb3wwLGkrPShzJm98fnMmbikrdFsxMV0tMTk5MDQwNDE2MnwwLGk9KGk8PDIyfGk+Pj4xMCkrc3wwLFxubis9KGkmc3x+aSZvKSt0WzEyXSsxODA0NjAzNjgyfDAsbj0objw8N3xuPj4+MjUpK2l8MCxvKz0obiZpfH5uJnMpK3RbMTNdLTQwMzQxMTAxfDAsbz0obzw8MTJ8bz4+PjIwKStcbm58MCxzKz0obyZufH5vJmkpK3RbMTRdLTE1MDIwMDIyOTB8MCxzPShzPDwxN3xzPj4+MTUpK298MCxpKz0ocyZvfH5zJm4pK3RbMTVdKzEyMzY1MzUzMjl8MCxpPShpPDwyMnxcbmk+Pj4xMCkrc3wwLG4rPShpJm98cyZ+bykrdFsxXS0xNjU3OTY1MTB8MCxuPShuPDw1fG4+Pj4yNykraXwwLG8rPShuJnN8aSZ+cykrdFs2XS0xMDY5NTAxNjMyfDAsbz0obzw8XG45fG8+Pj4yMykrbnwwLHMrPShvJml8biZ+aSkrdFsxMV0rNjQzNzE3NzEzfDAscz0oczw8MTR8cz4+PjE4KStvfDAsaSs9KHMmbnxvJn5uKSt0WzBdLTM3Mzg5NzMwMnwwLGk9XG4oaTw8MjB8aT4+PjEyKStzfDAsbis9KGkmb3xzJn5vKSt0WzVdLTcwMTU1ODY5MXwwLG49KG48PDV8bj4+PjI3KStpfDAsbys9KG4mc3xpJn5zKSt0WzEwXSszODAxNjA4M3wwLFxubz0obzw8OXxvPj4+MjMpK258MCxzKz0obyZpfG4mfmkpK3RbMTVdLTY2MDQ3ODMzNXwwLHM9KHM8PDE0fHM+Pj4xOCkrb3wwLGkrPShzJm58byZ+bikrdFs0XS00MDU1Mzc4NDh8XG4wLGk9KGk8PDIwfGk+Pj4xMikrc3wwLG4rPShpJm98cyZ+bykrdFs5XSs1Njg0NDY0Mzh8MCxuPShuPDw1fG4+Pj4yNykraXwwLG8rPShuJnN8aSZ+cykrdFsxNF0tMTAxOTgwMzY5MHxcbjAsbz0obzw8OXxvPj4+MjMpK258MCxzKz0obyZpfG4mfmkpK3RbM10tMTg3MzYzOTYxfDAscz0oczw8MTR8cz4+PjE4KStvfDAsaSs9KHMmbnxvJn5uKSt0WzhdKzExNjM1MzE1MDF8XG4wLGk9KGk8PDIwfGk+Pj4xMikrc3wwLG4rPShpJm98cyZ+bykrdFsxM10tMTQ0NDY4MTQ2N3wwLG49KG48PDV8bj4+PjI3KStpfDAsbys9KG4mc3xpJn5zKSt0WzJdLTUxNDAzNzg0fFxuMCxvPShvPDw5fG8+Pj4yMykrbnwwLHMrPShvJml8biZ+aSkrdFs3XSsxNzM1MzI4NDczfDAscz0oczw8MTR8cz4+PjE4KStvfDAsaSs9KHMmbnxvJn5uKSt0WzEyXS0xOTI2NjA3NzM0fFxuMCxpPShpPDwyMHxpPj4+MTIpK3N8MCxuKz0oaV5zXm8pK3RbNV0tMzc4NTU4fDAsbj0objw8NHxuPj4+MjgpK2l8MCxvKz0obl5pXnMpK3RbOF0tMjAyMjU3NDQ2M3wwLG89KG88PFxuMTF8bz4+PjIxKStufDAscys9KG9ebl5pKSt0WzExXSsxODM5MDMwNTYyfDAscz0oczw8MTZ8cz4+PjE2KStvfDAsaSs9KHNeb15uKSt0WzE0XS0zNTMwOTU1NnwwLGk9KGk8PFxuMjN8aT4+PjkpK3N8MCxuKz0oaV5zXm8pK3RbMV0tMTUzMDk5MjA2MHwwLG49KG48PDR8bj4+PjI4KStpfDAsbys9KG5eaV5zKSt0WzRdKzEyNzI4OTMzNTN8MCxvPShvPDwxMXxcbm8+Pj4yMSkrbnwwLHMrPShvXm5eaSkrdFs3XS0xNTU0OTc2MzJ8MCxzPShzPDwxNnxzPj4+MTYpK298MCxpKz0oc15vXm4pK3RbMTBdLTEwOTQ3MzA2NDB8MCxpPShpPDwyM3xcbmk+Pj45KStzfDAsbis9KGlec15vKSt0WzEzXSs2ODEyNzkxNzR8MCxuPShuPDw0fG4+Pj4yOCkraXwwLG8rPShuXmlecykrdFswXS0zNTg1MzcyMjJ8MCxvPShvPDwxMXxvPj4+XG4yMSkrbnwwLHMrPShvXm5eaSkrdFszXS03MjI1MjE5Nzl8MCxzPShzPDwxNnxzPj4+MTYpK298MCxpKz0oc15vXm4pK3RbNl0rNzYwMjkxODl8MCxpPShpPDwyM3xpPj4+OSkrXG5zfDAsbis9KGlec15vKSt0WzldLTY0MDM2NDQ4N3wwLG49KG48PDR8bj4+PjI4KStpfDAsbys9KG5eaV5zKSt0WzEyXS00MjE4MTU4MzV8MCxvPShvPDwxMXxvPj4+MjEpK258XG4wLHMrPShvXm5eaSkrdFsxNV0rNTMwNzQyNTIwfDAscz0oczw8MTZ8cz4+PjE2KStvfDAsaSs9KHNeb15uKSt0WzJdLTk5NTMzODY1MXwwLGk9KGk8PDIzfGk+Pj45KStzfDAsXG5uKz0oc14oaXx+bykpK3RbMF0tMTk4NjMwODQ0fDAsbj0objw8NnxuPj4+MjYpK2l8MCxvKz0oaV4obnx+cykpK3RbN10rMTEyNjg5MTQxNXwwLG89KG88PDEwfG8+Pj4yMikrXG5ufDAscys9KG5eKG98fmkpKSt0WzE0XS0xNDE2MzU0OTA1fDAscz0oczw8MTV8cz4+PjE3KStvfDAsaSs9KG9eKHN8fm4pKSt0WzVdLTU3NDM0MDU1fDAsaT0oaTw8MjF8aT4+PlxuMTEpK3N8MCxuKz0oc14oaXx+bykpK3RbMTJdKzE3MDA0ODU1NzF8MCxuPShuPDw2fG4+Pj4yNikraXwwLG8rPShpXihufH5zKSkrdFszXS0xODk0OTg2NjA2fDAsbz0obzw8MTB8XG5vPj4+MjIpK258MCxzKz0obl4ob3x+aSkpK3RbMTBdLTEwNTE1MjN8MCxzPShzPDwxNXxzPj4+MTcpK298MCxpKz0ob14oc3x+bikpK3RbMV0tMjA1NDkyMjc5OXwwLGk9KGk8PFxuMjF8aT4+PjExKStzfDAsbis9KHNeKGl8fm8pKSt0WzhdKzE4NzMzMTMzNTl8MCxuPShuPDw2fG4+Pj4yNikraXwwLG8rPShpXihufH5zKSkrdFsxNV0tMzA2MTE3NDR8MCxvPVxuKG88PDEwfG8+Pj4yMikrbnwwLHMrPShuXihvfH5pKSkrdFs2XS0xNTYwMTk4MzgwfDAscz0oczw8MTV8cz4+PjE3KStvfDAsaSs9KG9eKHN8fm4pKSt0WzEzXSsxMzA5MTUxNjQ5fFxuMCxpPShpPDwyMXxpPj4+MTEpK3N8MCxuKz0oc14oaXx+bykpK3RbNF0tMTQ1NTIzMDcwfDAsbj0objw8NnxuPj4+MjYpK2l8MCxvKz0oaV4obnx+cykpK3RbMTFdLTExMjAyMTAzNzl8XG4wLG89KG88PDEwfG8+Pj4yMikrbnwwLHMrPShuXihvfH5pKSkrdFsyXSs3MTg3ODcyNTl8MCxzPShzPDwxNXxzPj4+MTcpK298MCxpKz0ob14oc3x+bikpK3RbOV0tMzQzNDg1NTUxfFxuMCxpPShpPDwyMXxpPj4+MTEpK3N8MCxlWzBdPW4rZVswXXwwLGVbMV09aStlWzFdfDAsZVsyXT1zK2VbMl18MCxlWzNdPW8rZVszXXwwfXN0YXJ0KCl7cmV0dXJuIHRoaXMuX2RhdGFMZW5ndGg9XG4wLHRoaXMuX2J1ZmZlckxlbmd0aD0wLHRoaXMuX3N0YXRlLnNldChVLnN0YXRlSWRlbnRpdHkpLHRoaXN9YXBwZW5kU3RyKGUpe2xldCB0PXRoaXMuX2J1ZmZlcjgsbj10aGlzLlxuX2J1ZmZlcjMyLGk9dGhpcy5fYnVmZmVyTGVuZ3RoLHMsbztmb3Iobz0wO288ZS5sZW5ndGg7bys9MSl7aWYocz1lLmNoYXJDb2RlQXQobyksczwxMjgpdFtpKytdPXM7ZWxzZSBpZihzPFxuMjA0OCl0W2krK109KHM+Pj42KSsxOTIsdFtpKytdPXMmNjN8MTI4O2Vsc2UgaWYoczw1NTI5Nnx8cz41NjMxOSl0W2krK109KHM+Pj4xMikrMjI0LHRbaSsrXT1zPj4+NiY2M3xcbjEyOCx0W2krK109cyY2M3wxMjg7ZWxzZXtpZihzPShzLTU1Mjk2KSoxMDI0KyhlLmNoYXJDb2RlQXQoKytvKS01NjMyMCkrNjU1MzYscz4xMTE0MTExKXRocm93IG5ldyBFcnJvcihcblwiVW5pY29kZSBzdGFuZGFyZCBzdXBwb3J0cyBjb2RlIHBvaW50cyB1cCB0byBVKzEwRkZGRlwiKTt0W2krK109KHM+Pj4xOCkrMjQwLHRbaSsrXT1zPj4+MTImNjN8MTI4LHRbaSsrXT1cbnM+Pj42JjYzfDEyOCx0W2krK109cyY2M3wxMjh9aT49NjQmJih0aGlzLl9kYXRhTGVuZ3RoKz02NCxVLl9tZDVjeWNsZSh0aGlzLl9zdGF0ZSxuKSxpLT02NCxuWzBdPW5bMTZdKX1cbnJldHVybiB0aGlzLl9idWZmZXJMZW5ndGg9aSx0aGlzfWFwcGVuZEFzY2lpU3RyKGUpe2xldCB0PXRoaXMuX2J1ZmZlcjgsbj10aGlzLl9idWZmZXIzMixpPXRoaXMuX2J1ZmZlckxlbmd0aCxcbnMsbz0wO2Zvcig7Oyl7Zm9yKHM9TWF0aC5taW4oZS5sZW5ndGgtbyw2NC1pKTtzLS07KXRbaSsrXT1lLmNoYXJDb2RlQXQobysrKTtpZihpPDY0KWJyZWFrO3RoaXMuX2RhdGFMZW5ndGgrPVxuNjQsVS5fbWQ1Y3ljbGUodGhpcy5fc3RhdGUsbiksaT0wfXJldHVybiB0aGlzLl9idWZmZXJMZW5ndGg9aSx0aGlzfWFwcGVuZEJ5dGVBcnJheShlKXtsZXQgdD10aGlzLl9idWZmZXI4LFxubj10aGlzLl9idWZmZXIzMixpPXRoaXMuX2J1ZmZlckxlbmd0aCxzLG89MDtmb3IoOzspe2ZvcihzPU1hdGgubWluKGUubGVuZ3RoLW8sNjQtaSk7cy0tOyl0W2krK109ZVtvKytdO1xuaWYoaTw2NClicmVhazt0aGlzLl9kYXRhTGVuZ3RoKz02NCxVLl9tZDVjeWNsZSh0aGlzLl9zdGF0ZSxuKSxpPTB9cmV0dXJuIHRoaXMuX2J1ZmZlckxlbmd0aD1pLHRoaXN9Z2V0U3RhdGUoKXtcbmxldCBlPXRoaXMuX3N0YXRlO3JldHVybntidWZmZXI6U3RyaW5nLmZyb21DaGFyQ29kZS5hcHBseShudWxsLEFycmF5LmZyb20odGhpcy5fYnVmZmVyOCkpLGJ1Zmxlbjp0aGlzLlxuX2J1ZmZlckxlbmd0aCxsZW5ndGg6dGhpcy5fZGF0YUxlbmd0aCxzdGF0ZTpbZVswXSxlWzFdLGVbMl0sZVszXV19fXNldFN0YXRlKGUpe2xldCB0PWUuYnVmZmVyLG49ZS5zdGF0ZSxcbmk9dGhpcy5fc3RhdGUscztmb3IodGhpcy5fZGF0YUxlbmd0aD1lLmxlbmd0aCx0aGlzLl9idWZmZXJMZW5ndGg9ZS5idWZsZW4saVswXT1uWzBdLGlbMV09blsxXSxpWzJdPW5bMl0sXG5pWzNdPW5bM10scz0wO3M8dC5sZW5ndGg7cys9MSl0aGlzLl9idWZmZXI4W3NdPXQuY2hhckNvZGVBdChzKX1lbmQoZT0hMSl7bGV0IHQ9dGhpcy5fYnVmZmVyTGVuZ3RoLG49dGhpcy5cbl9idWZmZXI4LGk9dGhpcy5fYnVmZmVyMzIscz0odD4+MikrMTt0aGlzLl9kYXRhTGVuZ3RoKz10O2xldCBvPXRoaXMuX2RhdGFMZW5ndGgqODtpZihuW3RdPTEyOCxuW3QrMV09XG5uW3QrMl09blt0KzNdPTAsaS5zZXQoVS5idWZmZXIzMklkZW50aXR5LnN1YmFycmF5KHMpLHMpLHQ+NTUmJihVLl9tZDVjeWNsZSh0aGlzLl9zdGF0ZSxpKSxpLnNldChVLmJ1ZmZlcjMySWRlbnRpdHkpKSxcbm88PTQyOTQ5NjcyOTUpaVsxNF09bztlbHNle2xldCB1PW8udG9TdHJpbmcoMTYpLm1hdGNoKC8oLio/KSguezAsOH0pJC8pO2lmKHU9PT1udWxsKXJldHVybjtsZXQgYz1wYXJzZUludChcbnVbMl0sMTYpLGw9cGFyc2VJbnQodVsxXSwxNil8fDA7aVsxNF09YyxpWzE1XT1sfXJldHVybiBVLl9tZDVjeWNsZSh0aGlzLl9zdGF0ZSxpKSxlP3RoaXMuX3N0YXRlOlUuX2hleChcbnRoaXMuX3N0YXRlKX19O2EoVSxcIk1kNVwiKSxFKFUsXCJzdGF0ZUlkZW50aXR5XCIsbmV3IEludDMyQXJyYXkoWzE3MzI1ODQxOTMsLTI3MTczMzg3OSwtMTczMjU4NDE5NCwyNzE3MzM4NzhdKSksXG5FKFUsXCJidWZmZXIzMklkZW50aXR5XCIsbmV3IEludDMyQXJyYXkoWzAsMCwwLDAsMCwwLDAsMCwwLDAsMCwwLDAsMCwwLDBdKSksRShVLFwiaGV4Q2hhcnNcIixcIjAxMjM0NTY3ODlcXFxuYWJjZGVmXCIpLEUoVSxcImhleE91dFwiLFtdKSxFKFUsXCJvbmVQYXNzSGFzaGVyXCIsbmV3IFUpO2V0PVV9KTt2YXIgYXI9e307bmUoYXIse2NyZWF0ZUhhc2g6KCk9Pmd1LGNyZWF0ZUhtYWM6KCk9Pnd1LHJhbmRvbUJ5dGVzOigpPT5tdX0pO2Z1bmN0aW9uIG11KHIpe3JldHVybiBjcnlwdG8uXG5nZXRSYW5kb21WYWx1ZXMoZC5hbGxvYyhyKSl9ZnVuY3Rpb24gZ3Uocil7aWYocj09PVwic2hhMjU2XCIpcmV0dXJue3VwZGF0ZTphKGZ1bmN0aW9uKGUpe3JldHVybntkaWdlc3Q6YShcbmZ1bmN0aW9uKCl7cmV0dXJuIGQuZnJvbShYZShlKSl9LFwiZGlnZXN0XCIpfX0sXCJ1cGRhdGVcIil9O2lmKHI9PT1cIm1kNVwiKXJldHVybnt1cGRhdGU6YShmdW5jdGlvbihlKXtyZXR1cm57XG5kaWdlc3Q6YShmdW5jdGlvbigpe3JldHVybiB0eXBlb2YgZT09XCJzdHJpbmdcIj9ldC5oYXNoU3RyKGUpOmV0Lmhhc2hCeXRlQXJyYXkoZSl9LFwiZGlnZXN0XCIpfX0sXCJ1cGRhdGVcIil9O1xudGhyb3cgbmV3IEVycm9yKGBIYXNoIHR5cGUgJyR7cn0nIG5vdCBzdXBwb3J0ZWRgKX1mdW5jdGlvbiB3dShyLGUpe2lmKHIhPT1cInNoYTI1NlwiKXRocm93IG5ldyBFcnJvcihgXFxcbk9ubHkgc2hhMjU2IGlzIHN1cHBvcnRlZCAocmVxdWVzdGVkOiAnJHtyfScpYCk7cmV0dXJue3VwZGF0ZTphKGZ1bmN0aW9uKHQpe3JldHVybntkaWdlc3Q6YShmdW5jdGlvbigpe1xudHlwZW9mIGU9PVwic3RyaW5nXCImJihlPW5ldyBUZXh0RW5jb2RlcigpLmVuY29kZShlKSksdHlwZW9mIHQ9PVwic3RyaW5nXCImJih0PW5ldyBUZXh0RW5jb2RlcigpLmVuY29kZShcbnQpKTtsZXQgbj1lLmxlbmd0aDtpZihuPjY0KWU9WGUoZSk7ZWxzZSBpZihuPDY0KXtsZXQgYz1uZXcgVWludDhBcnJheSg2NCk7Yy5zZXQoZSksZT1jfWxldCBpPW5ldyBVaW50OEFycmF5KFxuNjQpLHM9bmV3IFVpbnQ4QXJyYXkoNjQpO2ZvcihsZXQgYz0wO2M8NjQ7YysrKWlbY109NTReZVtjXSxzW2NdPTkyXmVbY107bGV0IG89bmV3IFVpbnQ4QXJyYXkodC5sZW5ndGgrXG42NCk7by5zZXQoaSwwKSxvLnNldCh0LDY0KTtsZXQgdT1uZXcgVWludDhBcnJheSg5Nik7cmV0dXJuIHUuc2V0KHMsMCksdS5zZXQoWGUobyksNjQpLGQuZnJvbShYZSh1KSl9LFxuXCJkaWdlc3RcIil9fSxcInVwZGF0ZVwiKX19dmFyIHVyPXooKCk9PntcInVzZSBzdHJpY3RcIjtwKCk7SmkoKTtYaSgpO2EobXUsXCJyYW5kb21CeXRlc1wiKTthKGd1LFwiY3JlYXRlSGFzaFwiKTtcbmEod3UsXCJjcmVhdGVIbWFjXCIpfSk7dmFyIHR0PUkoKE1mLGNyKT0+e1widXNlIHN0cmljdFwiO3AoKTtjci5leHBvcnRzPXtob3N0OlwibG9jYWxob3N0XCIsdXNlcjptLnBsYXRmb3JtPT09XCJ3aW4zMlwiP20uZW52LlVTRVJOQU1FOlxubS5lbnYuVVNFUixkYXRhYmFzZTp2b2lkIDAscGFzc3dvcmQ6bnVsbCxjb25uZWN0aW9uU3RyaW5nOnZvaWQgMCxwb3J0OjU0MzIscm93czowLGJpbmFyeTohMSxtYXg6MTAsaWRsZVRpbWVvdXRNaWxsaXM6M2U0LFxuY2xpZW50X2VuY29kaW5nOlwiXCIsc3NsOiExLGFwcGxpY2F0aW9uX25hbWU6dm9pZCAwLGZhbGxiYWNrX2FwcGxpY2F0aW9uX25hbWU6dm9pZCAwLG9wdGlvbnM6dm9pZCAwLHBhcnNlSW5wdXREYXRlc0FzVVRDOiExLFxuc3RhdGVtZW50X3RpbWVvdXQ6ITEsbG9ja190aW1lb3V0OiExLGlkbGVfaW5fdHJhbnNhY3Rpb25fc2Vzc2lvbl90aW1lb3V0OiExLHF1ZXJ5X3RpbWVvdXQ6ITEsY29ubmVjdF90aW1lb3V0OjAsXG5rZWVwYWxpdmVzOjEsa2VlcGFsaXZlc19pZGxlOjB9O3ZhciBrZT1KZSgpLGJ1PWtlLmdldFR5cGVQYXJzZXIoMjAsXCJ0ZXh0XCIpLHZ1PWtlLmdldFR5cGVQYXJzZXIoMTAxNixcblwidGV4dFwiKTtjci5leHBvcnRzLl9fZGVmaW5lU2V0dGVyX18oXCJwYXJzZUludDhcIixmdW5jdGlvbihyKXtrZS5zZXRUeXBlUGFyc2VyKDIwLFwidGV4dFwiLHI/a2UuZ2V0VHlwZVBhcnNlcihcbjIzLFwidGV4dFwiKTpidSksa2Uuc2V0VHlwZVBhcnNlcigxMDE2LFwidGV4dFwiLHI/a2UuZ2V0VHlwZVBhcnNlcigxMDA3LFwidGV4dFwiKTp2dSl9KX0pO3ZhciBydD1JKChEZix0cyk9PntcInVzZSBzdHJpY3RcIjtwKCk7dmFyIHh1PSh1cigpLEQoYXIpKSxTdT10dCgpO2Z1bmN0aW9uIEV1KHIpe3ZhciBlPXIucmVwbGFjZSgvXFxcXC9nLFxuXCJcXFxcXFxcXFwiKS5yZXBsYWNlKC9cIi9nLCdcXFxcXCInKTtyZXR1cm4nXCInK2UrJ1wiJ31hKEV1LFwiZXNjYXBlRWxlbWVudFwiKTtmdW5jdGlvbiBlcyhyKXtmb3IodmFyIGU9XCJ7XCIsdD0wO3Q8XG5yLmxlbmd0aDt0KyspdD4wJiYoZT1lK1wiLFwiKSxyW3RdPT09bnVsbHx8dHlwZW9mIHJbdF0+XCJ1XCI/ZT1lK1wiTlVMTFwiOkFycmF5LmlzQXJyYXkoclt0XSk/ZT1lK2VzKHJbdF0pOlxuclt0XWluc3RhbmNlb2YgZD9lKz1cIlxcXFxcXFxceFwiK3JbdF0udG9TdHJpbmcoXCJoZXhcIik6ZSs9RXUoRXQoclt0XSkpO3JldHVybiBlPWUrXCJ9XCIsZX1hKGVzLFwiYXJyYXlTdHJpbmdcIik7XG52YXIgRXQ9YShmdW5jdGlvbihyLGUpe2lmKHI9PW51bGwpcmV0dXJuIG51bGw7aWYociBpbnN0YW5jZW9mIGQpcmV0dXJuIHI7aWYoQXJyYXlCdWZmZXIuaXNWaWV3KHIpKXt2YXIgdD1kLlxuZnJvbShyLmJ1ZmZlcixyLmJ5dGVPZmZzZXQsci5ieXRlTGVuZ3RoKTtyZXR1cm4gdC5sZW5ndGg9PT1yLmJ5dGVMZW5ndGg/dDp0LnNsaWNlKHIuYnl0ZU9mZnNldCxyLmJ5dGVPZmZzZXQrXG5yLmJ5dGVMZW5ndGgpfXJldHVybiByIGluc3RhbmNlb2YgRGF0ZT9TdS5wYXJzZUlucHV0RGF0ZXNBc1VUQz9fdShyKTpDdShyKTpBcnJheS5pc0FycmF5KHIpP2VzKHIpOnR5cGVvZiByPT1cblwib2JqZWN0XCI/QXUocixlKTpyLnRvU3RyaW5nKCl9LFwicHJlcGFyZVZhbHVlXCIpO2Z1bmN0aW9uIEF1KHIsZSl7aWYociYmdHlwZW9mIHIudG9Qb3N0Z3Jlcz09XCJmdW5jdGlvblwiKXtcbmlmKGU9ZXx8W10sZS5pbmRleE9mKHIpIT09LTEpdGhyb3cgbmV3IEVycm9yKCdjaXJjdWxhciByZWZlcmVuY2UgZGV0ZWN0ZWQgd2hpbGUgcHJlcGFyaW5nIFwiJytyKydcIiBmb1xcXG5yIHF1ZXJ5Jyk7cmV0dXJuIGUucHVzaChyKSxFdChyLnRvUG9zdGdyZXMoRXQpLGUpfXJldHVybiBKU09OLnN0cmluZ2lmeShyKX1hKEF1LFwicHJlcGFyZU9iamVjdFwiKTtmdW5jdGlvbiBXKHIsZSl7XG5mb3Iocj1cIlwiK3I7ci5sZW5ndGg8ZTspcj1cIjBcIityO3JldHVybiByfWEoVyxcInBhZFwiKTtmdW5jdGlvbiBDdShyKXt2YXIgZT0tci5nZXRUaW1lem9uZU9mZnNldCgpLHQ9ci5nZXRGdWxsWWVhcigpLFxubj10PDE7biYmKHQ9TWF0aC5hYnModCkrMSk7dmFyIGk9Vyh0LDQpK1wiLVwiK1coci5nZXRNb250aCgpKzEsMikrXCItXCIrVyhyLmdldERhdGUoKSwyKStcIlRcIitXKHIuZ2V0SG91cnMoKSxcbjIpK1wiOlwiK1coci5nZXRNaW51dGVzKCksMikrXCI6XCIrVyhyLmdldFNlY29uZHMoKSwyKStcIi5cIitXKHIuZ2V0TWlsbGlzZWNvbmRzKCksMyk7cmV0dXJuIGU8MD8oaSs9XCItXCIsZSo9XG4tMSk6aSs9XCIrXCIsaSs9VyhNYXRoLmZsb29yKGUvNjApLDIpK1wiOlwiK1coZSU2MCwyKSxuJiYoaSs9XCIgQkNcIiksaX1hKEN1LFwiZGF0ZVRvU3RyaW5nXCIpO2Z1bmN0aW9uIF91KHIpe1xudmFyIGU9ci5nZXRVVENGdWxsWWVhcigpLHQ9ZTwxO3QmJihlPU1hdGguYWJzKGUpKzEpO3ZhciBuPVcoZSw0KStcIi1cIitXKHIuZ2V0VVRDTW9udGgoKSsxLDIpK1wiLVwiK1coci5cbmdldFVUQ0RhdGUoKSwyKStcIlRcIitXKHIuZ2V0VVRDSG91cnMoKSwyKStcIjpcIitXKHIuZ2V0VVRDTWludXRlcygpLDIpK1wiOlwiK1coci5nZXRVVENTZWNvbmRzKCksMikrXCIuXCIrVyhcbnIuZ2V0VVRDTWlsbGlzZWNvbmRzKCksMyk7cmV0dXJuIG4rPVwiKzAwOjAwXCIsdCYmKG4rPVwiIEJDXCIpLG59YShfdSxcImRhdGVUb1N0cmluZ1VUQ1wiKTtmdW5jdGlvbiBUdShyLGUsdCl7XG5yZXR1cm4gcj10eXBlb2Ygcj09XCJzdHJpbmdcIj97dGV4dDpyfTpyLGUmJih0eXBlb2YgZT09XCJmdW5jdGlvblwiP3IuY2FsbGJhY2s9ZTpyLnZhbHVlcz1lKSx0JiYoci5jYWxsYmFjaz1cbnQpLHJ9YShUdSxcIm5vcm1hbGl6ZVF1ZXJ5Q29uZmlnXCIpO3ZhciBscj1hKGZ1bmN0aW9uKHIpe3JldHVybiB4dS5jcmVhdGVIYXNoKFwibWQ1XCIpLnVwZGF0ZShyLFwidXRmLThcIikuXG5kaWdlc3QoXCJoZXhcIil9LFwibWQ1XCIpLEl1PWEoZnVuY3Rpb24ocixlLHQpe3ZhciBuPWxyKGUrciksaT1scihkLmNvbmNhdChbZC5mcm9tKG4pLHRdKSk7cmV0dXJuXCJtZDVcIitpfSxcblwicG9zdGdyZXNNZDVQYXNzd29yZEhhc2hcIik7dHMuZXhwb3J0cz17cHJlcGFyZVZhbHVlOmEoZnVuY3Rpb24oZSl7cmV0dXJuIEV0KGUpfSxcInByZXBhcmVWYWx1ZVdyYXBwZXJcIiksXG5ub3JtYWxpemVRdWVyeUNvbmZpZzpUdSxwb3N0Z3Jlc01kNVBhc3N3b3JkSGFzaDpJdSxtZDU6bHJ9fSk7dmFyIG50PXt9O25lKG50LHtkZWZhdWx0OigpPT5MdX0pO3ZhciBMdSxpdD16KCgpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO0x1PXt9fSk7dmFyIGhzPUkoKHpmLGZzKT0+e1widXNlIHN0cmljdFwiO3AoKTt2YXIgaHI9KHVyKCksRChhcikpO2Z1bmN0aW9uIEZ1KHIpe2lmKHIuaW5kZXhPZihcIlNDUkFNLVNIQS0yNTZcIik9PT1cbi0xKXRocm93IG5ldyBFcnJvcihcIlNBU0w6IE9ubHkgbWVjaGFuaXNtIFNDUkFNLVNIQS0yNTYgaXMgY3VycmVudGx5IHN1cHBvcnRlZFwiKTtsZXQgZT1oci5yYW5kb21CeXRlcyhcbjE4KS50b1N0cmluZyhcImJhc2U2NFwiKTtyZXR1cm57bWVjaGFuaXNtOlwiU0NSQU0tU0hBLTI1NlwiLGNsaWVudE5vbmNlOmUscmVzcG9uc2U6XCJuLCxuPSoscj1cIitlLG1lc3NhZ2U6XCJcXFxuU0FTTEluaXRpYWxSZXNwb25zZVwifX1hKEZ1LFwic3RhcnRTZXNzaW9uXCIpO2Z1bmN0aW9uIGt1KHIsZSx0KXtpZihyLm1lc3NhZ2UhPT1cIlNBU0xJbml0aWFsUmVzcG9uc2VcIil0aHJvdyBuZXcgRXJyb3IoXG5cIlNBU0w6IExhc3QgbWVzc2FnZSB3YXMgbm90IFNBU0xJbml0aWFsUmVzcG9uc2VcIik7aWYodHlwZW9mIGUhPVwic3RyaW5nXCIpdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU1cXFxuLVNFUlZFUi1GSVJTVC1NRVNTQUdFOiBjbGllbnQgcGFzc3dvcmQgbXVzdCBiZSBhIHN0cmluZ1wiKTtpZih0eXBlb2YgdCE9XCJzdHJpbmdcIil0aHJvdyBuZXcgRXJyb3IoXCJTQVNcXFxuTDogU0NSQU0tU0VSVkVSLUZJUlNULU1FU1NBR0U6IHNlcnZlckRhdGEgbXVzdCBiZSBhIHN0cmluZ1wiKTtsZXQgbj1EdSh0KTtpZihuLm5vbmNlLnN0YXJ0c1dpdGgoci5jbGllbnROb25jZSkpe1xuaWYobi5ub25jZS5sZW5ndGg9PT1yLmNsaWVudE5vbmNlLmxlbmd0aCl0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBTQ1JBTS1TRVJWRVItRklSU1QtTUVTU0FHRTogc2VydmVyIG5cXFxub25jZSBpcyB0b28gc2hvcnRcIil9ZWxzZSB0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBTQ1JBTS1TRVJWRVItRklSU1QtTUVTU0FHRTogc2VydmVyIG5vbmNlIGRvZXMgbm90IHN0XFxcbmFydCB3aXRoIGNsaWVudCBub25jZVwiKTt2YXIgaT1kLmZyb20obi5zYWx0LFwiYmFzZTY0XCIpLHM9UXUoZSxpLG4uaXRlcmF0aW9uKSxvPU1lKHMsXCJDbGllbnQgS2V5XCIpLHU9cXUoXG5vKSxjPVwibj0qLHI9XCIrci5jbGllbnROb25jZSxsPVwicj1cIituLm5vbmNlK1wiLHM9XCIrbi5zYWx0K1wiLGk9XCIrbi5pdGVyYXRpb24sZj1cImM9Yml3cyxyPVwiK24ubm9uY2UseT1jK1xuXCIsXCIrbCtcIixcIitmLGc9TWUodSx5KSxBPWxzKG8sZyksQz1BLnRvU3RyaW5nKFwiYmFzZTY0XCIpLFE9TWUocyxcIlNlcnZlciBLZXlcIiksUD1NZShRLHkpO3IubWVzc2FnZT1cIlNBU1xcXG5MUmVzcG9uc2VcIixyLnNlcnZlclNpZ25hdHVyZT1QLnRvU3RyaW5nKFwiYmFzZTY0XCIpLHIucmVzcG9uc2U9ZitcIixwPVwiK0N9YShrdSxcImNvbnRpbnVlU2Vzc2lvblwiKTtmdW5jdGlvbiBNdShyLGUpe1xuaWYoci5tZXNzYWdlIT09XCJTQVNMUmVzcG9uc2VcIil0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBMYXN0IG1lc3NhZ2Ugd2FzIG5vdCBTQVNMUmVzcG9uc2VcIik7aWYodHlwZW9mIGUhPVxuXCJzdHJpbmdcIil0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBTQ1JBTS1TRVJWRVItRklOQUwtTUVTU0FHRTogc2VydmVyRGF0YSBtdXN0IGJlIGEgc3RyaW5nXCIpO2xldHtzZXJ2ZXJTaWduYXR1cmU6dH09T3UoXG5lKTtpZih0IT09ci5zZXJ2ZXJTaWduYXR1cmUpdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVkVSLUZJTkFMLU1FU1NBR0U6IHNlcnZlciBzaWduYXR1cmUgZG9lc1xcXG4gbm90IG1hdGNoXCIpfWEoTXUsXCJmaW5hbGl6ZVNlc3Npb25cIik7ZnVuY3Rpb24gVXUocil7aWYodHlwZW9mIHIhPVwic3RyaW5nXCIpdGhyb3cgbmV3IFR5cGVFcnJvcihcIlNBU0w6XFxcbiB0ZXh0IG11c3QgYmUgYSBzdHJpbmdcIik7cmV0dXJuIHIuc3BsaXQoXCJcIikubWFwKChlLHQpPT5yLmNoYXJDb2RlQXQodCkpLmV2ZXJ5KGU9PmU+PTMzJiZlPD00M3x8ZT49NDUmJlxuZTw9MTI2KX1hKFV1LFwiaXNQcmludGFibGVDaGFyc1wiKTtmdW5jdGlvbiB1cyhyKXtyZXR1cm4vXig/OlthLXpBLVowLTkrL117NH0pKig/OlthLXpBLVowLTkrL117Mn09PXxbYS16QS1aMC05Ky9dezN9PSk/JC8uXG50ZXN0KHIpfWEodXMsXCJpc0Jhc2U2NFwiKTtmdW5jdGlvbiBjcyhyKXtpZih0eXBlb2YgciE9XCJzdHJpbmdcIil0aHJvdyBuZXcgVHlwZUVycm9yKFwiU0FTTDogYXR0cmlidXRlIHBcXFxuYWlycyB0ZXh0IG11c3QgYmUgYSBzdHJpbmdcIik7cmV0dXJuIG5ldyBNYXAoci5zcGxpdChcIixcIikubWFwKGU9PntpZighL14uPS8udGVzdChlKSl0aHJvdyBuZXcgRXJyb3IoXCJcXFxuU0FTTDogSW52YWxpZCBhdHRyaWJ1dGUgcGFpciBlbnRyeVwiKTtsZXQgdD1lWzBdLG49ZS5zdWJzdHJpbmcoMik7cmV0dXJuW3Qsbl19KSl9YShjcyxcInBhcnNlQXR0cmlidXRlXFxcblBhaXJzXCIpO2Z1bmN0aW9uIER1KHIpe2xldCBlPWNzKHIpLHQ9ZS5nZXQoXCJyXCIpO2lmKHQpe2lmKCFVdSh0KSl0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBTQ1JBTS1TRVJWRVItXFxcbkZJUlNULU1FU1NBR0U6IG5vbmNlIG11c3Qgb25seSBjb250YWluIHByaW50YWJsZSBjaGFyYWN0ZXJzXCIpfWVsc2UgdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVlxcXG5FUi1GSVJTVC1NRVNTQUdFOiBub25jZSBtaXNzaW5nXCIpO2xldCBuPWUuZ2V0KFwic1wiKTtpZihuKXtpZighdXMobikpdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVlxcXG5FUi1GSVJTVC1NRVNTQUdFOiBzYWx0IG11c3QgYmUgYmFzZTY0XCIpfWVsc2UgdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVkVSLUZJUlNULU1FU1NBR0U6IHNhbHRcXFxuIG1pc3NpbmdcIik7bGV0IGk9ZS5nZXQoXCJpXCIpO2lmKGkpe2lmKCEvXlsxLTldWzAtOV0qJC8udGVzdChpKSl0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBTQ1JBTS1TRVJWRVItRklcXFxuUlNULU1FU1NBR0U6IGludmFsaWQgaXRlcmF0aW9uIGNvdW50XCIpfWVsc2UgdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVkVSLUZJUlNULU1FU1NBR0U6IGl0ZXJhXFxcbnRpb24gbWlzc2luZ1wiKTtsZXQgcz1wYXJzZUludChpLDEwKTtyZXR1cm57bm9uY2U6dCxzYWx0Om4saXRlcmF0aW9uOnN9fWEoRHUsXCJwYXJzZVNlcnZlckZpcnN0TWVzc2FnZVwiKTtcbmZ1bmN0aW9uIE91KHIpe2xldCB0PWNzKHIpLmdldChcInZcIik7aWYodCl7aWYoIXVzKHQpKXRocm93IG5ldyBFcnJvcihcIlNBU0w6IFNDUkFNLVNFUlZFUi1GSU5BTC1NRVNTQUdcXFxuRTogc2VydmVyIHNpZ25hdHVyZSBtdXN0IGJlIGJhc2U2NFwiKX1lbHNlIHRocm93IG5ldyBFcnJvcihcIlNBU0w6IFNDUkFNLVNFUlZFUi1GSU5BTC1NRVNTQUdFOiBzZXJ2ZXIgXFxcbnNpZ25hdHVyZSBpcyBtaXNzaW5nXCIpO3JldHVybntzZXJ2ZXJTaWduYXR1cmU6dH19YShPdSxcInBhcnNlU2VydmVyRmluYWxNZXNzYWdlXCIpO2Z1bmN0aW9uIGxzKHIsZSl7aWYoIWQuXG5pc0J1ZmZlcihyKSl0aHJvdyBuZXcgVHlwZUVycm9yKFwiZmlyc3QgYXJndW1lbnQgbXVzdCBiZSBhIEJ1ZmZlclwiKTtpZighZC5pc0J1ZmZlcihlKSl0aHJvdyBuZXcgVHlwZUVycm9yKFxuXCJzZWNvbmQgYXJndW1lbnQgbXVzdCBiZSBhIEJ1ZmZlclwiKTtpZihyLmxlbmd0aCE9PWUubGVuZ3RoKXRocm93IG5ldyBFcnJvcihcIkJ1ZmZlciBsZW5ndGhzIG11c3QgbWF0Y1xcXG5oXCIpO2lmKHIubGVuZ3RoPT09MCl0aHJvdyBuZXcgRXJyb3IoXCJCdWZmZXJzIGNhbm5vdCBiZSBlbXB0eVwiKTtyZXR1cm4gZC5mcm9tKHIubWFwKCh0LG4pPT5yW25dXmVbbl0pKX1cbmEobHMsXCJ4b3JCdWZmZXJzXCIpO2Z1bmN0aW9uIHF1KHIpe3JldHVybiBoci5jcmVhdGVIYXNoKFwic2hhMjU2XCIpLnVwZGF0ZShyKS5kaWdlc3QoKX1hKHF1LFwic2hhMjU2XCIpO2Z1bmN0aW9uIE1lKHIsZSl7XG5yZXR1cm4gaHIuY3JlYXRlSG1hYyhcInNoYTI1NlwiLHIpLnVwZGF0ZShlKS5kaWdlc3QoKX1hKE1lLFwiaG1hY1NoYTI1NlwiKTtmdW5jdGlvbiBRdShyLGUsdCl7Zm9yKHZhciBuPU1lKFxucixkLmNvbmNhdChbZSxkLmZyb20oWzAsMCwwLDFdKV0pKSxpPW4scz0wO3M8dC0xO3MrKyluPU1lKHIsbiksaT1scyhpLG4pO3JldHVybiBpfWEoUXUsXCJIaVwiKTtmcy5leHBvcnRzPVxue3N0YXJ0U2Vzc2lvbjpGdSxjb250aW51ZVNlc3Npb246a3UsZmluYWxpemVTZXNzaW9uOk11fX0pO3ZhciBwcj17fTtuZShwcix7am9pbjooKT0+TnV9KTtmdW5jdGlvbiBOdSguLi5yKXtyZXR1cm4gci5qb2luKFwiL1wiKX12YXIgZHI9eigoKT0+e1widXNlIHN0cmljdFwiO3AoKTthKFxuTnUsXCJqb2luXCIpfSk7dmFyIHlyPXt9O25lKHlyLHtzdGF0OigpPT5qdX0pO2Z1bmN0aW9uIGp1KHIsZSl7ZShuZXcgRXJyb3IoXCJObyBmaWxlc3lzdGVtXCIpKX12YXIgbXI9eigoKT0+e1widXNlIHN0clxcXG5pY3RcIjtwKCk7YShqdSxcInN0YXRcIil9KTt2YXIgZ3I9e307bmUoZ3Ise2RlZmF1bHQ6KCk9Pld1fSk7dmFyIFd1LHdyPXooKCk9PntcInVzZSBzdHJpY3RcIjtwKCk7V3U9e319KTt2YXIgcHM9e307bmUocHMse1N0cmluZ0RlY29kZXI6KCk9PmJyfSk7dmFyIHZyLGJyLGRzPXooKCk9PntcInVzZSBzdHJpY3RcIjtwKCk7dnI9Y2xhc3MgdnJ7Y29uc3RydWN0b3IoZSl7XG5FKHRoaXMsXCJ0ZFwiKTt0aGlzLnRkPW5ldyBUZXh0RGVjb2RlcihlKX13cml0ZShlKXtyZXR1cm4gdGhpcy50ZC5kZWNvZGUoZSx7c3RyZWFtOiEwfSl9ZW5kKGUpe3JldHVybiB0aGlzLlxudGQuZGVjb2RlKGUpfX07YSh2cixcIlN0cmluZ0RlY29kZXJcIik7YnI9dnJ9KTt2YXIgd3M9SSgoaWgsZ3MpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhcntUcmFuc2Zvcm06SHV9PSh3cigpLEQoZ3IpKSx7U3RyaW5nRGVjb2RlcjokdX09KGRzKCksRChwcykpLGJlPVN5bWJvbChcblwibGFzdFwiKSxDdD1TeW1ib2woXCJkZWNvZGVyXCIpO2Z1bmN0aW9uIEd1KHIsZSx0KXtsZXQgbjtpZih0aGlzLm92ZXJmbG93KXtpZihuPXRoaXNbQ3RdLndyaXRlKHIpLnNwbGl0KFxudGhpcy5tYXRjaGVyKSxuLmxlbmd0aD09PTEpcmV0dXJuIHQoKTtuLnNoaWZ0KCksdGhpcy5vdmVyZmxvdz0hMX1lbHNlIHRoaXNbYmVdKz10aGlzW0N0XS53cml0ZShyKSxuPVxudGhpc1tiZV0uc3BsaXQodGhpcy5tYXRjaGVyKTt0aGlzW2JlXT1uLnBvcCgpO2ZvcihsZXQgaT0wO2k8bi5sZW5ndGg7aSsrKXRyeXttcyh0aGlzLHRoaXMubWFwcGVyKG5baV0pKX1jYXRjaChzKXtcbnJldHVybiB0KHMpfWlmKHRoaXMub3ZlcmZsb3c9dGhpc1tiZV0ubGVuZ3RoPnRoaXMubWF4TGVuZ3RoLHRoaXMub3ZlcmZsb3cmJiF0aGlzLnNraXBPdmVyZmxvdyl7dChuZXcgRXJyb3IoXG5cIm1heGltdW0gYnVmZmVyIHJlYWNoZWRcIikpO3JldHVybn10KCl9YShHdSxcInRyYW5zZm9ybVwiKTtmdW5jdGlvbiBWdShyKXtpZih0aGlzW2JlXSs9dGhpc1tDdF0uZW5kKCksdGhpc1tiZV0pXG50cnl7bXModGhpcyx0aGlzLm1hcHBlcih0aGlzW2JlXSkpfWNhdGNoKGUpe3JldHVybiByKGUpfXIoKX1hKFZ1LFwiZmx1c2hcIik7ZnVuY3Rpb24gbXMocixlKXtlIT09dm9pZCAwJiZcbnIucHVzaChlKX1hKG1zLFwicHVzaFwiKTtmdW5jdGlvbiB5cyhyKXtyZXR1cm4gcn1hKHlzLFwibm9vcFwiKTtmdW5jdGlvbiB6dShyLGUsdCl7c3dpdGNoKHI9cnx8L1xccj9cXG4vLGU9XG5lfHx5cyx0PXR8fHt9LGFyZ3VtZW50cy5sZW5ndGgpe2Nhc2UgMTp0eXBlb2Ygcj09XCJmdW5jdGlvblwiPyhlPXIscj0vXFxyP1xcbi8pOnR5cGVvZiByPT1cIm9iamVjdFwiJiYhKHIgaW5zdGFuY2VvZlxuUmVnRXhwKSYmIXJbU3ltYm9sLnNwbGl0XSYmKHQ9cixyPS9cXHI/XFxuLyk7YnJlYWs7Y2FzZSAyOnR5cGVvZiByPT1cImZ1bmN0aW9uXCI/KHQ9ZSxlPXIscj0vXFxyP1xcbi8pOnR5cGVvZiBlPT1cblwib2JqZWN0XCImJih0PWUsZT15cyl9dD1PYmplY3QuYXNzaWduKHt9LHQpLHQuYXV0b0Rlc3Ryb3k9ITAsdC50cmFuc2Zvcm09R3UsdC5mbHVzaD1WdSx0LnJlYWRhYmxlT2JqZWN0TW9kZT1cbiEwO2xldCBuPW5ldyBIdSh0KTtyZXR1cm4gbltiZV09XCJcIixuW0N0XT1uZXcgJHUoXCJ1dGY4XCIpLG4ubWF0Y2hlcj1yLG4ubWFwcGVyPWUsbi5tYXhMZW5ndGg9dC5tYXhMZW5ndGgsXG5uLnNraXBPdmVyZmxvdz10LnNraXBPdmVyZmxvd3x8ITEsbi5vdmVyZmxvdz0hMSxuLl9kZXN0cm95PWZ1bmN0aW9uKGkscyl7dGhpcy5fd3JpdGFibGVTdGF0ZS5lcnJvckVtaXR0ZWQ9XG4hMSxzKGkpfSxufWEoenUsXCJzcGxpdFwiKTtncy5leHBvcnRzPXp1fSk7dmFyIHhzPUkoKGFoLGRlKT0+e1widXNlIHN0cmljdFwiO3AoKTt2YXIgYnM9KGRyKCksRChwcikpLEt1PSh3cigpLEQoZ3IpKS5TdHJlYW0sWXU9d3MoKSx2cz0oaXQoKSxEKG50KSksXG5adT01NDMyLF90PW0ucGxhdGZvcm09PT1cIndpbjMyXCIsc3Q9bS5zdGRlcnIsSnU9NTYsWHU9NyxlYz02MTQ0MCx0Yz0zMjc2ODtmdW5jdGlvbiByYyhyKXtyZXR1cm4ociZlYyk9PVxudGN9YShyYyxcImlzUmVnRmlsZVwiKTt2YXIgVWU9W1wiaG9zdFwiLFwicG9ydFwiLFwiZGF0YWJhc2VcIixcInVzZXJcIixcInBhc3N3b3JkXCJdLHhyPVVlLmxlbmd0aCxuYz1VZVt4ci0xXTtmdW5jdGlvbiBTcigpe1xudmFyIHI9c3QgaW5zdGFuY2VvZiBLdSYmc3Qud3JpdGFibGU9PT0hMDtpZihyKXt2YXIgZT1BcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChhcmd1bWVudHMpLmNvbmNhdChgXG5gKTtzdC53cml0ZSh2cy5mb3JtYXQuYXBwbHkodnMsZSkpfX1hKFNyLFwid2FyblwiKTtPYmplY3QuZGVmaW5lUHJvcGVydHkoZGUuZXhwb3J0cyxcImlzV2luXCIse2dldDphKGZ1bmN0aW9uKCl7XG5yZXR1cm4gX3R9LFwiZ2V0XCIpLHNldDphKGZ1bmN0aW9uKHIpe190PXJ9LFwic2V0XCIpfSk7ZGUuZXhwb3J0cy53YXJuVG89ZnVuY3Rpb24ocil7dmFyIGU9c3Q7cmV0dXJuIHN0PVxucixlfTtkZS5leHBvcnRzLmdldEZpbGVOYW1lPWZ1bmN0aW9uKHIpe3ZhciBlPXJ8fG0uZW52LHQ9ZS5QR1BBU1NGSUxFfHwoX3Q/YnMuam9pbihlLkFQUERBVEF8fFwiLi9cIixcIlxcXG5wb3N0Z3Jlc3FsXCIsXCJwZ3Bhc3MuY29uZlwiKTpicy5qb2luKGUuSE9NRXx8XCIuL1wiLFwiLnBncGFzc1wiKSk7cmV0dXJuIHR9O2RlLmV4cG9ydHMudXNlUGdQYXNzPWZ1bmN0aW9uKHIsZSl7XG5yZXR1cm4gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG0uZW52LFwiUEdQQVNTV09SRFwiKT8hMTpfdD8hMDooZT1lfHxcIjx1bmtuPlwiLHJjKHIubW9kZSk/ci5cbm1vZGUmKEp1fFh1KT8oU3IoJ1dBUk5JTkc6IHBhc3N3b3JkIGZpbGUgXCIlc1wiIGhhcyBncm91cCBvciB3b3JsZCBhY2Nlc3M7IHBlcm1pc3Npb25zIHNob3VsZCBiZSB1PXJ3IFxcXG4oMDYwMCkgb3IgbGVzcycsZSksITEpOiEwOihTcignV0FSTklORzogcGFzc3dvcmQgZmlsZSBcIiVzXCIgaXMgbm90IGEgcGxhaW4gZmlsZScsZSksITEpKX07dmFyIGljPWRlLmV4cG9ydHMuXG5tYXRjaD1mdW5jdGlvbihyLGUpe3JldHVybiBVZS5zbGljZSgwLC0xKS5yZWR1Y2UoZnVuY3Rpb24odCxuLGkpe3JldHVybiBpPT0xJiZOdW1iZXIocltuXXx8WnUpPT09TnVtYmVyKFxuZVtuXSk/dCYmITA6dCYmKGVbbl09PT1cIipcInx8ZVtuXT09PXJbbl0pfSwhMCl9O2RlLmV4cG9ydHMuZ2V0UGFzc3dvcmQ9ZnVuY3Rpb24ocixlLHQpe3ZhciBuLGk9ZS5waXBlKFxuWXUoKSk7ZnVuY3Rpb24gcyhjKXt2YXIgbD1zYyhjKTtsJiZvYyhsKSYmaWMocixsKSYmKG49bFtuY10saS5lbmQoKSl9YShzLFwib25MaW5lXCIpO3ZhciBvPWEoZnVuY3Rpb24oKXtcbmUuZGVzdHJveSgpLHQobil9LFwib25FbmRcIiksdT1hKGZ1bmN0aW9uKGMpe2UuZGVzdHJveSgpLFNyKFwiV0FSTklORzogZXJyb3Igb24gcmVhZGluZyBmaWxlOiAlc1wiLGMpLHQoXG52b2lkIDApfSxcIm9uRXJyXCIpO2Uub24oXCJlcnJvclwiLHUpLGkub24oXCJkYXRhXCIscykub24oXCJlbmRcIixvKS5vbihcImVycm9yXCIsdSl9O3ZhciBzYz1kZS5leHBvcnRzLnBhcnNlTGluZT1cbmZ1bmN0aW9uKHIpe2lmKHIubGVuZ3RoPDExfHxyLm1hdGNoKC9eXFxzKyMvKSlyZXR1cm4gbnVsbDtmb3IodmFyIGU9XCJcIix0PVwiXCIsbj0wLGk9MCxzPTAsbz17fSx1PSExLGM9YShcbmZ1bmN0aW9uKGYseSxnKXt2YXIgQT1yLnN1YnN0cmluZyh5LGcpO09iamVjdC5oYXNPd25Qcm9wZXJ0eS5jYWxsKG0uZW52LFwiUEdQQVNTX05PX0RFRVNDQVBFXCIpfHwoQT1BLlxucmVwbGFjZSgvXFxcXChbOlxcXFxdKS9nLFwiJDFcIikpLG9bVWVbZl1dPUF9LFwiYWRkVG9PYmpcIiksbD0wO2w8ci5sZW5ndGgtMTtsKz0xKXtpZihlPXIuY2hhckF0KGwrMSksdD1yLmNoYXJBdChcbmwpLHU9bj09eHItMSx1KXtjKG4saSk7YnJlYWt9bD49MCYmZT09XCI6XCImJnQhPT1cIlxcXFxcIiYmKGMobixpLGwrMSksaT1sKzIsbis9MSl9cmV0dXJuIG89T2JqZWN0LmtleXMobykuXG5sZW5ndGg9PT14cj9vOm51bGwsb30sb2M9ZGUuZXhwb3J0cy5pc1ZhbGlkRW50cnk9ZnVuY3Rpb24ocil7Zm9yKHZhciBlPXswOmZ1bmN0aW9uKG8pe3JldHVybiBvLmxlbmd0aD5cbjB9LDE6ZnVuY3Rpb24obyl7cmV0dXJuIG89PT1cIipcIj8hMDoobz1OdW1iZXIobyksaXNGaW5pdGUobykmJm8+MCYmbzw5MDA3MTk5MjU0NzQwOTkyJiZNYXRoLmZsb29yKG8pPT09XG5vKX0sMjpmdW5jdGlvbihvKXtyZXR1cm4gby5sZW5ndGg+MH0sMzpmdW5jdGlvbihvKXtyZXR1cm4gby5sZW5ndGg+MH0sNDpmdW5jdGlvbihvKXtyZXR1cm4gby5sZW5ndGg+XG4wfX0sdD0wO3Q8VWUubGVuZ3RoO3QrPTEpe3ZhciBuPWVbdF0saT1yW1VlW3RdXXx8XCJcIixzPW4oaSk7aWYoIXMpcmV0dXJuITF9cmV0dXJuITB9fSk7dmFyIEVzPUkoKGZoLEVyKT0+e1widXNlIHN0cmljdFwiO3AoKTt2YXIgbGg9KGRyKCksRChwcikpLFNzPShtcigpLEQoeXIpKSxUdD14cygpO0VyLmV4cG9ydHM9ZnVuY3Rpb24ocixlKXtcbnZhciB0PVR0LmdldEZpbGVOYW1lKCk7U3Muc3RhdCh0LGZ1bmN0aW9uKG4saSl7aWYobnx8IVR0LnVzZVBnUGFzcyhpLHQpKXJldHVybiBlKHZvaWQgMCk7dmFyIHM9U3MuY3JlYXRlUmVhZFN0cmVhbShcbnQpO1R0LmdldFBhc3N3b3JkKHIscyxlKX0pfTtFci5leHBvcnRzLndhcm5Ubz1UdC53YXJuVG99KTt2YXIgQXM9e307bmUoQXMse2RlZmF1bHQ6KCk9PmFjfSk7dmFyIGFjLENzPXooKCk9PntcInVzZSBzdHJpY3RcIjtwKCk7YWM9e319KTt2YXIgVHM9SSgoZGgsX3MpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciB1Yz0oenQoKSxEKG1pKSksQXI9KG1yKCksRCh5cikpO2Z1bmN0aW9uIENyKHIpe2lmKHIuY2hhckF0KDApPT09XG5cIi9cIil7dmFyIHQ9ci5zcGxpdChcIiBcIik7cmV0dXJue2hvc3Q6dFswXSxkYXRhYmFzZTp0WzFdfX12YXIgZT11Yy5wYXJzZSgvIHwlW15hLWYwLTldfCVbYS1mMC05XVteYS1mMC05XS9pLlxudGVzdChyKT9lbmNvZGVVUkkocikucmVwbGFjZSgvXFwlMjUoXFxkXFxkKS9nLFwiJSQxXCIpOnIsITApLHQ9ZS5xdWVyeTtmb3IodmFyIG4gaW4gdClBcnJheS5pc0FycmF5KHRbbl0pJiZcbih0W25dPXRbbl1bdFtuXS5sZW5ndGgtMV0pO3ZhciBpPShlLmF1dGh8fFwiOlwiKS5zcGxpdChcIjpcIik7aWYodC51c2VyPWlbMF0sdC5wYXNzd29yZD1pLnNwbGljZSgxKS5qb2luKFxuXCI6XCIpLHQucG9ydD1lLnBvcnQsZS5wcm90b2NvbD09XCJzb2NrZXQ6XCIpcmV0dXJuIHQuaG9zdD1kZWNvZGVVUkkoZS5wYXRobmFtZSksdC5kYXRhYmFzZT1lLnF1ZXJ5LmRiLHQuXG5jbGllbnRfZW5jb2Rpbmc9ZS5xdWVyeS5lbmNvZGluZyx0O3QuaG9zdHx8KHQuaG9zdD1lLmhvc3RuYW1lKTt2YXIgcz1lLnBhdGhuYW1lO2lmKCF0Lmhvc3QmJnMmJi9eJTJmL2kuXG50ZXN0KHMpKXt2YXIgbz1zLnNwbGl0KFwiL1wiKTt0Lmhvc3Q9ZGVjb2RlVVJJQ29tcG9uZW50KG9bMF0pLHM9by5zcGxpY2UoMSkuam9pbihcIi9cIil9c3dpdGNoKHMmJnMuY2hhckF0KFxuMCk9PT1cIi9cIiYmKHM9cy5zbGljZSgxKXx8bnVsbCksdC5kYXRhYmFzZT1zJiZkZWNvZGVVUkkocyksKHQuc3NsPT09XCJ0cnVlXCJ8fHQuc3NsPT09XCIxXCIpJiYodC5zc2w9ITApLFxudC5zc2w9PT1cIjBcIiYmKHQuc3NsPSExKSwodC5zc2xjZXJ0fHx0LnNzbGtleXx8dC5zc2xyb290Y2VydHx8dC5zc2xtb2RlKSYmKHQuc3NsPXt9KSx0LnNzbGNlcnQmJih0LnNzbC5cbmNlcnQ9QXIucmVhZEZpbGVTeW5jKHQuc3NsY2VydCkudG9TdHJpbmcoKSksdC5zc2xrZXkmJih0LnNzbC5rZXk9QXIucmVhZEZpbGVTeW5jKHQuc3Nsa2V5KS50b1N0cmluZygpKSxcbnQuc3Nscm9vdGNlcnQmJih0LnNzbC5jYT1Bci5yZWFkRmlsZVN5bmModC5zc2xyb290Y2VydCkudG9TdHJpbmcoKSksdC5zc2xtb2RlKXtjYXNlXCJkaXNhYmxlXCI6e3Quc3NsPVxuITE7YnJlYWt9Y2FzZVwicHJlZmVyXCI6Y2FzZVwicmVxdWlyZVwiOmNhc2VcInZlcmlmeS1jYVwiOmNhc2VcInZlcmlmeS1mdWxsXCI6YnJlYWs7Y2FzZVwibm8tdmVyaWZ5XCI6e3Quc3NsLnJlamVjdFVuYXV0aG9yaXplZD1cbiExO2JyZWFrfX1yZXR1cm4gdH1hKENyLFwicGFyc2VcIik7X3MuZXhwb3J0cz1DcjtDci5wYXJzZT1Dcn0pO3ZhciBJdD1JKChnaCxScyk9PntcInVzZSBzdHJpY3RcIjtwKCk7dmFyIGNjPShDcygpLEQoQXMpKSxQcz10dCgpLElzPVRzKCkucGFyc2UsRz1hKGZ1bmN0aW9uKHIsZSx0KXtyZXR1cm4gdD09PVxudm9pZCAwP3Q9bS5lbnZbXCJQR1wiK3IudG9VcHBlckNhc2UoKV06dD09PSExfHwodD1tLmVudlt0XSksZVtyXXx8dHx8UHNbcl19LFwidmFsXCIpLGxjPWEoZnVuY3Rpb24oKXtzd2l0Y2gobS5cbmVudi5QR1NTTE1PREUpe2Nhc2VcImRpc2FibGVcIjpyZXR1cm4hMTtjYXNlXCJwcmVmZXJcIjpjYXNlXCJyZXF1aXJlXCI6Y2FzZVwidmVyaWZ5LWNhXCI6Y2FzZVwidmVyaWZ5LWZ1bGxcIjpyZXR1cm4hMDtjYXNlXCJcXFxubm8tdmVyaWZ5XCI6cmV0dXJue3JlamVjdFVuYXV0aG9yaXplZDohMX19cmV0dXJuIFBzLnNzbH0sXCJyZWFkU1NMQ29uZmlnRnJvbUVudmlyb25tZW50XCIpLERlPWEoZnVuY3Rpb24ocil7XG5yZXR1cm5cIidcIisoXCJcIityKS5yZXBsYWNlKC9cXFxcL2csXCJcXFxcXFxcXFwiKS5yZXBsYWNlKC8nL2csXCJcXFxcJ1wiKStcIidcIn0sXCJxdW90ZVBhcmFtVmFsdWVcIikscmU9YShmdW5jdGlvbihyLGUsdCl7XG52YXIgbj1lW3RdO24hPW51bGwmJnIucHVzaCh0K1wiPVwiK0RlKG4pKX0sXCJhZGRcIiksVHI9Y2xhc3MgVHJ7Y29uc3RydWN0b3IoZSl7ZT10eXBlb2YgZT09XCJzdHJpbmdcIj9JcyhlKTpcbmV8fHt9LGUuY29ubmVjdGlvblN0cmluZyYmKGU9T2JqZWN0LmFzc2lnbih7fSxlLElzKGUuY29ubmVjdGlvblN0cmluZykpKSx0aGlzLnVzZXI9RyhcInVzZXJcIixlKSx0aGlzLlxuZGF0YWJhc2U9RyhcImRhdGFiYXNlXCIsZSksdGhpcy5kYXRhYmFzZT09PXZvaWQgMCYmKHRoaXMuZGF0YWJhc2U9dGhpcy51c2VyKSx0aGlzLnBvcnQ9cGFyc2VJbnQoRyhcInBvclxcXG50XCIsZSksMTApLHRoaXMuaG9zdD1HKFwiaG9zdFwiLGUpLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLFwicGFzc3dvcmRcIix7Y29uZmlndXJhYmxlOiEwLGVudW1lcmFibGU6ITEsXG53cml0YWJsZTohMCx2YWx1ZTpHKFwicGFzc3dvcmRcIixlKX0pLHRoaXMuYmluYXJ5PUcoXCJiaW5hcnlcIixlKSx0aGlzLm9wdGlvbnM9RyhcIm9wdGlvbnNcIixlKSx0aGlzLnNzbD10eXBlb2YgZS5cbnNzbD5cInVcIj9sYygpOmUuc3NsLHR5cGVvZiB0aGlzLnNzbD09XCJzdHJpbmdcIiYmdGhpcy5zc2w9PT1cInRydWVcIiYmKHRoaXMuc3NsPSEwKSx0aGlzLnNzbD09PVwibm8tdmVyaWZ5XCImJlxuKHRoaXMuc3NsPXtyZWplY3RVbmF1dGhvcml6ZWQ6ITF9KSx0aGlzLnNzbCYmdGhpcy5zc2wua2V5JiZPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcy5zc2wsXCJrZXlcIix7ZW51bWVyYWJsZTohMX0pLFxudGhpcy5jbGllbnRfZW5jb2Rpbmc9RyhcImNsaWVudF9lbmNvZGluZ1wiLGUpLHRoaXMucmVwbGljYXRpb249RyhcInJlcGxpY2F0aW9uXCIsZSksdGhpcy5pc0RvbWFpblNvY2tldD1cbiEodGhpcy5ob3N0fHxcIlwiKS5pbmRleE9mKFwiL1wiKSx0aGlzLmFwcGxpY2F0aW9uX25hbWU9RyhcImFwcGxpY2F0aW9uX25hbWVcIixlLFwiUEdBUFBOQU1FXCIpLHRoaXMuZmFsbGJhY2tfYXBwbGljYXRpb25fbmFtZT1cbkcoXCJmYWxsYmFja19hcHBsaWNhdGlvbl9uYW1lXCIsZSwhMSksdGhpcy5zdGF0ZW1lbnRfdGltZW91dD1HKFwic3RhdGVtZW50X3RpbWVvdXRcIixlLCExKSx0aGlzLmxvY2tfdGltZW91dD1cbkcoXCJsb2NrX3RpbWVvdXRcIixlLCExKSx0aGlzLmlkbGVfaW5fdHJhbnNhY3Rpb25fc2Vzc2lvbl90aW1lb3V0PUcoXCJpZGxlX2luX3RyYW5zYWN0aW9uX3Nlc3Npb25fdGltZW9cXFxudXRcIixlLCExKSx0aGlzLnF1ZXJ5X3RpbWVvdXQ9RyhcInF1ZXJ5X3RpbWVvdXRcIixlLCExKSxlLmNvbm5lY3Rpb25UaW1lb3V0TWlsbGlzPT09dm9pZCAwP3RoaXMuY29ubmVjdF90aW1lb3V0PVxubS5lbnYuUEdDT05ORUNUX1RJTUVPVVR8fDA6dGhpcy5jb25uZWN0X3RpbWVvdXQ9TWF0aC5mbG9vcihlLmNvbm5lY3Rpb25UaW1lb3V0TWlsbGlzLzFlMyksZS5rZWVwQWxpdmU9PT1cbiExP3RoaXMua2VlcGFsaXZlcz0wOmUua2VlcEFsaXZlPT09ITAmJih0aGlzLmtlZXBhbGl2ZXM9MSksdHlwZW9mIGUua2VlcEFsaXZlSW5pdGlhbERlbGF5TWlsbGlzPT1cIm51XFxcbm1iZXJcIiYmKHRoaXMua2VlcGFsaXZlc19pZGxlPU1hdGguZmxvb3IoZS5rZWVwQWxpdmVJbml0aWFsRGVsYXlNaWxsaXMvMWUzKSl9Z2V0TGlicHFDb25uZWN0aW9uU3RyaW5nKGUpe1xudmFyIHQ9W107cmUodCx0aGlzLFwidXNlclwiKSxyZSh0LHRoaXMsXCJwYXNzd29yZFwiKSxyZSh0LHRoaXMsXCJwb3J0XCIpLHJlKHQsdGhpcyxcImFwcGxpY2F0aW9uX25hbWVcIikscmUoXG50LHRoaXMsXCJmYWxsYmFja19hcHBsaWNhdGlvbl9uYW1lXCIpLHJlKHQsdGhpcyxcImNvbm5lY3RfdGltZW91dFwiKSxyZSh0LHRoaXMsXCJvcHRpb25zXCIpO3ZhciBuPXR5cGVvZiB0aGlzLlxuc3NsPT1cIm9iamVjdFwiP3RoaXMuc3NsOnRoaXMuc3NsP3tzc2xtb2RlOnRoaXMuc3NsfTp7fTtpZihyZSh0LG4sXCJzc2xtb2RlXCIpLHJlKHQsbixcInNzbGNhXCIpLHJlKHQsbixcInNcXFxuc2xrZXlcIikscmUodCxuLFwic3NsY2VydFwiKSxyZSh0LG4sXCJzc2xyb290Y2VydFwiKSx0aGlzLmRhdGFiYXNlJiZ0LnB1c2goXCJkYm5hbWU9XCIrRGUodGhpcy5kYXRhYmFzZSkpLHRoaXMuXG5yZXBsaWNhdGlvbiYmdC5wdXNoKFwicmVwbGljYXRpb249XCIrRGUodGhpcy5yZXBsaWNhdGlvbikpLHRoaXMuaG9zdCYmdC5wdXNoKFwiaG9zdD1cIitEZSh0aGlzLmhvc3QpKSx0aGlzLlxuaXNEb21haW5Tb2NrZXQpcmV0dXJuIGUobnVsbCx0LmpvaW4oXCIgXCIpKTt0aGlzLmNsaWVudF9lbmNvZGluZyYmdC5wdXNoKFwiY2xpZW50X2VuY29kaW5nPVwiK0RlKHRoaXMuY2xpZW50X2VuY29kaW5nKSksXG5jYy5sb29rdXAodGhpcy5ob3N0LGZ1bmN0aW9uKGkscyl7cmV0dXJuIGk/ZShpLG51bGwpOih0LnB1c2goXCJob3N0YWRkcj1cIitEZShzKSksZShudWxsLHQuam9pbihcIiBcIikpKX0pfX07XG5hKFRyLFwiQ29ubmVjdGlvblBhcmFtZXRlcnNcIik7dmFyIF9yPVRyO1JzLmV4cG9ydHM9X3J9KTt2YXIgRnM9SSgodmgsTHMpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciBmYz1KZSgpLEJzPS9eKFtBLVphLXpdKykoPzogKFxcZCspKT8oPzogKFxcZCspKT8vLFByPWNsYXNzIFBye2NvbnN0cnVjdG9yKGUsdCl7XG50aGlzLmNvbW1hbmQ9bnVsbCx0aGlzLnJvd0NvdW50PW51bGwsdGhpcy5vaWQ9bnVsbCx0aGlzLnJvd3M9W10sdGhpcy5maWVsZHM9W10sdGhpcy5fcGFyc2Vycz12b2lkIDAsXG50aGlzLl90eXBlcz10LHRoaXMuUm93Q3Rvcj1udWxsLHRoaXMucm93QXNBcnJheT1lPT09XCJhcnJheVwiLHRoaXMucm93QXNBcnJheSYmKHRoaXMucGFyc2VSb3c9dGhpcy5fcGFyc2VSb3dBc0FycmF5KX1hZGRDb21tYW5kQ29tcGxldGUoZSl7XG52YXIgdDtlLnRleHQ/dD1Ccy5leGVjKGUudGV4dCk6dD1Ccy5leGVjKGUuY29tbWFuZCksdCYmKHRoaXMuY29tbWFuZD10WzFdLHRbM10/KHRoaXMub2lkPXBhcnNlSW50KHRbMl0sXG4xMCksdGhpcy5yb3dDb3VudD1wYXJzZUludCh0WzNdLDEwKSk6dFsyXSYmKHRoaXMucm93Q291bnQ9cGFyc2VJbnQodFsyXSwxMCkpKX1fcGFyc2VSb3dBc0FycmF5KGUpe2Zvcih2YXIgdD1uZXcgQXJyYXkoXG5lLmxlbmd0aCksbj0wLGk9ZS5sZW5ndGg7bjxpO24rKyl7dmFyIHM9ZVtuXTtzIT09bnVsbD90W25dPXRoaXMuX3BhcnNlcnNbbl0ocyk6dFtuXT1udWxsfXJldHVybiB0fXBhcnNlUm93KGUpe1xuZm9yKHZhciB0PXt9LG49MCxpPWUubGVuZ3RoO248aTtuKyspe3ZhciBzPWVbbl0sbz10aGlzLmZpZWxkc1tuXS5uYW1lO3MhPT1udWxsP3Rbb109dGhpcy5fcGFyc2Vyc1tuXShcbnMpOnRbb109bnVsbH1yZXR1cm4gdH1hZGRSb3coZSl7dGhpcy5yb3dzLnB1c2goZSl9YWRkRmllbGRzKGUpe3RoaXMuZmllbGRzPWUsdGhpcy5maWVsZHMubGVuZ3RoJiYodGhpcy5cbl9wYXJzZXJzPW5ldyBBcnJheShlLmxlbmd0aCkpO2Zvcih2YXIgdD0wO3Q8ZS5sZW5ndGg7dCsrKXt2YXIgbj1lW3RdO3RoaXMuX3R5cGVzP3RoaXMuX3BhcnNlcnNbdF09dGhpcy5cbl90eXBlcy5nZXRUeXBlUGFyc2VyKG4uZGF0YVR5cGVJRCxuLmZvcm1hdHx8XCJ0ZXh0XCIpOnRoaXMuX3BhcnNlcnNbdF09ZmMuZ2V0VHlwZVBhcnNlcihuLmRhdGFUeXBlSUQsbi5cbmZvcm1hdHx8XCJ0ZXh0XCIpfX19O2EoUHIsXCJSZXN1bHRcIik7dmFyIElyPVByO0xzLmV4cG9ydHM9SXJ9KTt2YXIgRHM9SSgoRWgsVXMpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhcntFdmVudEVtaXR0ZXI6aGN9PW1lKCksa3M9RnMoKSxNcz1ydCgpLEJyPWNsYXNzIEJyIGV4dGVuZHMgaGN7Y29uc3RydWN0b3IoZSx0LG4pe1xuc3VwZXIoKSxlPU1zLm5vcm1hbGl6ZVF1ZXJ5Q29uZmlnKGUsdCxuKSx0aGlzLnRleHQ9ZS50ZXh0LHRoaXMudmFsdWVzPWUudmFsdWVzLHRoaXMucm93cz1lLnJvd3MsdGhpcy5cbnR5cGVzPWUudHlwZXMsdGhpcy5uYW1lPWUubmFtZSx0aGlzLmJpbmFyeT1lLmJpbmFyeSx0aGlzLnBvcnRhbD1lLnBvcnRhbHx8XCJcIix0aGlzLmNhbGxiYWNrPWUuY2FsbGJhY2ssXG50aGlzLl9yb3dNb2RlPWUucm93TW9kZSxtLmRvbWFpbiYmZS5jYWxsYmFjayYmKHRoaXMuY2FsbGJhY2s9bS5kb21haW4uYmluZChlLmNhbGxiYWNrKSksdGhpcy5fcmVzdWx0PVxubmV3IGtzKHRoaXMuX3Jvd01vZGUsdGhpcy50eXBlcyksdGhpcy5fcmVzdWx0cz10aGlzLl9yZXN1bHQsdGhpcy5pc1ByZXBhcmVkU3RhdGVtZW50PSExLHRoaXMuX2NhbmNlbGVkRHVlVG9FcnJvcj1cbiExLHRoaXMuX3Byb21pc2U9bnVsbH1yZXF1aXJlc1ByZXBhcmF0aW9uKCl7cmV0dXJuIHRoaXMubmFtZXx8dGhpcy5yb3dzPyEwOiF0aGlzLnRleHR8fCF0aGlzLnZhbHVlcz9cbiExOnRoaXMudmFsdWVzLmxlbmd0aD4wfV9jaGVja0Zvck11bHRpcm93KCl7dGhpcy5fcmVzdWx0LmNvbW1hbmQmJihBcnJheS5pc0FycmF5KHRoaXMuX3Jlc3VsdHMpfHwodGhpcy5cbl9yZXN1bHRzPVt0aGlzLl9yZXN1bHRdKSx0aGlzLl9yZXN1bHQ9bmV3IGtzKHRoaXMuX3Jvd01vZGUsdGhpcy50eXBlcyksdGhpcy5fcmVzdWx0cy5wdXNoKHRoaXMuX3Jlc3VsdCkpfWhhbmRsZVJvd0Rlc2NyaXB0aW9uKGUpe1xudGhpcy5fY2hlY2tGb3JNdWx0aXJvdygpLHRoaXMuX3Jlc3VsdC5hZGRGaWVsZHMoZS5maWVsZHMpLHRoaXMuX2FjY3VtdWxhdGVSb3dzPXRoaXMuY2FsbGJhY2t8fCF0aGlzLlxubGlzdGVuZXJzKFwicm93XCIpLmxlbmd0aH1oYW5kbGVEYXRhUm93KGUpe2xldCB0O2lmKCF0aGlzLl9jYW5jZWxlZER1ZVRvRXJyb3Ipe3RyeXt0PXRoaXMuX3Jlc3VsdC5wYXJzZVJvdyhcbmUuZmllbGRzKX1jYXRjaChuKXt0aGlzLl9jYW5jZWxlZER1ZVRvRXJyb3I9bjtyZXR1cm59dGhpcy5lbWl0KFwicm93XCIsdCx0aGlzLl9yZXN1bHQpLHRoaXMuX2FjY3VtdWxhdGVSb3dzJiZcbnRoaXMuX3Jlc3VsdC5hZGRSb3codCl9fWhhbmRsZUNvbW1hbmRDb21wbGV0ZShlLHQpe3RoaXMuX2NoZWNrRm9yTXVsdGlyb3coKSx0aGlzLl9yZXN1bHQuYWRkQ29tbWFuZENvbXBsZXRlKFxuZSksdGhpcy5yb3dzJiZ0LnN5bmMoKX1oYW5kbGVFbXB0eVF1ZXJ5KGUpe3RoaXMucm93cyYmZS5zeW5jKCl9aGFuZGxlRXJyb3IoZSx0KXtpZih0aGlzLl9jYW5jZWxlZER1ZVRvRXJyb3ImJlxuKGU9dGhpcy5fY2FuY2VsZWREdWVUb0Vycm9yLHRoaXMuX2NhbmNlbGVkRHVlVG9FcnJvcj0hMSksdGhpcy5jYWxsYmFjaylyZXR1cm4gdGhpcy5jYWxsYmFjayhlKTt0aGlzLlxuZW1pdChcImVycm9yXCIsZSl9aGFuZGxlUmVhZHlGb3JRdWVyeShlKXtpZih0aGlzLl9jYW5jZWxlZER1ZVRvRXJyb3IpcmV0dXJuIHRoaXMuaGFuZGxlRXJyb3IodGhpcy5fY2FuY2VsZWREdWVUb0Vycm9yLFxuZSk7aWYodGhpcy5jYWxsYmFjayl0cnl7dGhpcy5jYWxsYmFjayhudWxsLHRoaXMuX3Jlc3VsdHMpfWNhdGNoKHQpe20ubmV4dFRpY2soKCk9Pnt0aHJvdyB0fSl9dGhpcy5lbWl0KFxuXCJlbmRcIix0aGlzLl9yZXN1bHRzKX1zdWJtaXQoZSl7aWYodHlwZW9mIHRoaXMudGV4dCE9XCJzdHJpbmdcIiYmdHlwZW9mIHRoaXMubmFtZSE9XCJzdHJpbmdcIilyZXR1cm4gbmV3IEVycm9yKFxuXCJBIHF1ZXJ5IG11c3QgaGF2ZSBlaXRoZXIgdGV4dCBvciBhIG5hbWUuIFN1cHBseWluZyBuZWl0aGVyIGlzIHVuc3VwcG9ydGVkLlwiKTtsZXQgdD1lLnBhcnNlZFN0YXRlbWVudHNbdGhpcy5cbm5hbWVdO3JldHVybiB0aGlzLnRleHQmJnQmJnRoaXMudGV4dCE9PXQ/bmV3IEVycm9yKGBQcmVwYXJlZCBzdGF0ZW1lbnRzIG11c3QgYmUgdW5pcXVlIC0gJyR7dGhpcy5uYW1lfVxcXG4nIHdhcyB1c2VkIGZvciBhIGRpZmZlcmVudCBzdGF0ZW1lbnRgKTp0aGlzLnZhbHVlcyYmIUFycmF5LmlzQXJyYXkodGhpcy52YWx1ZXMpP25ldyBFcnJvcihcIlF1ZXJ5IHZhbFxcXG51ZXMgbXVzdCBiZSBhbiBhcnJheVwiKToodGhpcy5yZXF1aXJlc1ByZXBhcmF0aW9uKCk/dGhpcy5wcmVwYXJlKGUpOmUucXVlcnkodGhpcy50ZXh0KSxudWxsKX1oYXNCZWVuUGFyc2VkKGUpe1xucmV0dXJuIHRoaXMubmFtZSYmZS5wYXJzZWRTdGF0ZW1lbnRzW3RoaXMubmFtZV19aGFuZGxlUG9ydGFsU3VzcGVuZGVkKGUpe3RoaXMuX2dldFJvd3MoZSx0aGlzLnJvd3MpfV9nZXRSb3dzKGUsdCl7XG5lLmV4ZWN1dGUoe3BvcnRhbDp0aGlzLnBvcnRhbCxyb3dzOnR9KSx0P2UuZmx1c2goKTplLnN5bmMoKX1wcmVwYXJlKGUpe3RoaXMuaXNQcmVwYXJlZFN0YXRlbWVudD0hMCx0aGlzLlxuaGFzQmVlblBhcnNlZChlKXx8ZS5wYXJzZSh7dGV4dDp0aGlzLnRleHQsbmFtZTp0aGlzLm5hbWUsdHlwZXM6dGhpcy50eXBlc30pO3RyeXtlLmJpbmQoe3BvcnRhbDp0aGlzLlxucG9ydGFsLHN0YXRlbWVudDp0aGlzLm5hbWUsdmFsdWVzOnRoaXMudmFsdWVzLGJpbmFyeTp0aGlzLmJpbmFyeSx2YWx1ZU1hcHBlcjpNcy5wcmVwYXJlVmFsdWV9KX1jYXRjaCh0KXtcbnRoaXMuaGFuZGxlRXJyb3IodCxlKTtyZXR1cm59ZS5kZXNjcmliZSh7dHlwZTpcIlBcIixuYW1lOnRoaXMucG9ydGFsfHxcIlwifSksdGhpcy5fZ2V0Um93cyhlLHRoaXMucm93cyl9aGFuZGxlQ29weUluUmVzcG9uc2UoZSl7XG5lLnNlbmRDb3B5RmFpbChcIk5vIHNvdXJjZSBzdHJlYW0gZGVmaW5lZFwiKX1oYW5kbGVDb3B5RGF0YShlLHQpe319O2EoQnIsXCJRdWVyeVwiKTt2YXIgUnI9QnI7VXMuZXhwb3J0cz1cblJyfSk7dmFyIGFuPUkoVD0+e1widXNlIHN0cmljdFwiO3AoKTtPYmplY3QuZGVmaW5lUHJvcGVydHkoVCxcIl9fZXNNb2R1bGVcIix7dmFsdWU6ITB9KTtULk5vdGljZU1lc3NhZ2U9VC5EYXRhUm93TWVzc2FnZT1cblQuQ29tbWFuZENvbXBsZXRlTWVzc2FnZT1ULlJlYWR5Rm9yUXVlcnlNZXNzYWdlPVQuTm90aWZpY2F0aW9uUmVzcG9uc2VNZXNzYWdlPVQuQmFja2VuZEtleURhdGFNZXNzYWdlPVxuVC5BdXRoZW50aWNhdGlvbk1ENVBhc3N3b3JkPVQuUGFyYW1ldGVyU3RhdHVzTWVzc2FnZT1ULlBhcmFtZXRlckRlc2NyaXB0aW9uTWVzc2FnZT1ULlJvd0Rlc2NyaXB0aW9uTWVzc2FnZT1cblQuRmllbGQ9VC5Db3B5UmVzcG9uc2U9VC5Db3B5RGF0YU1lc3NhZ2U9VC5EYXRhYmFzZUVycm9yPVQuY29weURvbmU9VC5lbXB0eVF1ZXJ5PVQucmVwbGljYXRpb25TdGFydD1cblQucG9ydGFsU3VzcGVuZGVkPVQubm9EYXRhPVQuY2xvc2VDb21wbGV0ZT1ULmJpbmRDb21wbGV0ZT1ULnBhcnNlQ29tcGxldGU9dm9pZCAwO1QucGFyc2VDb21wbGV0ZT17bmFtZTpcIlxcXG5wYXJzZUNvbXBsZXRlXCIsbGVuZ3RoOjV9O1QuYmluZENvbXBsZXRlPXtuYW1lOlwiYmluZENvbXBsZXRlXCIsbGVuZ3RoOjV9O1QuY2xvc2VDb21wbGV0ZT17bmFtZTpcImNsb3NlQ1xcXG5vbXBsZXRlXCIsbGVuZ3RoOjV9O1Qubm9EYXRhPXtuYW1lOlwibm9EYXRhXCIsbGVuZ3RoOjV9O1QucG9ydGFsU3VzcGVuZGVkPXtuYW1lOlwicG9ydGFsU3VzcGVuZGVkXCIsbGVuZ3RoOjV9O1xuVC5yZXBsaWNhdGlvblN0YXJ0PXtuYW1lOlwicmVwbGljYXRpb25TdGFydFwiLGxlbmd0aDo0fTtULmVtcHR5UXVlcnk9e25hbWU6XCJlbXB0eVF1ZXJ5XCIsbGVuZ3RoOjR9O1QuY29weURvbmU9XG57bmFtZTpcImNvcHlEb25lXCIsbGVuZ3RoOjR9O3ZhciBHcj1jbGFzcyBHciBleHRlbmRzIEVycm9ye2NvbnN0cnVjdG9yKGUsdCxuKXtzdXBlcihlKSx0aGlzLmxlbmd0aD10LHRoaXMuXG5uYW1lPW59fTthKEdyLFwiRGF0YWJhc2VFcnJvclwiKTt2YXIgTHI9R3I7VC5EYXRhYmFzZUVycm9yPUxyO3ZhciBWcj1jbGFzcyBWcntjb25zdHJ1Y3RvcihlLHQpe3RoaXMubGVuZ3RoPVxuZSx0aGlzLmNodW5rPXQsdGhpcy5uYW1lPVwiY29weURhdGFcIn19O2EoVnIsXCJDb3B5RGF0YU1lc3NhZ2VcIik7dmFyIEZyPVZyO1QuQ29weURhdGFNZXNzYWdlPUZyO3ZhciB6cj1jbGFzcyB6cntjb25zdHJ1Y3RvcihlLHQsbixpKXtcbnRoaXMubGVuZ3RoPWUsdGhpcy5uYW1lPXQsdGhpcy5iaW5hcnk9bix0aGlzLmNvbHVtblR5cGVzPW5ldyBBcnJheShpKX19O2EoenIsXCJDb3B5UmVzcG9uc2VcIik7dmFyIGtyPXpyO1xuVC5Db3B5UmVzcG9uc2U9a3I7dmFyIEtyPWNsYXNzIEtye2NvbnN0cnVjdG9yKGUsdCxuLGkscyxvLHUpe3RoaXMubmFtZT1lLHRoaXMudGFibGVJRD10LHRoaXMuY29sdW1uSUQ9XG5uLHRoaXMuZGF0YVR5cGVJRD1pLHRoaXMuZGF0YVR5cGVTaXplPXMsdGhpcy5kYXRhVHlwZU1vZGlmaWVyPW8sdGhpcy5mb3JtYXQ9dX19O2EoS3IsXCJGaWVsZFwiKTt2YXIgTXI9S3I7XG5ULkZpZWxkPU1yO3ZhciBZcj1jbGFzcyBZcntjb25zdHJ1Y3RvcihlLHQpe3RoaXMubGVuZ3RoPWUsdGhpcy5maWVsZENvdW50PXQsdGhpcy5uYW1lPVwicm93RGVzY3JpcHRpb1xcXG5uXCIsdGhpcy5maWVsZHM9bmV3IEFycmF5KHRoaXMuZmllbGRDb3VudCl9fTthKFlyLFwiUm93RGVzY3JpcHRpb25NZXNzYWdlXCIpO3ZhciBVcj1ZcjtULlJvd0Rlc2NyaXB0aW9uTWVzc2FnZT1cblVyO3ZhciBacj1jbGFzcyBacntjb25zdHJ1Y3RvcihlLHQpe3RoaXMubGVuZ3RoPWUsdGhpcy5wYXJhbWV0ZXJDb3VudD10LHRoaXMubmFtZT1cInBhcmFtZXRlckRlc2NyaXB0XFxcbmlvblwiLHRoaXMuZGF0YVR5cGVJRHM9bmV3IEFycmF5KHRoaXMucGFyYW1ldGVyQ291bnQpfX07YShacixcIlBhcmFtZXRlckRlc2NyaXB0aW9uTWVzc2FnZVwiKTt2YXIgRHI9WnI7XG5ULlBhcmFtZXRlckRlc2NyaXB0aW9uTWVzc2FnZT1Ecjt2YXIgSnI9Y2xhc3MgSnJ7Y29uc3RydWN0b3IoZSx0LG4pe3RoaXMubGVuZ3RoPWUsdGhpcy5wYXJhbWV0ZXJOYW1lPVxudCx0aGlzLnBhcmFtZXRlclZhbHVlPW4sdGhpcy5uYW1lPVwicGFyYW1ldGVyU3RhdHVzXCJ9fTthKEpyLFwiUGFyYW1ldGVyU3RhdHVzTWVzc2FnZVwiKTt2YXIgT3I9SnI7VC5QYXJhbWV0ZXJTdGF0dXNNZXNzYWdlPVxuT3I7dmFyIFhyPWNsYXNzIFhye2NvbnN0cnVjdG9yKGUsdCl7dGhpcy5sZW5ndGg9ZSx0aGlzLnNhbHQ9dCx0aGlzLm5hbWU9XCJhdXRoZW50aWNhdGlvbk1ENVBhc3N3b3JkXCJ9fTtcbmEoWHIsXCJBdXRoZW50aWNhdGlvbk1ENVBhc3N3b3JkXCIpO3ZhciBxcj1YcjtULkF1dGhlbnRpY2F0aW9uTUQ1UGFzc3dvcmQ9cXI7dmFyIGVuPWNsYXNzIGVue2NvbnN0cnVjdG9yKGUsdCxuKXtcbnRoaXMubGVuZ3RoPWUsdGhpcy5wcm9jZXNzSUQ9dCx0aGlzLnNlY3JldEtleT1uLHRoaXMubmFtZT1cImJhY2tlbmRLZXlEYXRhXCJ9fTthKGVuLFwiQmFja2VuZEtleURhdGFNZXNcXFxuc2FnZVwiKTt2YXIgUXI9ZW47VC5CYWNrZW5kS2V5RGF0YU1lc3NhZ2U9UXI7dmFyIHRuPWNsYXNzIHRue2NvbnN0cnVjdG9yKGUsdCxuLGkpe3RoaXMubGVuZ3RoPWUsdGhpcy5cbnByb2Nlc3NJZD10LHRoaXMuY2hhbm5lbD1uLHRoaXMucGF5bG9hZD1pLHRoaXMubmFtZT1cIm5vdGlmaWNhdGlvblwifX07YSh0bixcIk5vdGlmaWNhdGlvblJlc3BvbnNlTWVzc2FcXFxuZ2VcIik7dmFyIE5yPXRuO1QuTm90aWZpY2F0aW9uUmVzcG9uc2VNZXNzYWdlPU5yO3ZhciBybj1jbGFzcyBybntjb25zdHJ1Y3RvcihlLHQpe3RoaXMubGVuZ3RoPWUsdGhpcy5cbnN0YXR1cz10LHRoaXMubmFtZT1cInJlYWR5Rm9yUXVlcnlcIn19O2Eocm4sXCJSZWFkeUZvclF1ZXJ5TWVzc2FnZVwiKTt2YXIganI9cm47VC5SZWFkeUZvclF1ZXJ5TWVzc2FnZT1qcjtcbnZhciBubj1jbGFzcyBubntjb25zdHJ1Y3RvcihlLHQpe3RoaXMubGVuZ3RoPWUsdGhpcy50ZXh0PXQsdGhpcy5uYW1lPVwiY29tbWFuZENvbXBsZXRlXCJ9fTthKG5uLFwiQ29tbWFcXFxubmRDb21wbGV0ZU1lc3NhZ2VcIik7dmFyIFdyPW5uO1QuQ29tbWFuZENvbXBsZXRlTWVzc2FnZT1Xcjt2YXIgc249Y2xhc3Mgc257Y29uc3RydWN0b3IoZSx0KXt0aGlzLmxlbmd0aD1cbmUsdGhpcy5maWVsZHM9dCx0aGlzLm5hbWU9XCJkYXRhUm93XCIsdGhpcy5maWVsZENvdW50PXQubGVuZ3RofX07YShzbixcIkRhdGFSb3dNZXNzYWdlXCIpO3ZhciBIcj1zbjtULkRhdGFSb3dNZXNzYWdlPVxuSHI7dmFyIG9uPWNsYXNzIG9ue2NvbnN0cnVjdG9yKGUsdCl7dGhpcy5sZW5ndGg9ZSx0aGlzLm1lc3NhZ2U9dCx0aGlzLm5hbWU9XCJub3RpY2VcIn19O2Eob24sXCJOb3RpY2VNZVxcXG5zc2FnZVwiKTt2YXIgJHI9b247VC5Ob3RpY2VNZXNzYWdlPSRyfSk7dmFyIE9zPUkoUHQ9PntcInVzZSBzdHJpY3RcIjtwKCk7T2JqZWN0LmRlZmluZVByb3BlcnR5KFB0LFwiX19lc01vZHVsZVwiLHt2YWx1ZTohMH0pO1B0LldyaXRlcj12b2lkIDA7dmFyIGNuPWNsYXNzIGNue2NvbnN0cnVjdG9yKGU9MjU2KXtcbnRoaXMuc2l6ZT1lLHRoaXMub2Zmc2V0PTUsdGhpcy5oZWFkZXJQb3NpdGlvbj0wLHRoaXMuYnVmZmVyPWQuYWxsb2NVbnNhZmUoZSl9ZW5zdXJlKGUpe3ZhciB0PXRoaXMuYnVmZmVyLlxubGVuZ3RoLXRoaXMub2Zmc2V0O2lmKHQ8ZSl7dmFyIG49dGhpcy5idWZmZXIsaT1uLmxlbmd0aCsobi5sZW5ndGg+PjEpK2U7dGhpcy5idWZmZXI9ZC5hbGxvY1Vuc2FmZShpKSxcbm4uY29weSh0aGlzLmJ1ZmZlcil9fWFkZEludDMyKGUpe3JldHVybiB0aGlzLmVuc3VyZSg0KSx0aGlzLmJ1ZmZlclt0aGlzLm9mZnNldCsrXT1lPj4+MjQmMjU1LHRoaXMuYnVmZmVyW3RoaXMuXG5vZmZzZXQrK109ZT4+PjE2JjI1NSx0aGlzLmJ1ZmZlclt0aGlzLm9mZnNldCsrXT1lPj4+OCYyNTUsdGhpcy5idWZmZXJbdGhpcy5vZmZzZXQrK109ZT4+PjAmMjU1LHRoaXN9YWRkSW50MTYoZSl7XG5yZXR1cm4gdGhpcy5lbnN1cmUoMiksdGhpcy5idWZmZXJbdGhpcy5vZmZzZXQrK109ZT4+PjgmMjU1LHRoaXMuYnVmZmVyW3RoaXMub2Zmc2V0KytdPWU+Pj4wJjI1NSx0aGlzfWFkZENTdHJpbmcoZSl7XG5pZighZSl0aGlzLmVuc3VyZSgxKTtlbHNle3ZhciB0PWQuYnl0ZUxlbmd0aChlKTt0aGlzLmVuc3VyZSh0KzEpLHRoaXMuYnVmZmVyLndyaXRlKGUsdGhpcy5vZmZzZXQsXCJ1dFxcXG5mLThcIiksdGhpcy5vZmZzZXQrPXR9cmV0dXJuIHRoaXMuYnVmZmVyW3RoaXMub2Zmc2V0KytdPTAsdGhpc31hZGRTdHJpbmcoZT1cIlwiKXt2YXIgdD1kLmJ5dGVMZW5ndGgoZSk7XG5yZXR1cm4gdGhpcy5lbnN1cmUodCksdGhpcy5idWZmZXIud3JpdGUoZSx0aGlzLm9mZnNldCksdGhpcy5vZmZzZXQrPXQsdGhpc31hZGQoZSl7cmV0dXJuIHRoaXMuZW5zdXJlKFxuZS5sZW5ndGgpLGUuY29weSh0aGlzLmJ1ZmZlcix0aGlzLm9mZnNldCksdGhpcy5vZmZzZXQrPWUubGVuZ3RoLHRoaXN9am9pbihlKXtpZihlKXt0aGlzLmJ1ZmZlclt0aGlzLlxuaGVhZGVyUG9zaXRpb25dPWU7bGV0IHQ9dGhpcy5vZmZzZXQtKHRoaXMuaGVhZGVyUG9zaXRpb24rMSk7dGhpcy5idWZmZXIud3JpdGVJbnQzMkJFKHQsdGhpcy5oZWFkZXJQb3NpdGlvbitcbjEpfXJldHVybiB0aGlzLmJ1ZmZlci5zbGljZShlPzA6NSx0aGlzLm9mZnNldCl9Zmx1c2goZSl7dmFyIHQ9dGhpcy5qb2luKGUpO3JldHVybiB0aGlzLm9mZnNldD01LHRoaXMuXG5oZWFkZXJQb3NpdGlvbj0wLHRoaXMuYnVmZmVyPWQuYWxsb2NVbnNhZmUodGhpcy5zaXplKSx0fX07YShjbixcIldyaXRlclwiKTt2YXIgdW49Y247UHQuV3JpdGVyPXVufSk7dmFyIFFzPUkoQnQ9PntcInVzZSBzdHJpY3RcIjtwKCk7T2JqZWN0LmRlZmluZVByb3BlcnR5KEJ0LFwiX19lc01vZHVsZVwiLHt2YWx1ZTohMH0pO0J0LnNlcmlhbGl6ZT12b2lkIDA7XG52YXIgbG49T3MoKSxrPW5ldyBsbi5Xcml0ZXIscGM9YShyPT57ay5hZGRJbnQxNigzKS5hZGRJbnQxNigwKTtmb3IobGV0IG4gb2YgT2JqZWN0LmtleXMocikpay5hZGRDU3RyaW5nKFxubikuYWRkQ1N0cmluZyhyW25dKTtrLmFkZENTdHJpbmcoXCJjbGllbnRfZW5jb2RpbmdcIikuYWRkQ1N0cmluZyhcIlVURjhcIik7dmFyIGU9ay5hZGRDU3RyaW5nKFwiXCIpLmZsdXNoKCksXG50PWUubGVuZ3RoKzQ7cmV0dXJuIG5ldyBsbi5Xcml0ZXIoKS5hZGRJbnQzMih0KS5hZGQoZSkuZmx1c2goKX0sXCJzdGFydHVwXCIpLGRjPWEoKCk9PntsZXQgcj1kLmFsbG9jVW5zYWZlKFxuOCk7cmV0dXJuIHIud3JpdGVJbnQzMkJFKDgsMCksci53cml0ZUludDMyQkUoODA4NzcxMDMsNCkscn0sXCJyZXF1ZXN0U3NsXCIpLHljPWEocj0+ay5hZGRDU3RyaW5nKHIpLmZsdXNoKFxuMTEyKSxcInBhc3N3b3JkXCIpLG1jPWEoZnVuY3Rpb24ocixlKXtyZXR1cm4gay5hZGRDU3RyaW5nKHIpLmFkZEludDMyKGQuYnl0ZUxlbmd0aChlKSkuYWRkU3RyaW5nKGUpLGsuXG5mbHVzaCgxMTIpfSxcInNlbmRTQVNMSW5pdGlhbFJlc3BvbnNlTWVzc2FnZVwiKSxnYz1hKGZ1bmN0aW9uKHIpe3JldHVybiBrLmFkZFN0cmluZyhyKS5mbHVzaCgxMTIpfSxcInNlXFxcbm5kU0NSQU1DbGllbnRGaW5hbE1lc3NhZ2VcIiksd2M9YShyPT5rLmFkZENTdHJpbmcocikuZmx1c2goODEpLFwicXVlcnlcIikscXM9W10sYmM9YShyPT57bGV0IGU9ci5uYW1lfHxcblwiXCI7ZS5sZW5ndGg+NjMmJihjb25zb2xlLmVycm9yKFwiV2FybmluZyEgUG9zdGdyZXMgb25seSBzdXBwb3J0cyA2MyBjaGFyYWN0ZXJzIGZvciBxdWVyeSBuYW1lcy5cIiksY29uc29sZS5cbmVycm9yKFwiWW91IHN1cHBsaWVkICVzICglcylcIixlLGUubGVuZ3RoKSxjb25zb2xlLmVycm9yKFwiVGhpcyBjYW4gY2F1c2UgY29uZmxpY3RzIGFuZCBzaWxlbnQgZXJyb3JzIGVcXFxueGVjdXRpbmcgcXVlcmllc1wiKSk7bGV0IHQ9ci50eXBlc3x8cXM7Zm9yKHZhciBuPXQubGVuZ3RoLGk9ay5hZGRDU3RyaW5nKGUpLmFkZENTdHJpbmcoci50ZXh0KS5hZGRJbnQxNihcbm4pLHM9MDtzPG47cysrKWkuYWRkSW50MzIodFtzXSk7cmV0dXJuIGsuZmx1c2goODApfSxcInBhcnNlXCIpLE9lPW5ldyBsbi5Xcml0ZXIsdmM9YShmdW5jdGlvbihyLGUpe2ZvcihsZXQgdD0wO3Q8XG5yLmxlbmd0aDt0Kyspe2xldCBuPWU/ZShyW3RdLHQpOnJbdF07bj09bnVsbD8oay5hZGRJbnQxNigwKSxPZS5hZGRJbnQzMigtMSkpOm4gaW5zdGFuY2VvZiBkPyhrLmFkZEludDE2KFxuMSksT2UuYWRkSW50MzIobi5sZW5ndGgpLE9lLmFkZChuKSk6KGsuYWRkSW50MTYoMCksT2UuYWRkSW50MzIoZC5ieXRlTGVuZ3RoKG4pKSxPZS5hZGRTdHJpbmcobikpfX0sXCJcXFxud3JpdGVWYWx1ZXNcIikseGM9YSgocj17fSk9PntsZXQgZT1yLnBvcnRhbHx8XCJcIix0PXIuc3RhdGVtZW50fHxcIlwiLG49ci5iaW5hcnl8fCExLGk9ci52YWx1ZXN8fHFzLHM9aS5sZW5ndGg7XG5yZXR1cm4gay5hZGRDU3RyaW5nKGUpLmFkZENTdHJpbmcodCksay5hZGRJbnQxNihzKSx2YyhpLHIudmFsdWVNYXBwZXIpLGsuYWRkSW50MTYocyksay5hZGQoT2UuZmx1c2goKSksXG5rLmFkZEludDE2KG4/MTowKSxrLmZsdXNoKDY2KX0sXCJiaW5kXCIpLFNjPWQuZnJvbShbNjksMCwwLDAsOSwwLDAsMCwwLDBdKSxFYz1hKHI9PntpZighcnx8IXIucG9ydGFsJiZcbiFyLnJvd3MpcmV0dXJuIFNjO2xldCBlPXIucG9ydGFsfHxcIlwiLHQ9ci5yb3dzfHwwLG49ZC5ieXRlTGVuZ3RoKGUpLGk9NCtuKzErNCxzPWQuYWxsb2NVbnNhZmUoMStpKTtyZXR1cm4gc1swXT1cbjY5LHMud3JpdGVJbnQzMkJFKGksMSkscy53cml0ZShlLDUsXCJ1dGYtOFwiKSxzW24rNV09MCxzLndyaXRlVUludDMyQkUodCxzLmxlbmd0aC00KSxzfSxcImV4ZWN1dGVcIiksQWM9YShcbihyLGUpPT57bGV0IHQ9ZC5hbGxvY1Vuc2FmZSgxNik7cmV0dXJuIHQud3JpdGVJbnQzMkJFKDE2LDApLHQud3JpdGVJbnQxNkJFKDEyMzQsNCksdC53cml0ZUludDE2QkUoNTY3OCxcbjYpLHQud3JpdGVJbnQzMkJFKHIsOCksdC53cml0ZUludDMyQkUoZSwxMiksdH0sXCJjYW5jZWxcIiksZm49YSgocixlKT0+e2xldCBuPTQrZC5ieXRlTGVuZ3RoKGUpKzEsaT1kLlxuYWxsb2NVbnNhZmUoMStuKTtyZXR1cm4gaVswXT1yLGkud3JpdGVJbnQzMkJFKG4sMSksaS53cml0ZShlLDUsXCJ1dGYtOFwiKSxpW25dPTAsaX0sXCJjc3RyaW5nTWVzc2FnZVwiKSxcbkNjPWsuYWRkQ1N0cmluZyhcIlBcIikuZmx1c2goNjgpLF9jPWsuYWRkQ1N0cmluZyhcIlNcIikuZmx1c2goNjgpLFRjPWEocj0+ci5uYW1lP2ZuKDY4LGAke3IudHlwZX0ke3IubmFtZXx8XG5cIlwifWApOnIudHlwZT09PVwiUFwiP0NjOl9jLFwiZGVzY3JpYmVcIiksSWM9YShyPT57bGV0IGU9YCR7ci50eXBlfSR7ci5uYW1lfHxcIlwifWA7cmV0dXJuIGZuKDY3LGUpfSxcImNsb3NlXCIpLFxuUGM9YShyPT5rLmFkZChyKS5mbHVzaCgxMDApLFwiY29weURhdGFcIiksUmM9YShyPT5mbigxMDIsciksXCJjb3B5RmFpbFwiKSxSdD1hKHI9PmQuZnJvbShbciwwLDAsMCw0XSksXCJjXFxcbm9kZU9ubHlCdWZmZXJcIiksQmM9UnQoNzIpLExjPVJ0KDgzKSxGYz1SdCg4OCksa2M9UnQoOTkpLE1jPXtzdGFydHVwOnBjLHBhc3N3b3JkOnljLHJlcXVlc3RTc2w6ZGMsc2VuZFNBU0xJbml0aWFsUmVzcG9uc2VNZXNzYWdlOm1jLFxuc2VuZFNDUkFNQ2xpZW50RmluYWxNZXNzYWdlOmdjLHF1ZXJ5OndjLHBhcnNlOmJjLGJpbmQ6eGMsZXhlY3V0ZTpFYyxkZXNjcmliZTpUYyxjbG9zZTpJYyxmbHVzaDphKCgpPT5CYyxcblwiZmx1c2hcIiksc3luYzphKCgpPT5MYyxcInN5bmNcIiksZW5kOmEoKCk9PkZjLFwiZW5kXCIpLGNvcHlEYXRhOlBjLGNvcHlEb25lOmEoKCk9PmtjLFwiY29weURvbmVcIiksY29weUZhaWw6UmMsXG5jYW5jZWw6QWN9O0J0LnNlcmlhbGl6ZT1NY30pO3ZhciBOcz1JKEx0PT57XCJ1c2Ugc3RyaWN0XCI7cCgpO09iamVjdC5kZWZpbmVQcm9wZXJ0eShMdCxcIl9fZXNNb2R1bGVcIix7dmFsdWU6ITB9KTtMdC5CdWZmZXJSZWFkZXI9dm9pZCAwO1xudmFyIFVjPWQuYWxsb2NVbnNhZmUoMCkscG49Y2xhc3MgcG57Y29uc3RydWN0b3IoZT0wKXt0aGlzLm9mZnNldD1lLHRoaXMuYnVmZmVyPVVjLHRoaXMuZW5jb2Rpbmc9XCJ1dGZcXFxuLThcIn1zZXRCdWZmZXIoZSx0KXt0aGlzLm9mZnNldD1lLHRoaXMuYnVmZmVyPXR9aW50MTYoKXtsZXQgZT10aGlzLmJ1ZmZlci5yZWFkSW50MTZCRSh0aGlzLm9mZnNldCk7cmV0dXJuIHRoaXMuXG5vZmZzZXQrPTIsZX1ieXRlKCl7bGV0IGU9dGhpcy5idWZmZXJbdGhpcy5vZmZzZXRdO3JldHVybiB0aGlzLm9mZnNldCsrLGV9aW50MzIoKXtsZXQgZT10aGlzLmJ1ZmZlci5yZWFkSW50MzJCRShcbnRoaXMub2Zmc2V0KTtyZXR1cm4gdGhpcy5vZmZzZXQrPTQsZX11aW50MzIoKXtsZXQgZT10aGlzLmJ1ZmZlci5yZWFkVUludDMyQkUodGhpcy5vZmZzZXQpO3JldHVybiB0aGlzLlxub2Zmc2V0Kz00LGV9c3RyaW5nKGUpe2xldCB0PXRoaXMuYnVmZmVyLnRvU3RyaW5nKHRoaXMuZW5jb2RpbmcsdGhpcy5vZmZzZXQsdGhpcy5vZmZzZXQrZSk7cmV0dXJuIHRoaXMuXG5vZmZzZXQrPWUsdH1jc3RyaW5nKCl7bGV0IGU9dGhpcy5vZmZzZXQsdD1lO2Zvcig7dGhpcy5idWZmZXJbdCsrXSE9PTA7KTtyZXR1cm4gdGhpcy5vZmZzZXQ9dCx0aGlzLmJ1ZmZlci5cbnRvU3RyaW5nKHRoaXMuZW5jb2RpbmcsZSx0LTEpfWJ5dGVzKGUpe2xldCB0PXRoaXMuYnVmZmVyLnNsaWNlKHRoaXMub2Zmc2V0LHRoaXMub2Zmc2V0K2UpO3JldHVybiB0aGlzLlxub2Zmc2V0Kz1lLHR9fTthKHBuLFwiQnVmZmVyUmVhZGVyXCIpO3ZhciBobj1wbjtMdC5CdWZmZXJSZWFkZXI9aG59KTt2YXIgSHM9SShGdD0+e1widXNlIHN0cmljdFwiO3AoKTtPYmplY3QuZGVmaW5lUHJvcGVydHkoRnQsXCJfX2VzTW9kdWxlXCIse3ZhbHVlOiEwfSk7RnQuUGFyc2VyPXZvaWQgMDt2YXIgTT1hbigpLFxuRGM9TnMoKSxkbj0xLE9jPTQsanM9ZG4rT2MsV3M9ZC5hbGxvY1Vuc2FmZSgwKSxtbj1jbGFzcyBtbntjb25zdHJ1Y3RvcihlKXtpZih0aGlzLmJ1ZmZlcj1Xcyx0aGlzLmJ1ZmZlckxlbmd0aD1cbjAsdGhpcy5idWZmZXJPZmZzZXQ9MCx0aGlzLnJlYWRlcj1uZXcgRGMuQnVmZmVyUmVhZGVyLGU/Lm1vZGU9PT1cImJpbmFyeVwiKXRocm93IG5ldyBFcnJvcihcIkJpbmFyeSBtb2RcXFxuZSBub3Qgc3VwcG9ydGVkIHlldFwiKTt0aGlzLm1vZGU9ZT8ubW9kZXx8XCJ0ZXh0XCJ9cGFyc2UoZSx0KXt0aGlzLm1lcmdlQnVmZmVyKGUpO2xldCBuPXRoaXMuYnVmZmVyT2Zmc2V0K1xudGhpcy5idWZmZXJMZW5ndGgsaT10aGlzLmJ1ZmZlck9mZnNldDtmb3IoO2kranM8PW47KXtsZXQgcz10aGlzLmJ1ZmZlcltpXSxvPXRoaXMuYnVmZmVyLnJlYWRVSW50MzJCRShcbmkrZG4pLHU9ZG4rbztpZih1K2k8PW4pe2xldCBjPXRoaXMuaGFuZGxlUGFja2V0KGkranMscyxvLHRoaXMuYnVmZmVyKTt0KGMpLGkrPXV9ZWxzZSBicmVha31pPT09bj8odGhpcy5cbmJ1ZmZlcj1Xcyx0aGlzLmJ1ZmZlckxlbmd0aD0wLHRoaXMuYnVmZmVyT2Zmc2V0PTApOih0aGlzLmJ1ZmZlckxlbmd0aD1uLWksdGhpcy5idWZmZXJPZmZzZXQ9aSl9bWVyZ2VCdWZmZXIoZSl7XG5pZih0aGlzLmJ1ZmZlckxlbmd0aD4wKXtsZXQgdD10aGlzLmJ1ZmZlckxlbmd0aCtlLmJ5dGVMZW5ndGg7aWYodCt0aGlzLmJ1ZmZlck9mZnNldD50aGlzLmJ1ZmZlci5ieXRlTGVuZ3RoKXtcbmxldCBpO2lmKHQ8PXRoaXMuYnVmZmVyLmJ5dGVMZW5ndGgmJnRoaXMuYnVmZmVyT2Zmc2V0Pj10aGlzLmJ1ZmZlckxlbmd0aClpPXRoaXMuYnVmZmVyO2Vsc2V7bGV0IHM9dGhpcy5cbmJ1ZmZlci5ieXRlTGVuZ3RoKjI7Zm9yKDt0Pj1zOylzKj0yO2k9ZC5hbGxvY1Vuc2FmZShzKX10aGlzLmJ1ZmZlci5jb3B5KGksMCx0aGlzLmJ1ZmZlck9mZnNldCx0aGlzLmJ1ZmZlck9mZnNldCtcbnRoaXMuYnVmZmVyTGVuZ3RoKSx0aGlzLmJ1ZmZlcj1pLHRoaXMuYnVmZmVyT2Zmc2V0PTB9ZS5jb3B5KHRoaXMuYnVmZmVyLHRoaXMuYnVmZmVyT2Zmc2V0K3RoaXMuYnVmZmVyTGVuZ3RoKSxcbnRoaXMuYnVmZmVyTGVuZ3RoPXR9ZWxzZSB0aGlzLmJ1ZmZlcj1lLHRoaXMuYnVmZmVyT2Zmc2V0PTAsdGhpcy5idWZmZXJMZW5ndGg9ZS5ieXRlTGVuZ3RofWhhbmRsZVBhY2tldChlLHQsbixpKXtcbnN3aXRjaCh0KXtjYXNlIDUwOnJldHVybiBNLmJpbmRDb21wbGV0ZTtjYXNlIDQ5OnJldHVybiBNLnBhcnNlQ29tcGxldGU7Y2FzZSA1MTpyZXR1cm4gTS5jbG9zZUNvbXBsZXRlO2Nhc2UgMTEwOlxucmV0dXJuIE0ubm9EYXRhO2Nhc2UgMTE1OnJldHVybiBNLnBvcnRhbFN1c3BlbmRlZDtjYXNlIDk5OnJldHVybiBNLmNvcHlEb25lO2Nhc2UgODc6cmV0dXJuIE0ucmVwbGljYXRpb25TdGFydDtjYXNlIDczOlxucmV0dXJuIE0uZW1wdHlRdWVyeTtjYXNlIDY4OnJldHVybiB0aGlzLnBhcnNlRGF0YVJvd01lc3NhZ2UoZSxuLGkpO2Nhc2UgNjc6cmV0dXJuIHRoaXMucGFyc2VDb21tYW5kQ29tcGxldGVNZXNzYWdlKFxuZSxuLGkpO2Nhc2UgOTA6cmV0dXJuIHRoaXMucGFyc2VSZWFkeUZvclF1ZXJ5TWVzc2FnZShlLG4saSk7Y2FzZSA2NTpyZXR1cm4gdGhpcy5wYXJzZU5vdGlmaWNhdGlvbk1lc3NhZ2UoXG5lLG4saSk7Y2FzZSA4MjpyZXR1cm4gdGhpcy5wYXJzZUF1dGhlbnRpY2F0aW9uUmVzcG9uc2UoZSxuLGkpO2Nhc2UgODM6cmV0dXJuIHRoaXMucGFyc2VQYXJhbWV0ZXJTdGF0dXNNZXNzYWdlKFxuZSxuLGkpO2Nhc2UgNzU6cmV0dXJuIHRoaXMucGFyc2VCYWNrZW5kS2V5RGF0YShlLG4saSk7Y2FzZSA2OTpyZXR1cm4gdGhpcy5wYXJzZUVycm9yTWVzc2FnZShlLG4saSxcImVcXFxucnJvclwiKTtjYXNlIDc4OnJldHVybiB0aGlzLnBhcnNlRXJyb3JNZXNzYWdlKGUsbixpLFwibm90aWNlXCIpO2Nhc2UgODQ6cmV0dXJuIHRoaXMucGFyc2VSb3dEZXNjcmlwdGlvbk1lc3NhZ2UoXG5lLG4saSk7Y2FzZSAxMTY6cmV0dXJuIHRoaXMucGFyc2VQYXJhbWV0ZXJEZXNjcmlwdGlvbk1lc3NhZ2UoZSxuLGkpO2Nhc2UgNzE6cmV0dXJuIHRoaXMucGFyc2VDb3B5SW5NZXNzYWdlKFxuZSxuLGkpO2Nhc2UgNzI6cmV0dXJuIHRoaXMucGFyc2VDb3B5T3V0TWVzc2FnZShlLG4saSk7Y2FzZSAxMDA6cmV0dXJuIHRoaXMucGFyc2VDb3B5RGF0YShlLG4saSk7ZGVmYXVsdDpcbnJldHVybiBuZXcgTS5EYXRhYmFzZUVycm9yKFwicmVjZWl2ZWQgaW52YWxpZCByZXNwb25zZTogXCIrdC50b1N0cmluZygxNiksbixcImVycm9yXCIpfX1wYXJzZVJlYWR5Rm9yUXVlcnlNZXNzYWdlKGUsdCxuKXtcbnRoaXMucmVhZGVyLnNldEJ1ZmZlcihlLG4pO2xldCBpPXRoaXMucmVhZGVyLnN0cmluZygxKTtyZXR1cm4gbmV3IE0uUmVhZHlGb3JRdWVyeU1lc3NhZ2UodCxpKX1wYXJzZUNvbW1hbmRDb21wbGV0ZU1lc3NhZ2UoZSx0LG4pe1xudGhpcy5yZWFkZXIuc2V0QnVmZmVyKGUsbik7bGV0IGk9dGhpcy5yZWFkZXIuY3N0cmluZygpO3JldHVybiBuZXcgTS5Db21tYW5kQ29tcGxldGVNZXNzYWdlKHQsaSl9cGFyc2VDb3B5RGF0YShlLHQsbil7XG5sZXQgaT1uLnNsaWNlKGUsZSsodC00KSk7cmV0dXJuIG5ldyBNLkNvcHlEYXRhTWVzc2FnZSh0LGkpfXBhcnNlQ29weUluTWVzc2FnZShlLHQsbil7cmV0dXJuIHRoaXMucGFyc2VDb3B5TWVzc2FnZShcbmUsdCxuLFwiY29weUluUmVzcG9uc2VcIil9cGFyc2VDb3B5T3V0TWVzc2FnZShlLHQsbil7cmV0dXJuIHRoaXMucGFyc2VDb3B5TWVzc2FnZShlLHQsbixcImNvcHlPdXRSZXNwb25cXFxuc2VcIil9cGFyc2VDb3B5TWVzc2FnZShlLHQsbixpKXt0aGlzLnJlYWRlci5zZXRCdWZmZXIoZSxuKTtsZXQgcz10aGlzLnJlYWRlci5ieXRlKCkhPT0wLG89dGhpcy5yZWFkZXIuXG5pbnQxNigpLHU9bmV3IE0uQ29weVJlc3BvbnNlKHQsaSxzLG8pO2ZvcihsZXQgYz0wO2M8bztjKyspdS5jb2x1bW5UeXBlc1tjXT10aGlzLnJlYWRlci5pbnQxNigpO3JldHVybiB1fXBhcnNlTm90aWZpY2F0aW9uTWVzc2FnZShlLHQsbil7XG50aGlzLnJlYWRlci5zZXRCdWZmZXIoZSxuKTtsZXQgaT10aGlzLnJlYWRlci5pbnQzMigpLHM9dGhpcy5yZWFkZXIuY3N0cmluZygpLG89dGhpcy5yZWFkZXIuY3N0cmluZygpO1xucmV0dXJuIG5ldyBNLk5vdGlmaWNhdGlvblJlc3BvbnNlTWVzc2FnZSh0LGkscyxvKX1wYXJzZVJvd0Rlc2NyaXB0aW9uTWVzc2FnZShlLHQsbil7dGhpcy5yZWFkZXIuc2V0QnVmZmVyKFxuZSxuKTtsZXQgaT10aGlzLnJlYWRlci5pbnQxNigpLHM9bmV3IE0uUm93RGVzY3JpcHRpb25NZXNzYWdlKHQsaSk7Zm9yKGxldCBvPTA7bzxpO28rKylzLmZpZWxkc1tvXT10aGlzLlxucGFyc2VGaWVsZCgpO3JldHVybiBzfXBhcnNlRmllbGQoKXtsZXQgZT10aGlzLnJlYWRlci5jc3RyaW5nKCksdD10aGlzLnJlYWRlci51aW50MzIoKSxuPXRoaXMucmVhZGVyLlxuaW50MTYoKSxpPXRoaXMucmVhZGVyLnVpbnQzMigpLHM9dGhpcy5yZWFkZXIuaW50MTYoKSxvPXRoaXMucmVhZGVyLmludDMyKCksdT10aGlzLnJlYWRlci5pbnQxNigpPT09MD9cblwidGV4dFwiOlwiYmluYXJ5XCI7cmV0dXJuIG5ldyBNLkZpZWxkKGUsdCxuLGkscyxvLHUpfXBhcnNlUGFyYW1ldGVyRGVzY3JpcHRpb25NZXNzYWdlKGUsdCxuKXt0aGlzLnJlYWRlci5cbnNldEJ1ZmZlcihlLG4pO2xldCBpPXRoaXMucmVhZGVyLmludDE2KCkscz1uZXcgTS5QYXJhbWV0ZXJEZXNjcmlwdGlvbk1lc3NhZ2UodCxpKTtmb3IobGV0IG89MDtvPGk7bysrKVxucy5kYXRhVHlwZUlEc1tvXT10aGlzLnJlYWRlci5pbnQzMigpO3JldHVybiBzfXBhcnNlRGF0YVJvd01lc3NhZ2UoZSx0LG4pe3RoaXMucmVhZGVyLnNldEJ1ZmZlcihlLG4pO1xubGV0IGk9dGhpcy5yZWFkZXIuaW50MTYoKSxzPW5ldyBBcnJheShpKTtmb3IobGV0IG89MDtvPGk7bysrKXtsZXQgdT10aGlzLnJlYWRlci5pbnQzMigpO3Nbb109dT09PS0xP1xubnVsbDp0aGlzLnJlYWRlci5zdHJpbmcodSl9cmV0dXJuIG5ldyBNLkRhdGFSb3dNZXNzYWdlKHQscyl9cGFyc2VQYXJhbWV0ZXJTdGF0dXNNZXNzYWdlKGUsdCxuKXt0aGlzLlxucmVhZGVyLnNldEJ1ZmZlcihlLG4pO2xldCBpPXRoaXMucmVhZGVyLmNzdHJpbmcoKSxzPXRoaXMucmVhZGVyLmNzdHJpbmcoKTtyZXR1cm4gbmV3IE0uUGFyYW1ldGVyU3RhdHVzTWVzc2FnZShcbnQsaSxzKX1wYXJzZUJhY2tlbmRLZXlEYXRhKGUsdCxuKXt0aGlzLnJlYWRlci5zZXRCdWZmZXIoZSxuKTtsZXQgaT10aGlzLnJlYWRlci5pbnQzMigpLHM9dGhpcy5yZWFkZXIuXG5pbnQzMigpO3JldHVybiBuZXcgTS5CYWNrZW5kS2V5RGF0YU1lc3NhZ2UodCxpLHMpfXBhcnNlQXV0aGVudGljYXRpb25SZXNwb25zZShlLHQsbil7dGhpcy5yZWFkZXIuc2V0QnVmZmVyKFxuZSxuKTtsZXQgaT10aGlzLnJlYWRlci5pbnQzMigpLHM9e25hbWU6XCJhdXRoZW50aWNhdGlvbk9rXCIsbGVuZ3RoOnR9O3N3aXRjaChpKXtjYXNlIDA6YnJlYWs7Y2FzZSAzOnMuXG5sZW5ndGg9PT04JiYocy5uYW1lPVwiYXV0aGVudGljYXRpb25DbGVhcnRleHRQYXNzd29yZFwiKTticmVhaztjYXNlIDU6aWYocy5sZW5ndGg9PT0xMil7cy5uYW1lPVwiYXV0aGVuXFxcbnRpY2F0aW9uTUQ1UGFzc3dvcmRcIjtsZXQgdT10aGlzLnJlYWRlci5ieXRlcyg0KTtyZXR1cm4gbmV3IE0uQXV0aGVudGljYXRpb25NRDVQYXNzd29yZCh0LHUpfWJyZWFrO2Nhc2UgMTA6XG5zLm5hbWU9XCJhdXRoZW50aWNhdGlvblNBU0xcIixzLm1lY2hhbmlzbXM9W107bGV0IG87ZG8gbz10aGlzLnJlYWRlci5jc3RyaW5nKCksbyYmcy5tZWNoYW5pc21zLnB1c2gobyk7d2hpbGUobyk7XG5icmVhaztjYXNlIDExOnMubmFtZT1cImF1dGhlbnRpY2F0aW9uU0FTTENvbnRpbnVlXCIscy5kYXRhPXRoaXMucmVhZGVyLnN0cmluZyh0LTgpO2JyZWFrO2Nhc2UgMTI6cy5uYW1lPVxuXCJhdXRoZW50aWNhdGlvblNBU0xGaW5hbFwiLHMuZGF0YT10aGlzLnJlYWRlci5zdHJpbmcodC04KTticmVhaztkZWZhdWx0OnRocm93IG5ldyBFcnJvcihcIlVua25vd24gYXV0aFxcXG5lbnRpY2F0aW9uT2sgbWVzc2FnZSB0eXBlIFwiK2kpfXJldHVybiBzfXBhcnNlRXJyb3JNZXNzYWdlKGUsdCxuLGkpe3RoaXMucmVhZGVyLnNldEJ1ZmZlcihlLG4pO2xldCBzPXt9LFxubz10aGlzLnJlYWRlci5zdHJpbmcoMSk7Zm9yKDtvIT09XCJcXDBcIjspc1tvXT10aGlzLnJlYWRlci5jc3RyaW5nKCksbz10aGlzLnJlYWRlci5zdHJpbmcoMSk7bGV0IHU9cy5NLFxuYz1pPT09XCJub3RpY2VcIj9uZXcgTS5Ob3RpY2VNZXNzYWdlKHQsdSk6bmV3IE0uRGF0YWJhc2VFcnJvcih1LHQsaSk7cmV0dXJuIGMuc2V2ZXJpdHk9cy5TLGMuY29kZT1zLkMsXG5jLmRldGFpbD1zLkQsYy5oaW50PXMuSCxjLnBvc2l0aW9uPXMuUCxjLmludGVybmFsUG9zaXRpb249cy5wLGMuaW50ZXJuYWxRdWVyeT1zLnEsYy53aGVyZT1zLlcsYy5zY2hlbWE9XG5zLnMsYy50YWJsZT1zLnQsYy5jb2x1bW49cy5jLGMuZGF0YVR5cGU9cy5kLGMuY29uc3RyYWludD1zLm4sYy5maWxlPXMuRixjLmxpbmU9cy5MLGMucm91dGluZT1zLlIsY319O1xuYShtbixcIlBhcnNlclwiKTt2YXIgeW49bW47RnQuUGFyc2VyPXlufSk7dmFyIGduPUkodmU9PntcInVzZSBzdHJpY3RcIjtwKCk7T2JqZWN0LmRlZmluZVByb3BlcnR5KHZlLFwiX19lc01vZHVsZVwiLHt2YWx1ZTohMH0pO3ZlLkRhdGFiYXNlRXJyb3I9dmUuXG5zZXJpYWxpemU9dmUucGFyc2U9dm9pZCAwO3ZhciBxYz1hbigpO09iamVjdC5kZWZpbmVQcm9wZXJ0eSh2ZSxcIkRhdGFiYXNlRXJyb3JcIix7ZW51bWVyYWJsZTohMCxnZXQ6YShcbmZ1bmN0aW9uKCl7cmV0dXJuIHFjLkRhdGFiYXNlRXJyb3J9LFwiZ2V0XCIpfSk7dmFyIFFjPVFzKCk7T2JqZWN0LmRlZmluZVByb3BlcnR5KHZlLFwic2VyaWFsaXplXCIse2VudW1lcmFibGU6ITAsXG5nZXQ6YShmdW5jdGlvbigpe3JldHVybiBRYy5zZXJpYWxpemV9LFwiZ2V0XCIpfSk7dmFyIE5jPUhzKCk7ZnVuY3Rpb24gamMocixlKXtsZXQgdD1uZXcgTmMuUGFyc2VyO3JldHVybiByLlxub24oXCJkYXRhXCIsbj0+dC5wYXJzZShuLGUpKSxuZXcgUHJvbWlzZShuPT5yLm9uKFwiZW5kXCIsKCk9Pm4oKSkpfWEoamMsXCJwYXJzZVwiKTt2ZS5wYXJzZT1qY30pO3ZhciAkcz17fTtuZSgkcyx7Y29ubmVjdDooKT0+V2N9KTtmdW5jdGlvbiBXYyh7c29ja2V0OnIsc2VydmVybmFtZTplfSl7cmV0dXJuIHIuc3RhcnRUbHMoZSkscn12YXIgR3M9eihcbigpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO2EoV2MsXCJjb25uZWN0XCIpfSk7dmFyIHZuPUkoKEdoLEtzKT0+e1widXNlIHN0cmljdFwiO3AoKTt2YXIgVnM9KFdlKCksRCh5aSkpLEhjPW1lKCkuRXZlbnRFbWl0dGVyLHtwYXJzZTokYyxzZXJpYWxpemU6cX09Z24oKSxcbnpzPXEuZmx1c2goKSxHYz1xLnN5bmMoKSxWYz1xLmVuZCgpLGJuPWNsYXNzIGJuIGV4dGVuZHMgSGN7Y29uc3RydWN0b3IoZSl7c3VwZXIoKSxlPWV8fHt9LHRoaXMuc3RyZWFtPVxuZS5zdHJlYW18fG5ldyBWcy5Tb2NrZXQsdGhpcy5fa2VlcEFsaXZlPWUua2VlcEFsaXZlLHRoaXMuX2tlZXBBbGl2ZUluaXRpYWxEZWxheU1pbGxpcz1lLmtlZXBBbGl2ZUluaXRpYWxEZWxheU1pbGxpcyxcbnRoaXMubGFzdEJ1ZmZlcj0hMSx0aGlzLnBhcnNlZFN0YXRlbWVudHM9e30sdGhpcy5zc2w9ZS5zc2x8fCExLHRoaXMuX2VuZGluZz0hMSx0aGlzLl9lbWl0TWVzc2FnZT0hMTtcbnZhciB0PXRoaXM7dGhpcy5vbihcIm5ld0xpc3RlbmVyXCIsZnVuY3Rpb24obil7bj09PVwibWVzc2FnZVwiJiYodC5fZW1pdE1lc3NhZ2U9ITApfSl9Y29ubmVjdChlLHQpe3ZhciBuPXRoaXM7XG50aGlzLl9jb25uZWN0aW5nPSEwLHRoaXMuc3RyZWFtLnNldE5vRGVsYXkoITApLHRoaXMuc3RyZWFtLmNvbm5lY3QoZSx0KSx0aGlzLnN0cmVhbS5vbmNlKFwiY29ubmVjdFwiLGZ1bmN0aW9uKCl7XG5uLl9rZWVwQWxpdmUmJm4uc3RyZWFtLnNldEtlZXBBbGl2ZSghMCxuLl9rZWVwQWxpdmVJbml0aWFsRGVsYXlNaWxsaXMpLG4uZW1pdChcImNvbm5lY3RcIil9KTtsZXQgaT1hKGZ1bmN0aW9uKHMpe1xubi5fZW5kaW5nJiYocy5jb2RlPT09XCJFQ09OTlJFU0VUXCJ8fHMuY29kZT09PVwiRVBJUEVcIil8fG4uZW1pdChcImVycm9yXCIscyl9LFwicmVwb3J0U3RyZWFtRXJyb3JcIik7aWYodGhpcy5cbnN0cmVhbS5vbihcImVycm9yXCIsaSksdGhpcy5zdHJlYW0ub24oXCJjbG9zZVwiLGZ1bmN0aW9uKCl7bi5lbWl0KFwiZW5kXCIpfSksIXRoaXMuc3NsKXJldHVybiB0aGlzLmF0dGFjaExpc3RlbmVycyhcbnRoaXMuc3RyZWFtKTt0aGlzLnN0cmVhbS5vbmNlKFwiZGF0YVwiLGZ1bmN0aW9uKHMpe3ZhciBvPXMudG9TdHJpbmcoXCJ1dGY4XCIpO3N3aXRjaChvKXtjYXNlXCJTXCI6YnJlYWs7Y2FzZVwiXFxcbk5cIjpyZXR1cm4gbi5zdHJlYW0uZW5kKCksbi5lbWl0KFwiZXJyb3JcIixuZXcgRXJyb3IoXCJUaGUgc2VydmVyIGRvZXMgbm90IHN1cHBvcnQgU1NMIGNvbm5lY3Rpb25zXCIpKTtkZWZhdWx0OlxucmV0dXJuIG4uc3RyZWFtLmVuZCgpLG4uZW1pdChcImVycm9yXCIsbmV3IEVycm9yKFwiVGhlcmUgd2FzIGFuIGVycm9yIGVzdGFibGlzaGluZyBhbiBTU0wgY29ubmVjdGlvblwiKSl9XG52YXIgdT0oR3MoKSxEKCRzKSk7bGV0IGM9e3NvY2tldDpuLnN0cmVhbX07bi5zc2whPT0hMCYmKE9iamVjdC5hc3NpZ24oYyxuLnNzbCksXCJrZXlcImluIG4uc3NsJiYoYy5rZXk9XG5uLnNzbC5rZXkpKSxWcy5pc0lQKHQpPT09MCYmKGMuc2VydmVybmFtZT10KTt0cnl7bi5zdHJlYW09dS5jb25uZWN0KGMpfWNhdGNoKGwpe3JldHVybiBuLmVtaXQoXCJlcnJvclwiLFxubCl9bi5hdHRhY2hMaXN0ZW5lcnMobi5zdHJlYW0pLG4uc3RyZWFtLm9uKFwiZXJyb3JcIixpKSxuLmVtaXQoXCJzc2xjb25uZWN0XCIpfSl9YXR0YWNoTGlzdGVuZXJzKGUpe2Uub24oXG5cImVuZFwiLCgpPT57dGhpcy5lbWl0KFwiZW5kXCIpfSksJGMoZSx0PT57dmFyIG49dC5uYW1lPT09XCJlcnJvclwiP1wiZXJyb3JNZXNzYWdlXCI6dC5uYW1lO3RoaXMuX2VtaXRNZXNzYWdlJiZcbnRoaXMuZW1pdChcIm1lc3NhZ2VcIix0KSx0aGlzLmVtaXQobix0KX0pfXJlcXVlc3RTc2woKXt0aGlzLnN0cmVhbS53cml0ZShxLnJlcXVlc3RTc2woKSl9c3RhcnR1cChlKXt0aGlzLlxuc3RyZWFtLndyaXRlKHEuc3RhcnR1cChlKSl9Y2FuY2VsKGUsdCl7dGhpcy5fc2VuZChxLmNhbmNlbChlLHQpKX1wYXNzd29yZChlKXt0aGlzLl9zZW5kKHEucGFzc3dvcmQoZSkpfXNlbmRTQVNMSW5pdGlhbFJlc3BvbnNlTWVzc2FnZShlLHQpe1xudGhpcy5fc2VuZChxLnNlbmRTQVNMSW5pdGlhbFJlc3BvbnNlTWVzc2FnZShlLHQpKX1zZW5kU0NSQU1DbGllbnRGaW5hbE1lc3NhZ2UoZSl7dGhpcy5fc2VuZChxLnNlbmRTQ1JBTUNsaWVudEZpbmFsTWVzc2FnZShcbmUpKX1fc2VuZChlKXtyZXR1cm4gdGhpcy5zdHJlYW0ud3JpdGFibGU/dGhpcy5zdHJlYW0ud3JpdGUoZSk6ITF9cXVlcnkoZSl7dGhpcy5fc2VuZChxLnF1ZXJ5KGUpKX1wYXJzZShlKXtcbnRoaXMuX3NlbmQocS5wYXJzZShlKSl9YmluZChlKXt0aGlzLl9zZW5kKHEuYmluZChlKSl9ZXhlY3V0ZShlKXt0aGlzLl9zZW5kKHEuZXhlY3V0ZShlKSl9Zmx1c2goKXt0aGlzLlxuc3RyZWFtLndyaXRhYmxlJiZ0aGlzLnN0cmVhbS53cml0ZSh6cyl9c3luYygpe3RoaXMuX2VuZGluZz0hMCx0aGlzLl9zZW5kKHpzKSx0aGlzLl9zZW5kKEdjKX1yZWYoKXt0aGlzLlxuc3RyZWFtLnJlZigpfXVucmVmKCl7dGhpcy5zdHJlYW0udW5yZWYoKX1lbmQoKXtpZih0aGlzLl9lbmRpbmc9ITAsIXRoaXMuX2Nvbm5lY3Rpbmd8fCF0aGlzLnN0cmVhbS53cml0YWJsZSl7XG50aGlzLnN0cmVhbS5lbmQoKTtyZXR1cm59cmV0dXJuIHRoaXMuc3RyZWFtLndyaXRlKFZjLCgpPT57dGhpcy5zdHJlYW0uZW5kKCl9KX1jbG9zZShlKXt0aGlzLl9zZW5kKHEuXG5jbG9zZShlKSl9ZGVzY3JpYmUoZSl7dGhpcy5fc2VuZChxLmRlc2NyaWJlKGUpKX1zZW5kQ29weUZyb21DaHVuayhlKXt0aGlzLl9zZW5kKHEuY29weURhdGEoZSkpfWVuZENvcHlGcm9tKCl7XG50aGlzLl9zZW5kKHEuY29weURvbmUoKSl9c2VuZENvcHlGYWlsKGUpe3RoaXMuX3NlbmQocS5jb3B5RmFpbChlKSl9fTthKGJuLFwiQ29ubmVjdGlvblwiKTt2YXIgd249Ym47S3MuXG5leHBvcnRzPXdufSk7dmFyIEpzPUkoKFloLFpzKT0+e1widXNlIHN0cmljdFwiO3AoKTt2YXIgemM9bWUoKS5FdmVudEVtaXR0ZXIsS2g9KGl0KCksRChudCkpLEtjPXJ0KCkseG49aHMoKSxZYz1FcygpLFxuWmM9U3QoKSxKYz1JdCgpLFlzPURzKCksWGM9dHQoKSxlbD12bigpLFNuPWNsYXNzIFNuIGV4dGVuZHMgemN7Y29uc3RydWN0b3IoZSl7c3VwZXIoKSx0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzPVxubmV3IEpjKGUpLHRoaXMudXNlcj10aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLnVzZXIsdGhpcy5kYXRhYmFzZT10aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLmRhdGFiYXNlLFxudGhpcy5wb3J0PXRoaXMuY29ubmVjdGlvblBhcmFtZXRlcnMucG9ydCx0aGlzLmhvc3Q9dGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycy5ob3N0LE9iamVjdC5kZWZpbmVQcm9wZXJ0eShcbnRoaXMsXCJwYXNzd29yZFwiLHtjb25maWd1cmFibGU6ITAsZW51bWVyYWJsZTohMSx3cml0YWJsZTohMCx2YWx1ZTp0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLnBhc3N3b3JkfSksXG50aGlzLnJlcGxpY2F0aW9uPXRoaXMuY29ubmVjdGlvblBhcmFtZXRlcnMucmVwbGljYXRpb247dmFyIHQ9ZXx8e307dGhpcy5fUHJvbWlzZT10LlByb21pc2V8fHcuUHJvbWlzZSxcbnRoaXMuX3R5cGVzPW5ldyBaYyh0LnR5cGVzKSx0aGlzLl9lbmRpbmc9ITEsdGhpcy5fY29ubmVjdGluZz0hMSx0aGlzLl9jb25uZWN0ZWQ9ITEsdGhpcy5fY29ubmVjdGlvbkVycm9yPVxuITEsdGhpcy5fcXVlcnlhYmxlPSEwLHRoaXMuY29ubmVjdGlvbj10LmNvbm5lY3Rpb258fG5ldyBlbCh7c3RyZWFtOnQuc3RyZWFtLHNzbDp0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLlxuc3NsLGtlZXBBbGl2ZTp0LmtlZXBBbGl2ZXx8ITEsa2VlcEFsaXZlSW5pdGlhbERlbGF5TWlsbGlzOnQua2VlcEFsaXZlSW5pdGlhbERlbGF5TWlsbGlzfHwwLGVuY29kaW5nOnRoaXMuXG5jb25uZWN0aW9uUGFyYW1ldGVycy5jbGllbnRfZW5jb2Rpbmd8fFwidXRmOFwifSksdGhpcy5xdWVyeVF1ZXVlPVtdLHRoaXMuYmluYXJ5PXQuYmluYXJ5fHxYYy5iaW5hcnksdGhpcy5cbnByb2Nlc3NJRD1udWxsLHRoaXMuc2VjcmV0S2V5PW51bGwsdGhpcy5zc2w9dGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycy5zc2x8fCExLHRoaXMuc3NsJiZ0aGlzLnNzbC5rZXkmJlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMuc3NsLFwia2V5XCIse2VudW1lcmFibGU6ITF9KSx0aGlzLl9jb25uZWN0aW9uVGltZW91dE1pbGxpcz10LmNvbm5lY3Rpb25UaW1lb3V0TWlsbGlzfHxcbjB9X2Vycm9yQWxsUXVlcmllcyhlKXtsZXQgdD1hKG49PnttLm5leHRUaWNrKCgpPT57bi5oYW5kbGVFcnJvcihlLHRoaXMuY29ubmVjdGlvbil9KX0sXCJlbnF1ZXVlRXJyb3JcIik7XG50aGlzLmFjdGl2ZVF1ZXJ5JiYodCh0aGlzLmFjdGl2ZVF1ZXJ5KSx0aGlzLmFjdGl2ZVF1ZXJ5PW51bGwpLHRoaXMucXVlcnlRdWV1ZS5mb3JFYWNoKHQpLHRoaXMucXVlcnlRdWV1ZS5cbmxlbmd0aD0wfV9jb25uZWN0KGUpe3ZhciB0PXRoaXMsbj10aGlzLmNvbm5lY3Rpb247aWYodGhpcy5fY29ubmVjdGlvbkNhbGxiYWNrPWUsdGhpcy5fY29ubmVjdGluZ3x8dGhpcy5cbl9jb25uZWN0ZWQpe2xldCBpPW5ldyBFcnJvcihcIkNsaWVudCBoYXMgYWxyZWFkeSBiZWVuIGNvbm5lY3RlZC4gWW91IGNhbm5vdCByZXVzZSBhIGNsaWVudC5cIik7bS5uZXh0VGljayhcbigpPT57ZShpKX0pO3JldHVybn10aGlzLl9jb25uZWN0aW5nPSEwLHRoaXMuY29ubmVjdGlvblRpbWVvdXRIYW5kbGUsdGhpcy5fY29ubmVjdGlvblRpbWVvdXRNaWxsaXM+MCYmXG4odGhpcy5jb25uZWN0aW9uVGltZW91dEhhbmRsZT1zZXRUaW1lb3V0KCgpPT57bi5fZW5kaW5nPSEwLG4uc3RyZWFtLmRlc3Ryb3kobmV3IEVycm9yKFwidGltZW91dCBleHBpclxcXG5lZFwiKSl9LHRoaXMuX2Nvbm5lY3Rpb25UaW1lb3V0TWlsbGlzKSksdGhpcy5ob3N0JiZ0aGlzLmhvc3QuaW5kZXhPZihcIi9cIik9PT0wP24uY29ubmVjdCh0aGlzLmhvc3QrXCIvLlxcXG5zLlBHU1FMLlwiK3RoaXMucG9ydCk6bi5jb25uZWN0KHRoaXMucG9ydCx0aGlzLmhvc3QpLG4ub24oXCJjb25uZWN0XCIsZnVuY3Rpb24oKXt0LnNzbD9uLnJlcXVlc3RTc2woKTpuLlxuc3RhcnR1cCh0LmdldFN0YXJ0dXBDb25mKCkpfSksbi5vbihcInNzbGNvbm5lY3RcIixmdW5jdGlvbigpe24uc3RhcnR1cCh0LmdldFN0YXJ0dXBDb25mKCkpfSksdGhpcy5fYXR0YWNoTGlzdGVuZXJzKFxubiksbi5vbmNlKFwiZW5kXCIsKCk9PntsZXQgaT10aGlzLl9lbmRpbmc/bmV3IEVycm9yKFwiQ29ubmVjdGlvbiB0ZXJtaW5hdGVkXCIpOm5ldyBFcnJvcihcIkNvbm5lY3Rpb24gdGVyXFxcbm1pbmF0ZWQgdW5leHBlY3RlZGx5XCIpO2NsZWFyVGltZW91dCh0aGlzLmNvbm5lY3Rpb25UaW1lb3V0SGFuZGxlKSx0aGlzLl9lcnJvckFsbFF1ZXJpZXMoaSksdGhpcy5fZW5kaW5nfHxcbih0aGlzLl9jb25uZWN0aW5nJiYhdGhpcy5fY29ubmVjdGlvbkVycm9yP3RoaXMuX2Nvbm5lY3Rpb25DYWxsYmFjaz90aGlzLl9jb25uZWN0aW9uQ2FsbGJhY2soaSk6dGhpcy5cbl9oYW5kbGVFcnJvckV2ZW50KGkpOnRoaXMuX2Nvbm5lY3Rpb25FcnJvcnx8dGhpcy5faGFuZGxlRXJyb3JFdmVudChpKSksbS5uZXh0VGljaygoKT0+e3RoaXMuZW1pdChcImVuXFxcbmRcIil9KX0pfWNvbm5lY3QoZSl7aWYoZSl7dGhpcy5fY29ubmVjdChlKTtyZXR1cm59cmV0dXJuIG5ldyB0aGlzLl9Qcm9taXNlKCh0LG4pPT57dGhpcy5fY29ubmVjdChpPT57XG5pP24oaSk6dCgpfSl9KX1fYXR0YWNoTGlzdGVuZXJzKGUpe2Uub24oXCJhdXRoZW50aWNhdGlvbkNsZWFydGV4dFBhc3N3b3JkXCIsdGhpcy5faGFuZGxlQXV0aENsZWFydGV4dFBhc3N3b3JkLlxuYmluZCh0aGlzKSksZS5vbihcImF1dGhlbnRpY2F0aW9uTUQ1UGFzc3dvcmRcIix0aGlzLl9oYW5kbGVBdXRoTUQ1UGFzc3dvcmQuYmluZCh0aGlzKSksZS5vbihcImF1dGhlbnRpY1xcXG5hdGlvblNBU0xcIix0aGlzLl9oYW5kbGVBdXRoU0FTTC5iaW5kKHRoaXMpKSxlLm9uKFwiYXV0aGVudGljYXRpb25TQVNMQ29udGludWVcIix0aGlzLl9oYW5kbGVBdXRoU0FTTENvbnRpbnVlLlxuYmluZCh0aGlzKSksZS5vbihcImF1dGhlbnRpY2F0aW9uU0FTTEZpbmFsXCIsdGhpcy5faGFuZGxlQXV0aFNBU0xGaW5hbC5iaW5kKHRoaXMpKSxlLm9uKFwiYmFja2VuZEtleURhdFxcXG5hXCIsdGhpcy5faGFuZGxlQmFja2VuZEtleURhdGEuYmluZCh0aGlzKSksZS5vbihcImVycm9yXCIsdGhpcy5faGFuZGxlRXJyb3JFdmVudC5iaW5kKHRoaXMpKSxlLm9uKFwiZXJyb1xcXG5yTWVzc2FnZVwiLHRoaXMuX2hhbmRsZUVycm9yTWVzc2FnZS5iaW5kKHRoaXMpKSxlLm9uKFwicmVhZHlGb3JRdWVyeVwiLHRoaXMuX2hhbmRsZVJlYWR5Rm9yUXVlcnkuYmluZCh0aGlzKSksXG5lLm9uKFwibm90aWNlXCIsdGhpcy5faGFuZGxlTm90aWNlLmJpbmQodGhpcykpLGUub24oXCJyb3dEZXNjcmlwdGlvblwiLHRoaXMuX2hhbmRsZVJvd0Rlc2NyaXB0aW9uLmJpbmQodGhpcykpLFxuZS5vbihcImRhdGFSb3dcIix0aGlzLl9oYW5kbGVEYXRhUm93LmJpbmQodGhpcykpLGUub24oXCJwb3J0YWxTdXNwZW5kZWRcIix0aGlzLl9oYW5kbGVQb3J0YWxTdXNwZW5kZWQuYmluZChcbnRoaXMpKSxlLm9uKFwiZW1wdHlRdWVyeVwiLHRoaXMuX2hhbmRsZUVtcHR5UXVlcnkuYmluZCh0aGlzKSksZS5vbihcImNvbW1hbmRDb21wbGV0ZVwiLHRoaXMuX2hhbmRsZUNvbW1hbmRDb21wbGV0ZS5cbmJpbmQodGhpcykpLGUub24oXCJwYXJzZUNvbXBsZXRlXCIsdGhpcy5faGFuZGxlUGFyc2VDb21wbGV0ZS5iaW5kKHRoaXMpKSxlLm9uKFwiY29weUluUmVzcG9uc2VcIix0aGlzLl9oYW5kbGVDb3B5SW5SZXNwb25zZS5cbmJpbmQodGhpcykpLGUub24oXCJjb3B5RGF0YVwiLHRoaXMuX2hhbmRsZUNvcHlEYXRhLmJpbmQodGhpcykpLGUub24oXCJub3RpZmljYXRpb25cIix0aGlzLl9oYW5kbGVOb3RpZmljYXRpb24uXG5iaW5kKHRoaXMpKX1fY2hlY2tQZ1Bhc3MoZSl7bGV0IHQ9dGhpcy5jb25uZWN0aW9uO3R5cGVvZiB0aGlzLnBhc3N3b3JkPT1cImZ1bmN0aW9uXCI/dGhpcy5fUHJvbWlzZS5yZXNvbHZlKCkuXG50aGVuKCgpPT50aGlzLnBhc3N3b3JkKCkpLnRoZW4obj0+e2lmKG4hPT12b2lkIDApe2lmKHR5cGVvZiBuIT1cInN0cmluZ1wiKXt0LmVtaXQoXCJlcnJvclwiLG5ldyBUeXBlRXJyb3IoXG5cIlBhc3N3b3JkIG11c3QgYmUgYSBzdHJpbmdcIikpO3JldHVybn10aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLnBhc3N3b3JkPXRoaXMucGFzc3dvcmQ9bn1lbHNlIHRoaXMuY29ubmVjdGlvblBhcmFtZXRlcnMuXG5wYXNzd29yZD10aGlzLnBhc3N3b3JkPW51bGw7ZSgpfSkuY2F0Y2gobj0+e3QuZW1pdChcImVycm9yXCIsbil9KTp0aGlzLnBhc3N3b3JkIT09bnVsbD9lKCk6WWModGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycyxcbm49PntuIT09dm9pZCAwJiYodGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycy5wYXNzd29yZD10aGlzLnBhc3N3b3JkPW4pLGUoKX0pfV9oYW5kbGVBdXRoQ2xlYXJ0ZXh0UGFzc3dvcmQoZSl7XG50aGlzLl9jaGVja1BnUGFzcygoKT0+e3RoaXMuY29ubmVjdGlvbi5wYXNzd29yZCh0aGlzLnBhc3N3b3JkKX0pfV9oYW5kbGVBdXRoTUQ1UGFzc3dvcmQoZSl7dGhpcy5fY2hlY2tQZ1Bhc3MoXG4oKT0+e2xldCB0PUtjLnBvc3RncmVzTWQ1UGFzc3dvcmRIYXNoKHRoaXMudXNlcix0aGlzLnBhc3N3b3JkLGUuc2FsdCk7dGhpcy5jb25uZWN0aW9uLnBhc3N3b3JkKHQpfSl9X2hhbmRsZUF1dGhTQVNMKGUpe1xudGhpcy5fY2hlY2tQZ1Bhc3MoKCk9Pnt0aGlzLnNhc2xTZXNzaW9uPXhuLnN0YXJ0U2Vzc2lvbihlLm1lY2hhbmlzbXMpLHRoaXMuY29ubmVjdGlvbi5zZW5kU0FTTEluaXRpYWxSZXNwb25zZU1lc3NhZ2UoXG50aGlzLnNhc2xTZXNzaW9uLm1lY2hhbmlzbSx0aGlzLnNhc2xTZXNzaW9uLnJlc3BvbnNlKX0pfV9oYW5kbGVBdXRoU0FTTENvbnRpbnVlKGUpe3huLmNvbnRpbnVlU2Vzc2lvbihcbnRoaXMuc2FzbFNlc3Npb24sdGhpcy5wYXNzd29yZCxlLmRhdGEpLHRoaXMuY29ubmVjdGlvbi5zZW5kU0NSQU1DbGllbnRGaW5hbE1lc3NhZ2UodGhpcy5zYXNsU2Vzc2lvbi5cbnJlc3BvbnNlKX1faGFuZGxlQXV0aFNBU0xGaW5hbChlKXt4bi5maW5hbGl6ZVNlc3Npb24odGhpcy5zYXNsU2Vzc2lvbixlLmRhdGEpLHRoaXMuc2FzbFNlc3Npb249bnVsbH1faGFuZGxlQmFja2VuZEtleURhdGEoZSl7XG50aGlzLnByb2Nlc3NJRD1lLnByb2Nlc3NJRCx0aGlzLnNlY3JldEtleT1lLnNlY3JldEtleX1faGFuZGxlUmVhZHlGb3JRdWVyeShlKXt0aGlzLl9jb25uZWN0aW5nJiYodGhpcy5cbl9jb25uZWN0aW5nPSExLHRoaXMuX2Nvbm5lY3RlZD0hMCxjbGVhclRpbWVvdXQodGhpcy5jb25uZWN0aW9uVGltZW91dEhhbmRsZSksdGhpcy5fY29ubmVjdGlvbkNhbGxiYWNrJiZcbih0aGlzLl9jb25uZWN0aW9uQ2FsbGJhY2sobnVsbCx0aGlzKSx0aGlzLl9jb25uZWN0aW9uQ2FsbGJhY2s9bnVsbCksdGhpcy5lbWl0KFwiY29ubmVjdFwiKSk7bGV0e2FjdGl2ZVF1ZXJ5OnR9PXRoaXM7XG50aGlzLmFjdGl2ZVF1ZXJ5PW51bGwsdGhpcy5yZWFkeUZvclF1ZXJ5PSEwLHQmJnQuaGFuZGxlUmVhZHlGb3JRdWVyeSh0aGlzLmNvbm5lY3Rpb24pLHRoaXMuX3B1bHNlUXVlcnlRdWV1ZSgpfV9oYW5kbGVFcnJvcldoaWxlQ29ubmVjdGluZyhlKXtcbmlmKCF0aGlzLl9jb25uZWN0aW9uRXJyb3Ipe2lmKHRoaXMuX2Nvbm5lY3Rpb25FcnJvcj0hMCxjbGVhclRpbWVvdXQodGhpcy5jb25uZWN0aW9uVGltZW91dEhhbmRsZSksdGhpcy5cbl9jb25uZWN0aW9uQ2FsbGJhY2spcmV0dXJuIHRoaXMuX2Nvbm5lY3Rpb25DYWxsYmFjayhlKTt0aGlzLmVtaXQoXCJlcnJvclwiLGUpfX1faGFuZGxlRXJyb3JFdmVudChlKXtpZih0aGlzLlxuX2Nvbm5lY3RpbmcpcmV0dXJuIHRoaXMuX2hhbmRsZUVycm9yV2hpbGVDb25uZWN0aW5nKGUpO3RoaXMuX3F1ZXJ5YWJsZT0hMSx0aGlzLl9lcnJvckFsbFF1ZXJpZXMoZSksdGhpcy5cbmVtaXQoXCJlcnJvclwiLGUpfV9oYW5kbGVFcnJvck1lc3NhZ2UoZSl7aWYodGhpcy5fY29ubmVjdGluZylyZXR1cm4gdGhpcy5faGFuZGxlRXJyb3JXaGlsZUNvbm5lY3RpbmcoZSk7XG5sZXQgdD10aGlzLmFjdGl2ZVF1ZXJ5O2lmKCF0KXt0aGlzLl9oYW5kbGVFcnJvckV2ZW50KGUpO3JldHVybn10aGlzLmFjdGl2ZVF1ZXJ5PW51bGwsdC5oYW5kbGVFcnJvcihlLFxudGhpcy5jb25uZWN0aW9uKX1faGFuZGxlUm93RGVzY3JpcHRpb24oZSl7dGhpcy5hY3RpdmVRdWVyeS5oYW5kbGVSb3dEZXNjcmlwdGlvbihlKX1faGFuZGxlRGF0YVJvdyhlKXtcbnRoaXMuYWN0aXZlUXVlcnkuaGFuZGxlRGF0YVJvdyhlKX1faGFuZGxlUG9ydGFsU3VzcGVuZGVkKGUpe3RoaXMuYWN0aXZlUXVlcnkuaGFuZGxlUG9ydGFsU3VzcGVuZGVkKHRoaXMuXG5jb25uZWN0aW9uKX1faGFuZGxlRW1wdHlRdWVyeShlKXt0aGlzLmFjdGl2ZVF1ZXJ5LmhhbmRsZUVtcHR5UXVlcnkodGhpcy5jb25uZWN0aW9uKX1faGFuZGxlQ29tbWFuZENvbXBsZXRlKGUpe1xudGhpcy5hY3RpdmVRdWVyeS5oYW5kbGVDb21tYW5kQ29tcGxldGUoZSx0aGlzLmNvbm5lY3Rpb24pfV9oYW5kbGVQYXJzZUNvbXBsZXRlKGUpe3RoaXMuYWN0aXZlUXVlcnkubmFtZSYmXG4odGhpcy5jb25uZWN0aW9uLnBhcnNlZFN0YXRlbWVudHNbdGhpcy5hY3RpdmVRdWVyeS5uYW1lXT10aGlzLmFjdGl2ZVF1ZXJ5LnRleHQpfV9oYW5kbGVDb3B5SW5SZXNwb25zZShlKXtcbnRoaXMuYWN0aXZlUXVlcnkuaGFuZGxlQ29weUluUmVzcG9uc2UodGhpcy5jb25uZWN0aW9uKX1faGFuZGxlQ29weURhdGEoZSl7dGhpcy5hY3RpdmVRdWVyeS5oYW5kbGVDb3B5RGF0YShcbmUsdGhpcy5jb25uZWN0aW9uKX1faGFuZGxlTm90aWZpY2F0aW9uKGUpe3RoaXMuZW1pdChcIm5vdGlmaWNhdGlvblwiLGUpfV9oYW5kbGVOb3RpY2UoZSl7dGhpcy5lbWl0KFwibm9cXFxudGljZVwiLGUpfWdldFN0YXJ0dXBDb25mKCl7dmFyIGU9dGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycyx0PXt1c2VyOmUudXNlcixkYXRhYmFzZTplLmRhdGFiYXNlfSxuPWUuYXBwbGljYXRpb25fbmFtZXx8XG5lLmZhbGxiYWNrX2FwcGxpY2F0aW9uX25hbWU7cmV0dXJuIG4mJih0LmFwcGxpY2F0aW9uX25hbWU9biksZS5yZXBsaWNhdGlvbiYmKHQucmVwbGljYXRpb249XCJcIitlLnJlcGxpY2F0aW9uKSxcbmUuc3RhdGVtZW50X3RpbWVvdXQmJih0LnN0YXRlbWVudF90aW1lb3V0PVN0cmluZyhwYXJzZUludChlLnN0YXRlbWVudF90aW1lb3V0LDEwKSkpLGUubG9ja190aW1lb3V0JiZcbih0LmxvY2tfdGltZW91dD1TdHJpbmcocGFyc2VJbnQoZS5sb2NrX3RpbWVvdXQsMTApKSksZS5pZGxlX2luX3RyYW5zYWN0aW9uX3Nlc3Npb25fdGltZW91dCYmKHQuaWRsZV9pbl90cmFuc2FjdGlvbl9zZXNzaW9uX3RpbWVvdXQ9XG5TdHJpbmcocGFyc2VJbnQoZS5pZGxlX2luX3RyYW5zYWN0aW9uX3Nlc3Npb25fdGltZW91dCwxMCkpKSxlLm9wdGlvbnMmJih0Lm9wdGlvbnM9ZS5vcHRpb25zKSx0fWNhbmNlbChlLHQpe1xuaWYoZS5hY3RpdmVRdWVyeT09PXQpe3ZhciBuPXRoaXMuY29ubmVjdGlvbjt0aGlzLmhvc3QmJnRoaXMuaG9zdC5pbmRleE9mKFwiL1wiKT09PTA/bi5jb25uZWN0KHRoaXMuaG9zdCtcblwiLy5zLlBHU1FMLlwiK3RoaXMucG9ydCk6bi5jb25uZWN0KHRoaXMucG9ydCx0aGlzLmhvc3QpLG4ub24oXCJjb25uZWN0XCIsZnVuY3Rpb24oKXtuLmNhbmNlbChlLnByb2Nlc3NJRCxcbmUuc2VjcmV0S2V5KX0pfWVsc2UgZS5xdWVyeVF1ZXVlLmluZGV4T2YodCkhPT0tMSYmZS5xdWVyeVF1ZXVlLnNwbGljZShlLnF1ZXJ5UXVldWUuaW5kZXhPZih0KSwxKX1zZXRUeXBlUGFyc2VyKGUsdCxuKXtcbnJldHVybiB0aGlzLl90eXBlcy5zZXRUeXBlUGFyc2VyKGUsdCxuKX1nZXRUeXBlUGFyc2VyKGUsdCl7cmV0dXJuIHRoaXMuX3R5cGVzLmdldFR5cGVQYXJzZXIoZSx0KX1lc2NhcGVJZGVudGlmaWVyKGUpe1xucmV0dXJuJ1wiJytlLnJlcGxhY2UoL1wiL2csJ1wiXCInKSsnXCInfWVzY2FwZUxpdGVyYWwoZSl7Zm9yKHZhciB0PSExLG49XCInXCIsaT0wO2k8ZS5sZW5ndGg7aSsrKXt2YXIgcz1lW2ldO1xucz09PVwiJ1wiP24rPXMrczpzPT09XCJcXFxcXCI/KG4rPXMrcyx0PSEwKTpuKz1zfXJldHVybiBuKz1cIidcIix0PT09ITAmJihuPVwiIEVcIituKSxufV9wdWxzZVF1ZXJ5UXVldWUoKXtpZih0aGlzLlxucmVhZHlGb3JRdWVyeT09PSEwKWlmKHRoaXMuYWN0aXZlUXVlcnk9dGhpcy5xdWVyeVF1ZXVlLnNoaWZ0KCksdGhpcy5hY3RpdmVRdWVyeSl7dGhpcy5yZWFkeUZvclF1ZXJ5PVxuITEsdGhpcy5oYXNFeGVjdXRlZD0hMDtsZXQgZT10aGlzLmFjdGl2ZVF1ZXJ5LnN1Ym1pdCh0aGlzLmNvbm5lY3Rpb24pO2UmJm0ubmV4dFRpY2soKCk9Pnt0aGlzLmFjdGl2ZVF1ZXJ5LlxuaGFuZGxlRXJyb3IoZSx0aGlzLmNvbm5lY3Rpb24pLHRoaXMucmVhZHlGb3JRdWVyeT0hMCx0aGlzLl9wdWxzZVF1ZXJ5UXVldWUoKX0pfWVsc2UgdGhpcy5oYXNFeGVjdXRlZCYmXG4odGhpcy5hY3RpdmVRdWVyeT1udWxsLHRoaXMuZW1pdChcImRyYWluXCIpKX1xdWVyeShlLHQsbil7dmFyIGkscyxvLHUsYztpZihlPT1udWxsKXRocm93IG5ldyBUeXBlRXJyb3IoXG5cIkNsaWVudCB3YXMgcGFzc2VkIGEgbnVsbCBvciB1bmRlZmluZWQgcXVlcnlcIik7cmV0dXJuIHR5cGVvZiBlLnN1Ym1pdD09XCJmdW5jdGlvblwiPyhvPWUucXVlcnlfdGltZW91dHx8XG50aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLnF1ZXJ5X3RpbWVvdXQscz1pPWUsdHlwZW9mIHQ9PVwiZnVuY3Rpb25cIiYmKGkuY2FsbGJhY2s9aS5jYWxsYmFja3x8dCkpOihvPXRoaXMuXG5jb25uZWN0aW9uUGFyYW1ldGVycy5xdWVyeV90aW1lb3V0LGk9bmV3IFlzKGUsdCxuKSxpLmNhbGxiYWNrfHwocz1uZXcgdGhpcy5fUHJvbWlzZSgobCxmKT0+e2kuY2FsbGJhY2s9XG4oeSxnKT0+eT9mKHkpOmwoZyl9KSkpLG8mJihjPWkuY2FsbGJhY2ssdT1zZXRUaW1lb3V0KCgpPT57dmFyIGw9bmV3IEVycm9yKFwiUXVlcnkgcmVhZCB0aW1lb3V0XCIpO20ubmV4dFRpY2soXG4oKT0+e2kuaGFuZGxlRXJyb3IobCx0aGlzLmNvbm5lY3Rpb24pfSksYyhsKSxpLmNhbGxiYWNrPSgpPT57fTt2YXIgZj10aGlzLnF1ZXJ5UXVldWUuaW5kZXhPZihpKTtmPi0xJiZcbnRoaXMucXVlcnlRdWV1ZS5zcGxpY2UoZiwxKSx0aGlzLl9wdWxzZVF1ZXJ5UXVldWUoKX0sbyksaS5jYWxsYmFjaz0obCxmKT0+e2NsZWFyVGltZW91dCh1KSxjKGwsZil9KSxcbnRoaXMuYmluYXJ5JiYhaS5iaW5hcnkmJihpLmJpbmFyeT0hMCksaS5fcmVzdWx0JiYhaS5fcmVzdWx0Ll90eXBlcyYmKGkuX3Jlc3VsdC5fdHlwZXM9dGhpcy5fdHlwZXMpLHRoaXMuXG5fcXVlcnlhYmxlP3RoaXMuX2VuZGluZz8obS5uZXh0VGljaygoKT0+e2kuaGFuZGxlRXJyb3IobmV3IEVycm9yKFwiQ2xpZW50IHdhcyBjbG9zZWQgYW5kIGlzIG5vdCBxdWVyeVxcXG5hYmxlXCIpLHRoaXMuY29ubmVjdGlvbil9KSxzKToodGhpcy5xdWVyeVF1ZXVlLnB1c2goaSksdGhpcy5fcHVsc2VRdWVyeVF1ZXVlKCkscyk6KG0ubmV4dFRpY2soKCk9PntpLlxuaGFuZGxlRXJyb3IobmV3IEVycm9yKFwiQ2xpZW50IGhhcyBlbmNvdW50ZXJlZCBhIGNvbm5lY3Rpb24gZXJyb3IgYW5kIGlzIG5vdCBxdWVyeWFibGVcIiksdGhpcy5jb25uZWN0aW9uKX0pLFxucyl9cmVmKCl7dGhpcy5jb25uZWN0aW9uLnJlZigpfXVucmVmKCl7dGhpcy5jb25uZWN0aW9uLnVucmVmKCl9ZW5kKGUpe2lmKHRoaXMuX2VuZGluZz0hMCwhdGhpcy5jb25uZWN0aW9uLlxuX2Nvbm5lY3RpbmcpaWYoZSllKCk7ZWxzZSByZXR1cm4gdGhpcy5fUHJvbWlzZS5yZXNvbHZlKCk7aWYodGhpcy5hY3RpdmVRdWVyeXx8IXRoaXMuX3F1ZXJ5YWJsZT90aGlzLlxuY29ubmVjdGlvbi5zdHJlYW0uZGVzdHJveSgpOnRoaXMuY29ubmVjdGlvbi5lbmQoKSxlKXRoaXMuY29ubmVjdGlvbi5vbmNlKFwiZW5kXCIsZSk7ZWxzZSByZXR1cm4gbmV3IHRoaXMuXG5fUHJvbWlzZSh0PT57dGhpcy5jb25uZWN0aW9uLm9uY2UoXCJlbmRcIix0KX0pfX07YShTbixcIkNsaWVudFwiKTt2YXIga3Q9U247a3QuUXVlcnk9WXM7WnMuZXhwb3J0cz1rdH0pO3ZhciBybz1JKChYaCx0byk9PntcInVzZSBzdHJpY3RcIjtwKCk7dmFyIHRsPW1lKCkuRXZlbnRFbWl0dGVyLFhzPWEoZnVuY3Rpb24oKXt9LFwiTk9PUFwiKSxlbz1hKChyLGUpPT57XG5sZXQgdD1yLmZpbmRJbmRleChlKTtyZXR1cm4gdD09PS0xP3ZvaWQgMDpyLnNwbGljZSh0LDEpWzBdfSxcInJlbW92ZVdoZXJlXCIpLENuPWNsYXNzIENue2NvbnN0cnVjdG9yKGUsdCxuKXtcbnRoaXMuY2xpZW50PWUsdGhpcy5pZGxlTGlzdGVuZXI9dCx0aGlzLnRpbWVvdXRJZD1ufX07YShDbixcIklkbGVJdGVtXCIpO3ZhciBFbj1Dbixfbj1jbGFzcyBfbntjb25zdHJ1Y3RvcihlKXtcbnRoaXMuY2FsbGJhY2s9ZX19O2EoX24sXCJQZW5kaW5nSXRlbVwiKTt2YXIgcWU9X247ZnVuY3Rpb24gcmwoKXt0aHJvdyBuZXcgRXJyb3IoXCJSZWxlYXNlIGNhbGxlZCBvbiBjbGlcXFxuZW50IHdoaWNoIGhhcyBhbHJlYWR5IGJlZW4gcmVsZWFzZWQgdG8gdGhlIHBvb2wuXCIpfWEocmwsXCJ0aHJvd09uRG91YmxlUmVsZWFzZVwiKTtmdW5jdGlvbiBNdChyLGUpe2lmKGUpXG5yZXR1cm57Y2FsbGJhY2s6ZSxyZXN1bHQ6dm9pZCAwfTtsZXQgdCxuLGk9YShmdW5jdGlvbihvLHUpe28/dChvKTpuKHUpfSxcImNiXCIpLHM9bmV3IHIoZnVuY3Rpb24obyx1KXtcbm49byx0PXV9KS5jYXRjaChvPT57dGhyb3cgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2Uobyksb30pO3JldHVybntjYWxsYmFjazppLHJlc3VsdDpzfX1hKE10LFwicHJvbWlzaWZ5XCIpO1xuZnVuY3Rpb24gbmwocixlKXtyZXR1cm4gYShmdW5jdGlvbiB0KG4pe24uY2xpZW50PWUsZS5yZW1vdmVMaXN0ZW5lcihcImVycm9yXCIsdCksZS5vbihcImVycm9yXCIsKCk9PntyLmxvZyhcblwiYWRkaXRpb25hbCBjbGllbnQgZXJyb3IgYWZ0ZXIgZGlzY29ubmVjdGlvbiBkdWUgdG8gZXJyb3JcIixuKX0pLHIuX3JlbW92ZShlKSxyLmVtaXQoXCJlcnJvclwiLG4sZSl9LFwiaVxcXG5kbGVMaXN0ZW5lclwiKX1hKG5sLFwibWFrZUlkbGVMaXN0ZW5lclwiKTt2YXIgVG49Y2xhc3MgVG4gZXh0ZW5kcyB0bHtjb25zdHJ1Y3RvcihlLHQpe3N1cGVyKCksdGhpcy5vcHRpb25zPVxuT2JqZWN0LmFzc2lnbih7fSxlKSxlIT1udWxsJiZcInBhc3N3b3JkXCJpbiBlJiZPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcy5vcHRpb25zLFwicGFzc3dvcmRcIix7Y29uZmlndXJhYmxlOiEwLFxuZW51bWVyYWJsZTohMSx3cml0YWJsZTohMCx2YWx1ZTplLnBhc3N3b3JkfSksZSE9bnVsbCYmZS5zc2wmJmUuc3NsLmtleSYmT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMub3B0aW9ucy5cbnNzbCxcImtleVwiLHtlbnVtZXJhYmxlOiExfSksdGhpcy5vcHRpb25zLm1heD10aGlzLm9wdGlvbnMubWF4fHx0aGlzLm9wdGlvbnMucG9vbFNpemV8fDEwLHRoaXMub3B0aW9ucy5cbm1heFVzZXM9dGhpcy5vcHRpb25zLm1heFVzZXN8fDEvMCx0aGlzLm9wdGlvbnMuYWxsb3dFeGl0T25JZGxlPXRoaXMub3B0aW9ucy5hbGxvd0V4aXRPbklkbGV8fCExLHRoaXMuXG5vcHRpb25zLm1heExpZmV0aW1lU2Vjb25kcz10aGlzLm9wdGlvbnMubWF4TGlmZXRpbWVTZWNvbmRzfHwwLHRoaXMubG9nPXRoaXMub3B0aW9ucy5sb2d8fGZ1bmN0aW9uKCl7fSxcbnRoaXMuQ2xpZW50PXRoaXMub3B0aW9ucy5DbGllbnR8fHR8fG90KCkuQ2xpZW50LHRoaXMuUHJvbWlzZT10aGlzLm9wdGlvbnMuUHJvbWlzZXx8dy5Qcm9taXNlLHR5cGVvZiB0aGlzLlxub3B0aW9ucy5pZGxlVGltZW91dE1pbGxpcz5cInVcIiYmKHRoaXMub3B0aW9ucy5pZGxlVGltZW91dE1pbGxpcz0xZTQpLHRoaXMuX2NsaWVudHM9W10sdGhpcy5faWRsZT1bXSx0aGlzLlxuX2V4cGlyZWQ9bmV3IFdlYWtTZXQsdGhpcy5fcGVuZGluZ1F1ZXVlPVtdLHRoaXMuX2VuZENhbGxiYWNrPXZvaWQgMCx0aGlzLmVuZGluZz0hMSx0aGlzLmVuZGVkPSExfV9pc0Z1bGwoKXtcbnJldHVybiB0aGlzLl9jbGllbnRzLmxlbmd0aD49dGhpcy5vcHRpb25zLm1heH1fcHVsc2VRdWV1ZSgpe2lmKHRoaXMubG9nKFwicHVsc2UgcXVldWVcIiksdGhpcy5lbmRlZCl7dGhpcy5cbmxvZyhcInB1bHNlIHF1ZXVlIGVuZGVkXCIpO3JldHVybn1pZih0aGlzLmVuZGluZyl7dGhpcy5sb2coXCJwdWxzZSBxdWV1ZSBvbiBlbmRpbmdcIiksdGhpcy5faWRsZS5sZW5ndGgmJlxudGhpcy5faWRsZS5zbGljZSgpLm1hcCh0PT57dGhpcy5fcmVtb3ZlKHQuY2xpZW50KX0pLHRoaXMuX2NsaWVudHMubGVuZ3RofHwodGhpcy5lbmRlZD0hMCx0aGlzLl9lbmRDYWxsYmFjaygpKTtcbnJldHVybn1pZighdGhpcy5fcGVuZGluZ1F1ZXVlLmxlbmd0aCl7dGhpcy5sb2coXCJubyBxdWV1ZWQgcmVxdWVzdHNcIik7cmV0dXJufWlmKCF0aGlzLl9pZGxlLmxlbmd0aCYmdGhpcy5cbl9pc0Z1bGwoKSlyZXR1cm47bGV0IGU9dGhpcy5fcGVuZGluZ1F1ZXVlLnNoaWZ0KCk7aWYodGhpcy5faWRsZS5sZW5ndGgpe2xldCB0PXRoaXMuX2lkbGUucG9wKCk7Y2xlYXJUaW1lb3V0KFxudC50aW1lb3V0SWQpO2xldCBuPXQuY2xpZW50O24ucmVmJiZuLnJlZigpO2xldCBpPXQuaWRsZUxpc3RlbmVyO3JldHVybiB0aGlzLl9hY3F1aXJlQ2xpZW50KG4sZSxpLCExKX1cbmlmKCF0aGlzLl9pc0Z1bGwoKSlyZXR1cm4gdGhpcy5uZXdDbGllbnQoZSk7dGhyb3cgbmV3IEVycm9yKFwidW5leHBlY3RlZCBjb25kaXRpb25cIil9X3JlbW92ZShlKXtsZXQgdD1lbyhcbnRoaXMuX2lkbGUsbj0+bi5jbGllbnQ9PT1lKTt0IT09dm9pZCAwJiZjbGVhclRpbWVvdXQodC50aW1lb3V0SWQpLHRoaXMuX2NsaWVudHM9dGhpcy5fY2xpZW50cy5maWx0ZXIoXG5uPT5uIT09ZSksZS5lbmQoKSx0aGlzLmVtaXQoXCJyZW1vdmVcIixlKX1jb25uZWN0KGUpe2lmKHRoaXMuZW5kaW5nKXtsZXQgaT1uZXcgRXJyb3IoXCJDYW5ub3QgdXNlIGEgcG9vXFxcbmwgYWZ0ZXIgY2FsbGluZyBlbmQgb24gdGhlIHBvb2xcIik7cmV0dXJuIGU/ZShpKTp0aGlzLlByb21pc2UucmVqZWN0KGkpfWxldCB0PU10KHRoaXMuUHJvbWlzZSxlKSxuPXQuXG5yZXN1bHQ7aWYodGhpcy5faXNGdWxsKCl8fHRoaXMuX2lkbGUubGVuZ3RoKXtpZih0aGlzLl9pZGxlLmxlbmd0aCYmbS5uZXh0VGljaygoKT0+dGhpcy5fcHVsc2VRdWV1ZSgpKSxcbiF0aGlzLm9wdGlvbnMuY29ubmVjdGlvblRpbWVvdXRNaWxsaXMpcmV0dXJuIHRoaXMuX3BlbmRpbmdRdWV1ZS5wdXNoKG5ldyBxZSh0LmNhbGxiYWNrKSksbjtsZXQgaT1hKCh1LGMsbCk9PntcbmNsZWFyVGltZW91dChvKSx0LmNhbGxiYWNrKHUsYyxsKX0sXCJxdWV1ZUNhbGxiYWNrXCIpLHM9bmV3IHFlKGkpLG89c2V0VGltZW91dCgoKT0+e2VvKHRoaXMuX3BlbmRpbmdRdWV1ZSxcbnU9PnUuY2FsbGJhY2s9PT1pKSxzLnRpbWVkT3V0PSEwLHQuY2FsbGJhY2sobmV3IEVycm9yKFwidGltZW91dCBleGNlZWRlZCB3aGVuIHRyeWluZyB0byBjb25uZWN0XCIpKX0sdGhpcy5cbm9wdGlvbnMuY29ubmVjdGlvblRpbWVvdXRNaWxsaXMpO3JldHVybiBvLnVucmVmJiZvLnVucmVmKCksdGhpcy5fcGVuZGluZ1F1ZXVlLnB1c2gocyksbn1yZXR1cm4gdGhpcy5cbm5ld0NsaWVudChuZXcgcWUodC5jYWxsYmFjaykpLG59bmV3Q2xpZW50KGUpe2xldCB0PW5ldyB0aGlzLkNsaWVudCh0aGlzLm9wdGlvbnMpO3RoaXMuX2NsaWVudHMucHVzaChcbnQpO2xldCBuPW5sKHRoaXMsdCk7dGhpcy5sb2coXCJjaGVja2luZyBjbGllbnQgdGltZW91dFwiKTtsZXQgaSxzPSExO3RoaXMub3B0aW9ucy5jb25uZWN0aW9uVGltZW91dE1pbGxpcyYmXG4oaT1zZXRUaW1lb3V0KCgpPT57dGhpcy5sb2coXCJlbmRpbmcgY2xpZW50IGR1ZSB0byB0aW1lb3V0XCIpLHM9ITAsdC5jb25uZWN0aW9uP3QuY29ubmVjdGlvbi5zdHJlYW0uZGVzdHJveSgpOlxudC5lbmQoKX0sdGhpcy5vcHRpb25zLmNvbm5lY3Rpb25UaW1lb3V0TWlsbGlzKSksdGhpcy5sb2coXCJjb25uZWN0aW5nIG5ldyBjbGllbnRcIiksdC5jb25uZWN0KG89PntpZihpJiZcbmNsZWFyVGltZW91dChpKSx0Lm9uKFwiZXJyb3JcIixuKSxvKXRoaXMubG9nKFwiY2xpZW50IGZhaWxlZCB0byBjb25uZWN0XCIsbyksdGhpcy5fY2xpZW50cz10aGlzLl9jbGllbnRzLlxuZmlsdGVyKHU9PnUhPT10KSxzJiYobz1uZXcgRXJyb3IoXCJDb25uZWN0aW9uIHRlcm1pbmF0ZWQgZHVlIHRvIGNvbm5lY3Rpb24gdGltZW91dFwiLHtjYXVzZTpvfSkpLHRoaXMuXG5fcHVsc2VRdWV1ZSgpLGUudGltZWRPdXR8fGUuY2FsbGJhY2sobyx2b2lkIDAsWHMpO2Vsc2V7aWYodGhpcy5sb2coXCJuZXcgY2xpZW50IGNvbm5lY3RlZFwiKSx0aGlzLm9wdGlvbnMuXG5tYXhMaWZldGltZVNlY29uZHMhPT0wKXtsZXQgdT1zZXRUaW1lb3V0KCgpPT57dGhpcy5sb2coXCJlbmRpbmcgY2xpZW50IGR1ZSB0byBleHBpcmVkIGxpZmV0aW1lXCIpLHRoaXMuXG5fZXhwaXJlZC5hZGQodCksdGhpcy5faWRsZS5maW5kSW5kZXgobD0+bC5jbGllbnQ9PT10KSE9PS0xJiZ0aGlzLl9hY3F1aXJlQ2xpZW50KHQsbmV3IHFlKChsLGYseSk9PnkoKSksXG5uLCExKX0sdGhpcy5vcHRpb25zLm1heExpZmV0aW1lU2Vjb25kcyoxZTMpO3UudW5yZWYoKSx0Lm9uY2UoXCJlbmRcIiwoKT0+Y2xlYXJUaW1lb3V0KHUpKX1yZXR1cm4gdGhpcy5cbl9hY3F1aXJlQ2xpZW50KHQsZSxuLCEwKX19KX1fYWNxdWlyZUNsaWVudChlLHQsbixpKXtpJiZ0aGlzLmVtaXQoXCJjb25uZWN0XCIsZSksdGhpcy5lbWl0KFwiYWNxdWlyZVwiLGUpLFxuZS5yZWxlYXNlPXRoaXMuX3JlbGVhc2VPbmNlKGUsbiksZS5yZW1vdmVMaXN0ZW5lcihcImVycm9yXCIsbiksdC50aW1lZE91dD9pJiZ0aGlzLm9wdGlvbnMudmVyaWZ5P3RoaXMuXG5vcHRpb25zLnZlcmlmeShlLGUucmVsZWFzZSk6ZS5yZWxlYXNlKCk6aSYmdGhpcy5vcHRpb25zLnZlcmlmeT90aGlzLm9wdGlvbnMudmVyaWZ5KGUscz0+e2lmKHMpcmV0dXJuIGUuXG5yZWxlYXNlKHMpLHQuY2FsbGJhY2socyx2b2lkIDAsWHMpO3QuY2FsbGJhY2sodm9pZCAwLGUsZS5yZWxlYXNlKX0pOnQuY2FsbGJhY2sodm9pZCAwLGUsZS5yZWxlYXNlKX1fcmVsZWFzZU9uY2UoZSx0KXtcbmxldCBuPSExO3JldHVybiBpPT57biYmcmwoKSxuPSEwLHRoaXMuX3JlbGVhc2UoZSx0LGkpfX1fcmVsZWFzZShlLHQsbil7aWYoZS5vbihcImVycm9yXCIsdCksZS5fcG9vbFVzZUNvdW50PVxuKGUuX3Bvb2xVc2VDb3VudHx8MCkrMSx0aGlzLmVtaXQoXCJyZWxlYXNlXCIsbixlKSxufHx0aGlzLmVuZGluZ3x8IWUuX3F1ZXJ5YWJsZXx8ZS5fZW5kaW5nfHxlLl9wb29sVXNlQ291bnQ+PVxudGhpcy5vcHRpb25zLm1heFVzZXMpe2UuX3Bvb2xVc2VDb3VudD49dGhpcy5vcHRpb25zLm1heFVzZXMmJnRoaXMubG9nKFwicmVtb3ZlIGV4cGVuZGVkIGNsaWVudFwiKSx0aGlzLlxuX3JlbW92ZShlKSx0aGlzLl9wdWxzZVF1ZXVlKCk7cmV0dXJufWlmKHRoaXMuX2V4cGlyZWQuaGFzKGUpKXt0aGlzLmxvZyhcInJlbW92ZSBleHBpcmVkIGNsaWVudFwiKSx0aGlzLlxuX2V4cGlyZWQuZGVsZXRlKGUpLHRoaXMuX3JlbW92ZShlKSx0aGlzLl9wdWxzZVF1ZXVlKCk7cmV0dXJufWxldCBzO3RoaXMub3B0aW9ucy5pZGxlVGltZW91dE1pbGxpcyYmKHM9XG5zZXRUaW1lb3V0KCgpPT57dGhpcy5sb2coXCJyZW1vdmUgaWRsZSBjbGllbnRcIiksdGhpcy5fcmVtb3ZlKGUpfSx0aGlzLm9wdGlvbnMuaWRsZVRpbWVvdXRNaWxsaXMpLHRoaXMuXG5vcHRpb25zLmFsbG93RXhpdE9uSWRsZSYmcy51bnJlZigpKSx0aGlzLm9wdGlvbnMuYWxsb3dFeGl0T25JZGxlJiZlLnVucmVmKCksdGhpcy5faWRsZS5wdXNoKG5ldyBFbihlLFxudCxzKSksdGhpcy5fcHVsc2VRdWV1ZSgpfXF1ZXJ5KGUsdCxuKXtpZih0eXBlb2YgZT09XCJmdW5jdGlvblwiKXtsZXQgcz1NdCh0aGlzLlByb21pc2UsZSk7cmV0dXJuIGIoZnVuY3Rpb24oKXtcbnJldHVybiBzLmNhbGxiYWNrKG5ldyBFcnJvcihcIlBhc3NpbmcgYSBmdW5jdGlvbiBhcyB0aGUgZmlyc3QgcGFyYW1ldGVyIHRvIHBvb2wucXVlcnkgaXMgbm90IHN1cHBvcnRlXFxcbmRcIikpfSkscy5yZXN1bHR9dHlwZW9mIHQ9PVwiZnVuY3Rpb25cIiYmKG49dCx0PXZvaWQgMCk7bGV0IGk9TXQodGhpcy5Qcm9taXNlLG4pO3JldHVybiBuPWkuY2FsbGJhY2ssdGhpcy5cbmNvbm5lY3QoKHMsbyk9PntpZihzKXJldHVybiBuKHMpO2xldCB1PSExLGM9YShsPT57dXx8KHU9ITAsby5yZWxlYXNlKGwpLG4obCkpfSxcIm9uRXJyb3JcIik7by5vbmNlKFwiZXJcXFxucm9yXCIsYyksdGhpcy5sb2coXCJkaXNwYXRjaGluZyBxdWVyeVwiKTt0cnl7by5xdWVyeShlLHQsKGwsZik9PntpZih0aGlzLmxvZyhcInF1ZXJ5IGRpc3BhdGNoZWRcIiksby5yZW1vdmVMaXN0ZW5lcihcblwiZXJyb3JcIixjKSwhdSlyZXR1cm4gdT0hMCxvLnJlbGVhc2UobCksbD9uKGwpOm4odm9pZCAwLGYpfSl9Y2F0Y2gobCl7cmV0dXJuIG8ucmVsZWFzZShsKSxuKGwpfX0pLGkucmVzdWx0fWVuZChlKXtcbmlmKHRoaXMubG9nKFwiZW5kaW5nXCIpLHRoaXMuZW5kaW5nKXtsZXQgbj1uZXcgRXJyb3IoXCJDYWxsZWQgZW5kIG9uIHBvb2wgbW9yZSB0aGFuIG9uY2VcIik7cmV0dXJuIGU/ZShuKTpcbnRoaXMuUHJvbWlzZS5yZWplY3Qobil9dGhpcy5lbmRpbmc9ITA7bGV0IHQ9TXQodGhpcy5Qcm9taXNlLGUpO3JldHVybiB0aGlzLl9lbmRDYWxsYmFjaz10LmNhbGxiYWNrLHRoaXMuXG5fcHVsc2VRdWV1ZSgpLHQucmVzdWx0fWdldCB3YWl0aW5nQ291bnQoKXtyZXR1cm4gdGhpcy5fcGVuZGluZ1F1ZXVlLmxlbmd0aH1nZXQgaWRsZUNvdW50KCl7cmV0dXJuIHRoaXMuXG5faWRsZS5sZW5ndGh9Z2V0IGV4cGlyZWRDb3VudCgpe3JldHVybiB0aGlzLl9jbGllbnRzLnJlZHVjZSgoZSx0KT0+ZSsodGhpcy5fZXhwaXJlZC5oYXModCk/MTowKSwwKX1nZXQgdG90YWxDb3VudCgpe1xucmV0dXJuIHRoaXMuX2NsaWVudHMubGVuZ3RofX07YShUbixcIlBvb2xcIik7dmFyIEFuPVRuO3RvLmV4cG9ydHM9QW59KTt2YXIgbm89e307bmUobm8se2RlZmF1bHQ6KCk9PmlsfSk7dmFyIGlsLGlvPXooKCk9PntcInVzZSBzdHJpY3RcIjtwKCk7aWw9e319KTt2YXIgc289SSgobnAsc2wpPT57c2wuZXhwb3J0cz17bmFtZTpcInBnXCIsdmVyc2lvbjpcIjguOC4wXCIsZGVzY3JpcHRpb246XCJQb3N0Z3JlU1FMIGNsaWVudCAtIHB1cmUgamF2YXNcXFxuY3JpcHQgJiBsaWJwcSB3aXRoIHRoZSBzYW1lIEFQSVwiLGtleXdvcmRzOltcImRhdGFiYXNlXCIsXCJsaWJwcVwiLFwicGdcIixcInBvc3RncmVcIixcInBvc3RncmVzXCIsXCJwb3N0Z3Jlc3FsXCIsXG5cInJkYm1zXCJdLGhvbWVwYWdlOlwiaHR0cHM6Ly9naXRodWIuY29tL2JyaWFuYy9ub2RlLXBvc3RncmVzXCIscmVwb3NpdG9yeTp7dHlwZTpcImdpdFwiLHVybDpcImdpdDovL2dpdGh1YlxcXG4uY29tL2JyaWFuYy9ub2RlLXBvc3RncmVzLmdpdFwiLGRpcmVjdG9yeTpcInBhY2thZ2VzL3BnXCJ9LGF1dGhvcjpcIkJyaWFuIENhcmxzb24gPGJyaWFuLm0uY2FybHNvbkBnbWFpbFxcXG4uY29tPlwiLG1haW46XCIuL2xpYlwiLGRlcGVuZGVuY2llczp7XCJidWZmZXItd3JpdGVyXCI6XCIyLjAuMFwiLFwicGFja2V0LXJlYWRlclwiOlwiMS4wLjBcIixcInBnLWNvbm5lY3Rpb24tc3RyXFxcbmluZ1wiOlwiXjIuNS4wXCIsXCJwZy1wb29sXCI6XCJeMy41LjJcIixcInBnLXByb3RvY29sXCI6XCJeMS41LjBcIixcInBnLXR5cGVzXCI6XCJeMi4xLjBcIixwZ3Bhc3M6XCIxLnhcIn0sZGV2RGVwZW5kZW5jaWVzOntcbmFzeW5jOlwiMi42LjRcIixibHVlYmlyZDpcIjMuNS4yXCIsY286XCI0LjYuMFwiLFwicGctY29weS1zdHJlYW1zXCI6XCIwLjMuMFwifSxwZWVyRGVwZW5kZW5jaWVzOntcInBnLW5hdGl2ZVwiOlwiXFxcbj49My4wLjFcIn0scGVlckRlcGVuZGVuY2llc01ldGE6e1wicGctbmF0aXZlXCI6e29wdGlvbmFsOiEwfX0sc2NyaXB0czp7dGVzdDpcIm1ha2UgdGVzdC1hbGxcIn0sZmlsZXM6W1wibGlcXFxuYlwiLFwiU1BPTlNPUlMubWRcIl0sbGljZW5zZTpcIk1JVFwiLGVuZ2luZXM6e25vZGU6XCI+PSA4LjAuMFwifSxnaXRIZWFkOlwiYzk5ZmIyYzEyN2RkZjhkNzEyNTAwZGIyYzdiOWE1NDkxXFxcbmExNzg2NTVcIn19KTt2YXIgdW89SSgoaXAsYW8pPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciBvbz1tZSgpLkV2ZW50RW1pdHRlcixvbD0oaXQoKSxEKG50KSksSW49cnQoKSxRZT1hby5leHBvcnRzPWZ1bmN0aW9uKHIsZSx0KXtcbm9vLmNhbGwodGhpcykscj1Jbi5ub3JtYWxpemVRdWVyeUNvbmZpZyhyLGUsdCksdGhpcy50ZXh0PXIudGV4dCx0aGlzLnZhbHVlcz1yLnZhbHVlcyx0aGlzLm5hbWU9ci5uYW1lLFxudGhpcy5jYWxsYmFjaz1yLmNhbGxiYWNrLHRoaXMuc3RhdGU9XCJuZXdcIix0aGlzLl9hcnJheU1vZGU9ci5yb3dNb2RlPT09XCJhcnJheVwiLHRoaXMuX2VtaXRSb3dFdmVudHM9ITEsXG50aGlzLm9uKFwibmV3TGlzdGVuZXJcIixmdW5jdGlvbihuKXtuPT09XCJyb3dcIiYmKHRoaXMuX2VtaXRSb3dFdmVudHM9ITApfS5iaW5kKHRoaXMpKX07b2wuaW5oZXJpdHMoUWUsb28pO1xudmFyIGFsPXtzcWxTdGF0ZTpcImNvZGVcIixzdGF0ZW1lbnRQb3NpdGlvbjpcInBvc2l0aW9uXCIsbWVzc2FnZVByaW1hcnk6XCJtZXNzYWdlXCIsY29udGV4dDpcIndoZXJlXCIsc2NoZW1hTmFtZTpcIlxcXG5zY2hlbWFcIix0YWJsZU5hbWU6XCJ0YWJsZVwiLGNvbHVtbk5hbWU6XCJjb2x1bW5cIixkYXRhVHlwZU5hbWU6XCJkYXRhVHlwZVwiLGNvbnN0cmFpbnROYW1lOlwiY29uc3RyYWludFwiLHNvdXJjZUZpbGU6XCJcXFxuZmlsZVwiLHNvdXJjZUxpbmU6XCJsaW5lXCIsc291cmNlRnVuY3Rpb246XCJyb3V0aW5lXCJ9O1FlLnByb3RvdHlwZS5oYW5kbGVFcnJvcj1mdW5jdGlvbihyKXt2YXIgZT10aGlzLm5hdGl2ZS5cbnBxLnJlc3VsdEVycm9yRmllbGRzKCk7aWYoZSlmb3IodmFyIHQgaW4gZSl7dmFyIG49YWxbdF18fHQ7cltuXT1lW3RdfXRoaXMuY2FsbGJhY2s/dGhpcy5jYWxsYmFjayhyKTpcbnRoaXMuZW1pdChcImVycm9yXCIsciksdGhpcy5zdGF0ZT1cImVycm9yXCJ9O1FlLnByb3RvdHlwZS50aGVuPWZ1bmN0aW9uKHIsZSl7cmV0dXJuIHRoaXMuX2dldFByb21pc2UoKS50aGVuKFxucixlKX07UWUucHJvdG90eXBlLmNhdGNoPWZ1bmN0aW9uKHIpe3JldHVybiB0aGlzLl9nZXRQcm9taXNlKCkuY2F0Y2gocil9O1FlLnByb3RvdHlwZS5fZ2V0UHJvbWlzZT1mdW5jdGlvbigpe1xucmV0dXJuIHRoaXMuX3Byb21pc2U/dGhpcy5fcHJvbWlzZToodGhpcy5fcHJvbWlzZT1uZXcgUHJvbWlzZShmdW5jdGlvbihyLGUpe3RoaXMuX29uY2UoXCJlbmRcIixyKSx0aGlzLlxuX29uY2UoXCJlcnJvclwiLGUpfS5iaW5kKHRoaXMpKSx0aGlzLl9wcm9taXNlKX07UWUucHJvdG90eXBlLnN1Ym1pdD1mdW5jdGlvbihyKXt0aGlzLnN0YXRlPVwicnVubmluZ1wiO3ZhciBlPXRoaXM7XG50aGlzLm5hdGl2ZT1yLm5hdGl2ZSxyLm5hdGl2ZS5hcnJheU1vZGU9dGhpcy5fYXJyYXlNb2RlO3ZhciB0PWEoZnVuY3Rpb24ocyxvLHUpe2lmKHIubmF0aXZlLmFycmF5TW9kZT1cbiExLGIoZnVuY3Rpb24oKXtlLmVtaXQoXCJfZG9uZVwiKX0pLHMpcmV0dXJuIGUuaGFuZGxlRXJyb3Iocyk7ZS5fZW1pdFJvd0V2ZW50cyYmKHUubGVuZ3RoPjE/by5mb3JFYWNoKFxuKGMsbCk9PntjLmZvckVhY2goZj0+e2UuZW1pdChcInJvd1wiLGYsdVtsXSl9KX0pOm8uZm9yRWFjaChmdW5jdGlvbihjKXtlLmVtaXQoXCJyb3dcIixjLHUpfSkpLGUuc3RhdGU9XCJlXFxcbm5kXCIsZS5lbWl0KFwiZW5kXCIsdSksZS5jYWxsYmFjayYmZS5jYWxsYmFjayhudWxsLHUpfSxcImFmdGVyXCIpO2lmKG0uZG9tYWluJiYodD1tLmRvbWFpbi5iaW5kKHQpKSx0aGlzLlxubmFtZSl7dGhpcy5uYW1lLmxlbmd0aD42MyYmKGNvbnNvbGUuZXJyb3IoXCJXYXJuaW5nISBQb3N0Z3JlcyBvbmx5IHN1cHBvcnRzIDYzIGNoYXJhY3RlcnMgZm9yIHF1ZXJ5IG5cXFxuYW1lcy5cIiksY29uc29sZS5lcnJvcihcIllvdSBzdXBwbGllZCAlcyAoJXMpXCIsdGhpcy5uYW1lLHRoaXMubmFtZS5sZW5ndGgpLGNvbnNvbGUuZXJyb3IoXCJUaGlzIGNhbiBjYXVcXFxuc2UgY29uZmxpY3RzIGFuZCBzaWxlbnQgZXJyb3JzIGV4ZWN1dGluZyBxdWVyaWVzXCIpKTt2YXIgbj0odGhpcy52YWx1ZXN8fFtdKS5tYXAoSW4ucHJlcGFyZVZhbHVlKTtpZihyLlxubmFtZWRRdWVyaWVzW3RoaXMubmFtZV0pe2lmKHRoaXMudGV4dCYmci5uYW1lZFF1ZXJpZXNbdGhpcy5uYW1lXSE9PXRoaXMudGV4dCl7bGV0IHM9bmV3IEVycm9yKGBQcmVwYVxcXG5yZWQgc3RhdGVtZW50cyBtdXN0IGJlIHVuaXF1ZSAtICcke3RoaXMubmFtZX0nIHdhcyB1c2VkIGZvciBhIGRpZmZlcmVudCBzdGF0ZW1lbnRgKTtyZXR1cm4gdChzKX1yZXR1cm4gci5cbm5hdGl2ZS5leGVjdXRlKHRoaXMubmFtZSxuLHQpfXJldHVybiByLm5hdGl2ZS5wcmVwYXJlKHRoaXMubmFtZSx0aGlzLnRleHQsbi5sZW5ndGgsZnVuY3Rpb24ocyl7cmV0dXJuIHM/XG50KHMpOihyLm5hbWVkUXVlcmllc1tlLm5hbWVdPWUudGV4dCxlLm5hdGl2ZS5leGVjdXRlKGUubmFtZSxuLHQpKX0pfWVsc2UgaWYodGhpcy52YWx1ZXMpe2lmKCFBcnJheS5pc0FycmF5KFxudGhpcy52YWx1ZXMpKXtsZXQgcz1uZXcgRXJyb3IoXCJRdWVyeSB2YWx1ZXMgbXVzdCBiZSBhbiBhcnJheVwiKTtyZXR1cm4gdChzKX12YXIgaT10aGlzLnZhbHVlcy5tYXAoSW4uXG5wcmVwYXJlVmFsdWUpO3IubmF0aXZlLnF1ZXJ5KHRoaXMudGV4dCxpLHQpfWVsc2Ugci5uYXRpdmUucXVlcnkodGhpcy50ZXh0LHQpfX0pO3ZhciBobz1JKCh1cCxmbyk9PntcInVzZSBzdHJpY3RcIjtwKCk7dmFyIHVsPShpbygpLEQobm8pKSxjbD1TdCgpLGFwPXNvKCksY289bWUoKS5FdmVudEVtaXR0ZXIsbGw9KGl0KCksRChudCkpLFxuZmw9SXQoKSxsbz11bygpLFo9Zm8uZXhwb3J0cz1mdW5jdGlvbihyKXtjby5jYWxsKHRoaXMpLHI9cnx8e30sdGhpcy5fUHJvbWlzZT1yLlByb21pc2V8fHcuUHJvbWlzZSx0aGlzLlxuX3R5cGVzPW5ldyBjbChyLnR5cGVzKSx0aGlzLm5hdGl2ZT1uZXcgdWwoe3R5cGVzOnRoaXMuX3R5cGVzfSksdGhpcy5fcXVlcnlRdWV1ZT1bXSx0aGlzLl9lbmRpbmc9ITEsdGhpcy5cbl9jb25uZWN0aW5nPSExLHRoaXMuX2Nvbm5lY3RlZD0hMSx0aGlzLl9xdWVyeWFibGU9ITA7dmFyIGU9dGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycz1uZXcgZmwocik7dGhpcy5cbnVzZXI9ZS51c2VyLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLFwicGFzc3dvcmRcIix7Y29uZmlndXJhYmxlOiEwLGVudW1lcmFibGU6ITEsd3JpdGFibGU6ITAsdmFsdWU6ZS5cbnBhc3N3b3JkfSksdGhpcy5kYXRhYmFzZT1lLmRhdGFiYXNlLHRoaXMuaG9zdD1lLmhvc3QsdGhpcy5wb3J0PWUucG9ydCx0aGlzLm5hbWVkUXVlcmllcz17fX07Wi5RdWVyeT1cbmxvO2xsLmluaGVyaXRzKFosY28pO1oucHJvdG90eXBlLl9lcnJvckFsbFF1ZXJpZXM9ZnVuY3Rpb24ocil7bGV0IGU9YSh0PT57bS5uZXh0VGljaygoKT0+e3QubmF0aXZlPXRoaXMuXG5uYXRpdmUsdC5oYW5kbGVFcnJvcihyKX0pfSxcImVucXVldWVFcnJvclwiKTt0aGlzLl9oYXNBY3RpdmVRdWVyeSgpJiYoZSh0aGlzLl9hY3RpdmVRdWVyeSksdGhpcy5fYWN0aXZlUXVlcnk9XG5udWxsKSx0aGlzLl9xdWVyeVF1ZXVlLmZvckVhY2goZSksdGhpcy5fcXVlcnlRdWV1ZS5sZW5ndGg9MH07Wi5wcm90b3R5cGUuX2Nvbm5lY3Q9ZnVuY3Rpb24ocil7dmFyIGU9dGhpcztcbmlmKHRoaXMuX2Nvbm5lY3Rpbmcpe20ubmV4dFRpY2soKCk9PnIobmV3IEVycm9yKFwiQ2xpZW50IGhhcyBhbHJlYWR5IGJlZW4gY29ubmVjdGVkLiBZb3UgY2Fubm90IHJldXNlXFxcbiBhIGNsaWVudC5cIikpKTtyZXR1cm59dGhpcy5fY29ubmVjdGluZz0hMCx0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLmdldExpYnBxQ29ubmVjdGlvblN0cmluZyhmdW5jdGlvbih0LG4pe1xuaWYodClyZXR1cm4gcih0KTtlLm5hdGl2ZS5jb25uZWN0KG4sZnVuY3Rpb24oaSl7aWYoaSlyZXR1cm4gZS5uYXRpdmUuZW5kKCkscihpKTtlLl9jb25uZWN0ZWQ9ITAsZS5uYXRpdmUuXG5vbihcImVycm9yXCIsZnVuY3Rpb24ocyl7ZS5fcXVlcnlhYmxlPSExLGUuX2Vycm9yQWxsUXVlcmllcyhzKSxlLmVtaXQoXCJlcnJvclwiLHMpfSksZS5uYXRpdmUub24oXCJub3RpZmlcXFxuY2F0aW9uXCIsZnVuY3Rpb24ocyl7ZS5lbWl0KFwibm90aWZpY2F0aW9uXCIse2NoYW5uZWw6cy5yZWxuYW1lLHBheWxvYWQ6cy5leHRyYX0pfSksZS5lbWl0KFwiY29ubmVjdFwiKSxlLlxuX3B1bHNlUXVlcnlRdWV1ZSghMCkscigpfSl9KX07Wi5wcm90b3R5cGUuY29ubmVjdD1mdW5jdGlvbihyKXtpZihyKXt0aGlzLl9jb25uZWN0KHIpO3JldHVybn1yZXR1cm4gbmV3IHRoaXMuXG5fUHJvbWlzZSgoZSx0KT0+e3RoaXMuX2Nvbm5lY3Qobj0+e24/dChuKTplKCl9KX0pfTtaLnByb3RvdHlwZS5xdWVyeT1mdW5jdGlvbihyLGUsdCl7dmFyIG4saSxzLG8sdTtpZihyPT1cbm51bGwpdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNsaWVudCB3YXMgcGFzc2VkIGEgbnVsbCBvciB1bmRlZmluZWQgcXVlcnlcIik7aWYodHlwZW9mIHIuc3VibWl0PT1cImZ1bmN0aW9cXFxublwiKXM9ci5xdWVyeV90aW1lb3V0fHx0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLnF1ZXJ5X3RpbWVvdXQsaT1uPXIsdHlwZW9mIGU9PVwiZnVuY3Rpb25cIiYmKHIuY2FsbGJhY2s9XG5lKTtlbHNlIGlmKHM9dGhpcy5jb25uZWN0aW9uUGFyYW1ldGVycy5xdWVyeV90aW1lb3V0LG49bmV3IGxvKHIsZSx0KSwhbi5jYWxsYmFjayl7bGV0IGMsbDtpPW5ldyB0aGlzLlxuX1Byb21pc2UoKGYseSk9PntjPWYsbD15fSksbi5jYWxsYmFjaz0oZix5KT0+Zj9sKGYpOmMoeSl9cmV0dXJuIHMmJih1PW4uY2FsbGJhY2ssbz1zZXRUaW1lb3V0KCgpPT57dmFyIGM9bmV3IEVycm9yKFxuXCJRdWVyeSByZWFkIHRpbWVvdXRcIik7bS5uZXh0VGljaygoKT0+e24uaGFuZGxlRXJyb3IoYyx0aGlzLmNvbm5lY3Rpb24pfSksdShjKSxuLmNhbGxiYWNrPSgpPT57fTt2YXIgbD10aGlzLlxuX3F1ZXJ5UXVldWUuaW5kZXhPZihuKTtsPi0xJiZ0aGlzLl9xdWVyeVF1ZXVlLnNwbGljZShsLDEpLHRoaXMuX3B1bHNlUXVlcnlRdWV1ZSgpfSxzKSxuLmNhbGxiYWNrPShjLGwpPT57XG5jbGVhclRpbWVvdXQobyksdShjLGwpfSksdGhpcy5fcXVlcnlhYmxlP3RoaXMuX2VuZGluZz8obi5uYXRpdmU9dGhpcy5uYXRpdmUsbS5uZXh0VGljaygoKT0+e24uaGFuZGxlRXJyb3IoXG5uZXcgRXJyb3IoXCJDbGllbnQgd2FzIGNsb3NlZCBhbmQgaXMgbm90IHF1ZXJ5YWJsZVwiKSl9KSxpKToodGhpcy5fcXVlcnlRdWV1ZS5wdXNoKG4pLHRoaXMuX3B1bHNlUXVlcnlRdWV1ZSgpLFxuaSk6KG4ubmF0aXZlPXRoaXMubmF0aXZlLG0ubmV4dFRpY2soKCk9PntuLmhhbmRsZUVycm9yKG5ldyBFcnJvcihcIkNsaWVudCBoYXMgZW5jb3VudGVyZWQgYSBjb25uZWN0aW9cXFxubiBlcnJvciBhbmQgaXMgbm90IHF1ZXJ5YWJsZVwiKSl9KSxpKX07Wi5wcm90b3R5cGUuZW5kPWZ1bmN0aW9uKHIpe3ZhciBlPXRoaXM7dGhpcy5fZW5kaW5nPSEwLHRoaXMuX2Nvbm5lY3RlZHx8XG50aGlzLm9uY2UoXCJjb25uZWN0XCIsdGhpcy5lbmQuYmluZCh0aGlzLHIpKTt2YXIgdDtyZXR1cm4gcnx8KHQ9bmV3IHRoaXMuX1Byb21pc2UoZnVuY3Rpb24obixpKXtyPWEocz0+cz9cbmkocyk6bigpLFwiY2JcIil9KSksdGhpcy5uYXRpdmUuZW5kKGZ1bmN0aW9uKCl7ZS5fZXJyb3JBbGxRdWVyaWVzKG5ldyBFcnJvcihcIkNvbm5lY3Rpb24gdGVybWluYXRlZFwiKSksXG5tLm5leHRUaWNrKCgpPT57ZS5lbWl0KFwiZW5kXCIpLHImJnIoKX0pfSksdH07Wi5wcm90b3R5cGUuX2hhc0FjdGl2ZVF1ZXJ5PWZ1bmN0aW9uKCl7cmV0dXJuIHRoaXMuX2FjdGl2ZVF1ZXJ5JiZcbnRoaXMuX2FjdGl2ZVF1ZXJ5LnN0YXRlIT09XCJlcnJvclwiJiZ0aGlzLl9hY3RpdmVRdWVyeS5zdGF0ZSE9PVwiZW5kXCJ9O1oucHJvdG90eXBlLl9wdWxzZVF1ZXJ5UXVldWU9ZnVuY3Rpb24ocil7XG5pZih0aGlzLl9jb25uZWN0ZWQmJiF0aGlzLl9oYXNBY3RpdmVRdWVyeSgpKXt2YXIgZT10aGlzLl9xdWVyeVF1ZXVlLnNoaWZ0KCk7aWYoIWUpe3J8fHRoaXMuZW1pdChcImRyYVxcXG5pblwiKTtyZXR1cm59dGhpcy5fYWN0aXZlUXVlcnk9ZSxlLnN1Ym1pdCh0aGlzKTt2YXIgdD10aGlzO2Uub25jZShcIl9kb25lXCIsZnVuY3Rpb24oKXt0Ll9wdWxzZVF1ZXJ5UXVldWUoKX0pfX07XG5aLnByb3RvdHlwZS5jYW5jZWw9ZnVuY3Rpb24ocil7dGhpcy5fYWN0aXZlUXVlcnk9PT1yP3RoaXMubmF0aXZlLmNhbmNlbChmdW5jdGlvbigpe30pOnRoaXMuX3F1ZXJ5UXVldWUuXG5pbmRleE9mKHIpIT09LTEmJnRoaXMuX3F1ZXJ5UXVldWUuc3BsaWNlKHRoaXMuX3F1ZXJ5UXVldWUuaW5kZXhPZihyKSwxKX07Wi5wcm90b3R5cGUucmVmPWZ1bmN0aW9uKCl7fTtcbloucHJvdG90eXBlLnVucmVmPWZ1bmN0aW9uKCl7fTtaLnByb3RvdHlwZS5zZXRUeXBlUGFyc2VyPWZ1bmN0aW9uKHIsZSx0KXtyZXR1cm4gdGhpcy5fdHlwZXMuc2V0VHlwZVBhcnNlcihcbnIsZSx0KX07Wi5wcm90b3R5cGUuZ2V0VHlwZVBhcnNlcj1mdW5jdGlvbihyLGUpe3JldHVybiB0aGlzLl90eXBlcy5nZXRUeXBlUGFyc2VyKHIsZSl9fSk7dmFyIFBuPUkoKGZwLHBvKT0+e1widXNlIHN0cmljdFwiO3AoKTtwby5leHBvcnRzPWhvKCl9KTt2YXIgb3Q9SSgocHAsYXQpPT57XCJ1c2Ugc3RyaWN0XCI7cCgpO3ZhciBobD1KcygpLHBsPXR0KCksZGw9dm4oKSx5bD1ybygpLHtEYXRhYmFzZUVycm9yOm1sfT1nbigpLGdsPWEoXG5yPT57dmFyIGU7cmV0dXJuIGU9Y2xhc3MgZXh0ZW5kcyB5bHtjb25zdHJ1Y3RvcihuKXtzdXBlcihuLHIpfX0sYShlLFwiQm91bmRQb29sXCIpLGV9LFwicG9vbEZhY3RvcnlcIiksUm49YShcbmZ1bmN0aW9uKHIpe3RoaXMuZGVmYXVsdHM9cGwsdGhpcy5DbGllbnQ9cix0aGlzLlF1ZXJ5PXRoaXMuQ2xpZW50LlF1ZXJ5LHRoaXMuUG9vbD1nbCh0aGlzLkNsaWVudCksdGhpcy5cbl9wb29scz1bXSx0aGlzLkNvbm5lY3Rpb249ZGwsdGhpcy50eXBlcz1KZSgpLHRoaXMuRGF0YWJhc2VFcnJvcj1tbH0sXCJQR1wiKTt0eXBlb2YgbS5lbnYuTk9ERV9QR19GT1JDRV9OQVRJVkU8XG5cInVcIj9hdC5leHBvcnRzPW5ldyBSbihQbigpKTooYXQuZXhwb3J0cz1uZXcgUm4oaGwpLE9iamVjdC5kZWZpbmVQcm9wZXJ0eShhdC5leHBvcnRzLFwibmF0aXZlXCIse2NvbmZpZ3VyYWJsZTohMCxcbmVudW1lcmFibGU6ITEsZ2V0KCl7dmFyIHI9bnVsbDt0cnl7cj1uZXcgUm4oUG4oKSl9Y2F0Y2goZSl7aWYoZS5jb2RlIT09XCJNT0RVTEVfTk9UX0ZPVU5EXCIpdGhyb3cgZX1yZXR1cm4gT2JqZWN0LlxuZGVmaW5lUHJvcGVydHkoYXQuZXhwb3J0cyxcIm5hdGl2ZVwiLHt2YWx1ZTpyfSkscn19KSl9KTtwKCk7cCgpO1dlKCk7enQoKTtwKCk7dmFyIGZhPU9iamVjdC5kZWZpbmVQcm9wZXJ0eSxoYT1PYmplY3QuZGVmaW5lUHJvcGVydGllcyxwYT1PYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyxnaT1PYmplY3QuXG5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMsZGE9T2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eSx5YT1PYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLHdpPWEoXG4ocixlLHQpPT5lIGluIHI/ZmEocixlLHtlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMCx2YWx1ZTp0fSk6cltlXT10LFwiX19kZWZOb3JtYWxQcm9wXCIpLFxubWE9YSgocixlKT0+e2Zvcih2YXIgdCBpbiBlfHwoZT17fSkpZGEuY2FsbChlLHQpJiZ3aShyLHQsZVt0XSk7aWYoZ2kpZm9yKHZhciB0IG9mIGdpKGUpKXlhLmNhbGwoZSx0KSYmXG53aShyLHQsZVt0XSk7cmV0dXJuIHJ9LFwiX19zcHJlYWRWYWx1ZXNcIiksZ2E9YSgocixlKT0+aGEocixwYShlKSksXCJfX3NwcmVhZFByb3BzXCIpLHdhPTEwMDhlMyxiaT1uZXcgVWludDhBcnJheShcbm5ldyBVaW50MTZBcnJheShbMjU4XSkuYnVmZmVyKVswXT09PTIsYmE9bmV3IFRleHREZWNvZGVyLEt0PW5ldyBUZXh0RW5jb2RlcixkdD1LdC5lbmNvZGUoXCIwMTIzNDU2Nzg5XFxcbmFiY2RlZlwiKSx5dD1LdC5lbmNvZGUoXCIwMTIzNDU2Nzg5QUJDREVGXCIpLHZhPUt0LmVuY29kZShcIkFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyXFxcbnN0dXZ3eHl6MDEyMzQ1Njc4OSsvXCIpO3ZhciB2aT12YS5zbGljZSgpO3ZpWzYyXT00NTt2aVs2M109OTU7dmFyIEhlLG10O2Z1bmN0aW9uIHhhKHIse2FscGhhYmV0OmUsc2NyYXRjaEFycjp0fT17fSl7aWYoIUhlKWlmKEhlPVxubmV3IFVpbnQxNkFycmF5KDI1NiksbXQ9bmV3IFVpbnQxNkFycmF5KDI1NiksYmkpZm9yKGxldCBDPTA7QzwyNTY7QysrKUhlW0NdPWR0W0MmMTVdPDw4fGR0W0M+Pj40XSxtdFtDXT1cbnl0W0MmMTVdPDw4fHl0W0M+Pj40XTtlbHNlIGZvcihsZXQgQz0wO0M8MjU2O0MrKylIZVtDXT1kdFtDJjE1XXxkdFtDPj4+NF08PDgsbXRbQ109eXRbQyYxNV18eXRbQz4+PjRdPDxcbjg7ci5ieXRlT2Zmc2V0JTQhPT0wJiYocj1uZXcgVWludDhBcnJheShyKSk7bGV0IG49ci5sZW5ndGgsaT1uPj4+MSxzPW4+Pj4yLG89dHx8bmV3IFVpbnQxNkFycmF5KG4pLHU9bmV3IFVpbnQzMkFycmF5KFxuci5idWZmZXIsci5ieXRlT2Zmc2V0LHMpLGM9bmV3IFVpbnQzMkFycmF5KG8uYnVmZmVyLG8uYnl0ZU9mZnNldCxpKSxsPWU9PT1cInVwcGVyXCI/bXQ6SGUsZj0wLHk9MCxnO2lmKGJpKVxuZm9yKDtmPHM7KWc9dVtmKytdLGNbeSsrXT1sW2c+Pj44JjI1NV08PDE2fGxbZyYyNTVdLGNbeSsrXT1sW2c+Pj4yNF08PDE2fGxbZz4+PjE2JjI1NV07ZWxzZSBmb3IoO2Y8czspXG5nPXVbZisrXSxjW3krK109bFtnPj4+MjRdPDwxNnxsW2c+Pj4xNiYyNTVdLGNbeSsrXT1sW2c+Pj44JjI1NV08PDE2fGxbZyYyNTVdO2ZvcihmPDw9MjtmPG47KW9bZl09bFtyW2YrK11dO1xucmV0dXJuIGJhLmRlY29kZShvLnN1YmFycmF5KDAsbikpfWEoeGEsXCJfdG9IZXhcIik7ZnVuY3Rpb24gU2EocixlPXt9KXtsZXQgdD1cIlwiLG49ci5sZW5ndGgsaT13YT4+PjEscz1NYXRoLlxuY2VpbChuL2kpLG89bmV3IFVpbnQxNkFycmF5KHM+MT9pOm4pO2ZvcihsZXQgdT0wO3U8czt1Kyspe2xldCBjPXUqaSxsPWMraTt0Kz14YShyLnN1YmFycmF5KGMsbCksZ2EobWEoXG57fSxlKSx7c2NyYXRjaEFycjpvfSkpfXJldHVybiB0fWEoU2EsXCJfdG9IZXhDaHVua2VkXCIpO2Z1bmN0aW9uIHhpKHIsZT17fSl7cmV0dXJuIGUuYWxwaGFiZXQhPT1cInVwcGVyXCImJlxudHlwZW9mIHIudG9IZXg9PVwiZnVuY3Rpb25cIj9yLnRvSGV4KCk6U2EocixlKX1hKHhpLFwidG9IZXhcIik7cCgpO3ZhciBndD1jbGFzcyBndHtjb25zdHJ1Y3RvcihlLHQpe3RoaXMuc3RyaW5ncz1lO3RoaXMudmFsdWVzPXR9dG9QYXJhbWV0ZXJpemVkUXVlcnkoZT17cXVlcnk6XCJcIixwYXJhbXM6W119KXtcbmxldHtzdHJpbmdzOnQsdmFsdWVzOm59PXRoaXM7Zm9yKGxldCBpPTAscz10Lmxlbmd0aDtpPHM7aSsrKWlmKGUucXVlcnkrPXRbaV0saTxuLmxlbmd0aCl7bGV0IG89bltpXTtcbmlmKG8gaW5zdGFuY2VvZiBHZSllLnF1ZXJ5Kz1vLnNxbDtlbHNlIGlmKG8gaW5zdGFuY2VvZiBBZSlpZihvLnF1ZXJ5RGF0YSBpbnN0YW5jZW9mIGd0KW8ucXVlcnlEYXRhLnRvUGFyYW1ldGVyaXplZFF1ZXJ5KFxuZSk7ZWxzZXtpZihvLnF1ZXJ5RGF0YS5wYXJhbXM/Lmxlbmd0aCl0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIHF1ZXJ5IGlzIG5vdCBjb21wb3NhYmxlXCIpO2UucXVlcnkrPW8ucXVlcnlEYXRhLlxucXVlcnl9ZWxzZXtsZXR7cGFyYW1zOnV9PWU7dS5wdXNoKG8pLGUucXVlcnkrPVwiJFwiK3UubGVuZ3RoLChvIGluc3RhbmNlb2YgZHx8QXJyYXlCdWZmZXIuaXNWaWV3KG8pKSYmXG4oZS5xdWVyeSs9XCI6OmJ5dGVhXCIpfX1yZXR1cm4gZX19O2EoZ3QsXCJTcWxUZW1wbGF0ZVwiKTt2YXIgJGU9Z3QsWXQ9Y2xhc3MgWXR7Y29uc3RydWN0b3IoZSl7dGhpcy5zcWw9ZX19O1xuYShZdCxcIlVuc2FmZVJhd1NxbFwiKTt2YXIgR2U9WXQ7dmFyIHNzPXhlKFN0KCkpLG9zPXhlKHJ0KCkpO3ZhciBBdD1jbGFzcyBBdCBleHRlbmRzIEVycm9ye2NvbnN0cnVjdG9yKHQpe3N1cGVyKHQpO0UodGhpcyxcIm5hbWVcIixcIk5lb25EYkVycm9yXCIpO0UodGhpcyxcInNldmVyaXR5XCIpO1xuRSh0aGlzLFwiY29kZVwiKTtFKHRoaXMsXCJkZXRhaWxcIik7RSh0aGlzLFwiaGludFwiKTtFKHRoaXMsXCJwb3NpdGlvblwiKTtFKHRoaXMsXCJpbnRlcm5hbFBvc2l0aW9uXCIpO0UodGhpcyxcblwiaW50ZXJuYWxRdWVyeVwiKTtFKHRoaXMsXCJ3aGVyZVwiKTtFKHRoaXMsXCJzY2hlbWFcIik7RSh0aGlzLFwidGFibGVcIik7RSh0aGlzLFwiY29sdW1uXCIpO0UodGhpcyxcImRhdGFUeXBlXCIpO1xuRSh0aGlzLFwiY29uc3RyYWludFwiKTtFKHRoaXMsXCJmaWxlXCIpO0UodGhpcyxcImxpbmVcIik7RSh0aGlzLFwicm91dGluZVwiKTtFKHRoaXMsXCJzb3VyY2VFcnJvclwiKTtcImNhcHR1cmVTXFxcbnRhY2tUcmFjZVwiaW4gRXJyb3ImJnR5cGVvZiBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZT09XCJmdW5jdGlvblwiJiZFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLEF0KX19O2EoXG5BdCxcIk5lb25EYkVycm9yXCIpO3ZhciB3ZT1BdCxycz1cInRyYW5zYWN0aW9uKCkgZXhwZWN0cyBhbiBhcnJheSBvZiBxdWVyaWVzLCBvciBhIGZ1bmN0aW9uIHJldHVybmluZyBhXFxcbm4gYXJyYXkgb2YgcXVlcmllc1wiLFB1PVtcInNldmVyaXR5XCIsXCJjb2RlXCIsXCJkZXRhaWxcIixcImhpbnRcIixcInBvc2l0aW9uXCIsXCJpbnRlcm5hbFBvc2l0aW9uXCIsXCJpbnRlcm5hbFF1ZVxcXG5yeVwiLFwid2hlcmVcIixcInNjaGVtYVwiLFwidGFibGVcIixcImNvbHVtblwiLFwiZGF0YVR5cGVcIixcImNvbnN0cmFpbnRcIixcImZpbGVcIixcImxpbmVcIixcInJvdXRpbmVcIl07ZnVuY3Rpb24gUnUocil7XG5yZXR1cm4gciBpbnN0YW5jZW9mIGQ/XCJcXFxceFwiK3hpKHIpOnJ9YShSdSxcImVuY29kZUJ1ZmZlcnNBc0J5dGVhXCIpO2Z1bmN0aW9uIG5zKHIpe2xldHtxdWVyeTplLHBhcmFtczp0fT1yIGluc3RhbmNlb2ZcbiRlP3IudG9QYXJhbWV0ZXJpemVkUXVlcnkoKTpyO3JldHVybntxdWVyeTplLHBhcmFtczp0Lm1hcChuPT5SdSgoMCxvcy5wcmVwYXJlVmFsdWUpKG4pKSl9fWEobnMsXCJwcmVwXFxcbmFyZVF1ZXJ5XCIpO2Z1bmN0aW9uIGFzKHIse2FycmF5TW9kZTplLGZ1bGxSZXN1bHRzOnQsZmV0Y2hPcHRpb25zOm4saXNvbGF0aW9uTGV2ZWw6aSxyZWFkT25seTpzLGRlZmVycmFibGU6byxcbmF1dGhUb2tlbjp1fT17fSl7aWYoIXIpdGhyb3cgbmV3IEVycm9yKFwiTm8gZGF0YWJhc2UgY29ubmVjdGlvbiBzdHJpbmcgd2FzIHByb3ZpZGVkIHRvIGBuZW9uKClgLiBQZXJoXFxcbmFwcyBhbiBlbnZpcm9ubWVudCB2YXJpYWJsZSBoYXMgbm90IGJlZW4gc2V0P1wiKTtsZXQgYzt0cnl7Yz1WdChyKX1jYXRjaHt0aHJvdyBuZXcgRXJyb3IoXCJEYXRhYmFzZSBjb1xcXG5ubmVjdGlvbiBzdHJpbmcgcHJvdmlkZWQgdG8gYG5lb24oKWAgaXMgbm90IGEgdmFsaWQgVVJMLiBDb25uZWN0aW9uIHN0cmluZzogXCIrU3RyaW5nKHIpKX1sZXR7cHJvdG9jb2w6bCxcbnVzZXJuYW1lOmYsaG9zdG5hbWU6eSxwb3J0OmcscGF0aG5hbWU6QX09YztpZihsIT09XCJwb3N0Z3JlczpcIiYmbCE9PVwicG9zdGdyZXNxbDpcInx8IWZ8fCF5fHwhQSl0aHJvdyBuZXcgRXJyb3IoXG5cIkRhdGFiYXNlIGNvbm5lY3Rpb24gc3RyaW5nIGZvcm1hdCBmb3IgYG5lb24oKWAgc2hvdWxkIGJlOiBwb3N0Z3Jlc3FsOi8vdXNlcjpwYXNzd29yZEBob3N0LnRsZC9kYm5hbVxcXG5lP29wdGlvbj12YWx1ZVwiKTtmdW5jdGlvbiBDKFAsLi4uTCl7aWYoIShBcnJheS5pc0FycmF5KFApJiZBcnJheS5pc0FycmF5KFAucmF3KSYmQXJyYXkuaXNBcnJheShMKSkpdGhyb3cgbmV3IEVycm9yKFxuJ1RoaXMgZnVuY3Rpb24gY2FuIG5vdyBiZSBjYWxsZWQgb25seSBhcyBhIHRhZ2dlZC10ZW1wbGF0ZSBmdW5jdGlvbjogc3FsYFNFTEVDVCAke3ZhbHVlfWAsIG5vdCBzcWwoXCJcXFxuU0VMRUNUICQxXCIsIFt2YWx1ZV0sIG9wdGlvbnMpLiBGb3IgYSBjb252ZW50aW9uYWwgZnVuY3Rpb24gY2FsbCB3aXRoIHZhbHVlIHBsYWNlaG9sZGVycyAoJDEsICQyLCBldGNcXFxuLiksIHVzZSBzcWwucXVlcnkoXCJTRUxFQ1QgJDFcIiwgW3ZhbHVlXSwgb3B0aW9ucykuJyk7cmV0dXJuIG5ldyBBZShRLG5ldyAkZShQLEwpKX1hKEMsXCJ0ZW1wbGF0ZUZuXCIpLEMuXG5xdWVyeT0oUCxMLF8pPT5uZXcgQWUoUSx7cXVlcnk6UCxwYXJhbXM6TD8/W119LF8pLEMudW5zYWZlPVA9Pm5ldyBHZShQKSxDLnRyYW5zYWN0aW9uPWFzeW5jKFAsTCk9PntpZih0eXBlb2YgUD09XG5cImZ1bmN0aW9uXCImJihQPVAoQykpLCFBcnJheS5pc0FycmF5KFApKXRocm93IG5ldyBFcnJvcihycyk7UC5mb3JFYWNoKEg9PntpZighKEggaW5zdGFuY2VvZiBBZSkpdGhyb3cgbmV3IEVycm9yKFxucnMpfSk7bGV0IF89UC5tYXAoSD0+SC5xdWVyeURhdGEpLHg9UC5tYXAoSD0+SC5vcHRzPz97fSk7cmV0dXJuIFEoXyx4LEwpfTthc3luYyBmdW5jdGlvbiBRKFAsTCxfKXtsZXR7XG5mZXRjaEVuZHBvaW50OngsZmV0Y2hGdW5jdGlvbjpIfT1nZSxsZT1BcnJheS5pc0FycmF5KFApP3txdWVyaWVzOlAubWFwKEo9Pm5zKEopKX06bnMoUCksTj1uPz97fSxpZT1lPz9cbiExLHNlPXQ/PyExLG9lPWksQj1zLCQ9bztfIT09dm9pZCAwJiYoXy5mZXRjaE9wdGlvbnMhPT12b2lkIDAmJihOPXsuLi5OLC4uLl8uZmV0Y2hPcHRpb25zfSksXy5hcnJheU1vZGUhPT1cbnZvaWQgMCYmKGllPV8uYXJyYXlNb2RlKSxfLmZ1bGxSZXN1bHRzIT09dm9pZCAwJiYoc2U9Xy5mdWxsUmVzdWx0cyksXy5pc29sYXRpb25MZXZlbCE9PXZvaWQgMCYmKG9lPV8uXG5pc29sYXRpb25MZXZlbCksXy5yZWFkT25seSE9PXZvaWQgMCYmKEI9Xy5yZWFkT25seSksXy5kZWZlcnJhYmxlIT09dm9pZCAwJiYoJD1fLmRlZmVycmFibGUpKSxMIT09dm9pZCAwJiZcbiFBcnJheS5pc0FycmF5KEwpJiZMLmZldGNoT3B0aW9ucyE9PXZvaWQgMCYmKE49ey4uLk4sLi4uTC5mZXRjaE9wdGlvbnN9KTtsZXQgZmU9dTshQXJyYXkuaXNBcnJheShMKSYmXG5MPy5hdXRoVG9rZW4hPT12b2lkIDAmJihmZT1MLmF1dGhUb2tlbik7bGV0IENlPXR5cGVvZiB4PT1cImZ1bmN0aW9uXCI/eCh5LGcse2p3dEF1dGg6ZmUhPT12b2lkIDB9KTp4LGhlPXtcblwiTmVvbi1Db25uZWN0aW9uLVN0cmluZ1wiOnIsXCJOZW9uLVJhdy1UZXh0LU91dHB1dFwiOlwidHJ1ZVwiLFwiTmVvbi1BcnJheS1Nb2RlXCI6XCJ0cnVlXCJ9LF9lPWF3YWl0IEJ1KGZlKTtfZSYmXG4oaGUuQXV0aG9yaXphdGlvbj1gQmVhcmVyICR7X2V9YCksQXJyYXkuaXNBcnJheShQKSYmKG9lIT09dm9pZCAwJiYoaGVbXCJOZW9uLUJhdGNoLUlzb2xhdGlvbi1MZXZlbFwiXT1cbm9lKSxCIT09dm9pZCAwJiYoaGVbXCJOZW9uLUJhdGNoLVJlYWQtT25seVwiXT1TdHJpbmcoQikpLCQhPT12b2lkIDAmJihoZVtcIk5lb24tQmF0Y2gtRGVmZXJyYWJsZVwiXT1TdHJpbmcoXG4kKSkpO2xldCBhZTt0cnl7YWU9YXdhaXQoSD8/ZmV0Y2gpKENlLHttZXRob2Q6XCJQT1NUXCIsYm9keTpKU09OLnN0cmluZ2lmeShsZSksaGVhZGVyczpoZSwuLi5OfSl9Y2F0Y2goSil7XG5sZXQgaj1uZXcgd2UoYEVycm9yIGNvbm5lY3RpbmcgdG8gZGF0YWJhc2U6ICR7Sn1gKTt0aHJvdyBqLnNvdXJjZUVycm9yPUosan1pZihhZS5vayl7bGV0IEo9YXdhaXQgYWUuXG5qc29uKCk7aWYoQXJyYXkuaXNBcnJheShQKSl7bGV0IGo9Si5yZXN1bHRzO2lmKCFBcnJheS5pc0FycmF5KGopKXRocm93IG5ldyB3ZShcIk5lb24gaW50ZXJuYWwgZXJyb3I6IFxcXG51bmV4cGVjdGVkIHJlc3VsdCBmb3JtYXRcIik7cmV0dXJuIGoubWFwKChYLFYpPT57bGV0IE5lPUxbVl0/P3t9LHdvPU5lLmFycmF5TW9kZT8/aWUsYm89TmUuZnVsbFJlc3VsdHM/P1xuc2U7cmV0dXJuIGlzKFgse2FycmF5TW9kZTp3byxmdWxsUmVzdWx0czpibyx0eXBlczpOZS50eXBlc30pfSl9ZWxzZXtsZXQgaj1MPz97fSxYPWouYXJyYXlNb2RlPz9pZSxWPWouXG5mdWxsUmVzdWx0cz8/c2U7cmV0dXJuIGlzKEose2FycmF5TW9kZTpYLGZ1bGxSZXN1bHRzOlYsdHlwZXM6ai50eXBlc30pfX1lbHNle2xldHtzdGF0dXM6Sn09YWU7aWYoSj09PVxuNDAwKXtsZXQgaj1hd2FpdCBhZS5qc29uKCksWD1uZXcgd2Uoai5tZXNzYWdlKTtmb3IobGV0IFYgb2YgUHUpWFtWXT1qW1ZdPz92b2lkIDA7dGhyb3cgWH1lbHNle2xldCBqPWF3YWl0IGFlLlxudGV4dCgpO3Rocm93IG5ldyB3ZShgU2VydmVyIGVycm9yIChIVFRQIHN0YXR1cyAke0p9KTogJHtqfWApfX19cmV0dXJuIGEoUSxcImV4ZWN1dGVcIiksQ31hKGFzLFwibmVvblwiKTtcbnZhciBmcj1jbGFzcyBmcntjb25zdHJ1Y3RvcihlLHQsbil7dGhpcy5leGVjdXRlPWU7dGhpcy5xdWVyeURhdGE9dDt0aGlzLm9wdHM9bn10aGVuKGUsdCl7cmV0dXJuIHRoaXMuXG5leGVjdXRlKHRoaXMucXVlcnlEYXRhLHRoaXMub3B0cykudGhlbihlLHQpfWNhdGNoKGUpe3JldHVybiB0aGlzLmV4ZWN1dGUodGhpcy5xdWVyeURhdGEsdGhpcy5vcHRzKS5jYXRjaChcbmUpfWZpbmFsbHkoZSl7cmV0dXJuIHRoaXMuZXhlY3V0ZSh0aGlzLnF1ZXJ5RGF0YSx0aGlzLm9wdHMpLmZpbmFsbHkoZSl9fTthKGZyLFwiTmVvblF1ZXJ5UHJvbWlzZVwiKTt2YXIgQWU9ZnI7XG5mdW5jdGlvbiBpcyhyLHthcnJheU1vZGU6ZSxmdWxsUmVzdWx0czp0LHR5cGVzOm59KXtsZXQgaT1uZXcgc3MuZGVmYXVsdChuKSxzPXIuZmllbGRzLm1hcChjPT5jLm5hbWUpLFxubz1yLmZpZWxkcy5tYXAoYz0+aS5nZXRUeXBlUGFyc2VyKGMuZGF0YVR5cGVJRCkpLHU9ZT09PSEwP3Iucm93cy5tYXAoYz0+Yy5tYXAoKGwsZik9Pmw9PT1udWxsP251bGw6b1tmXShcbmwpKSk6ci5yb3dzLm1hcChjPT5PYmplY3QuZnJvbUVudHJpZXMoYy5tYXAoKGwsZik9PltzW2ZdLGw9PT1udWxsP251bGw6b1tmXShsKV0pKSk7cmV0dXJuIHQ/KHIudmlhTmVvbkZldGNoPVxuITAsci5yb3dBc0FycmF5PWUsci5yb3dzPXUsci5fcGFyc2Vycz1vLHIuX3R5cGVzPWkscik6dX1hKGlzLFwicHJvY2Vzc1F1ZXJ5UmVzdWx0XCIpO2FzeW5jIGZ1bmN0aW9uIEJ1KHIpe1xuaWYodHlwZW9mIHI9PVwic3RyaW5nXCIpcmV0dXJuIHI7aWYodHlwZW9mIHI9PVwiZnVuY3Rpb25cIil0cnl7cmV0dXJuIGF3YWl0IFByb21pc2UucmVzb2x2ZShyKCkpfWNhdGNoKGUpe1xubGV0IHQ9bmV3IHdlKFwiRXJyb3IgZ2V0dGluZyBhdXRoIHRva2VuLlwiKTt0aHJvdyBlIGluc3RhbmNlb2YgRXJyb3ImJih0PW5ldyB3ZShgRXJyb3IgZ2V0dGluZyBhdXRoIHRvXFxcbmtlbjogJHtlLm1lc3NhZ2V9YCkpLHR9fWEoQnUsXCJnZXRBdXRoVG9rZW5cIik7cCgpO3ZhciBtbz14ZShvdCgpKTtwKCk7dmFyIHlvPXhlKG90KCkpO3ZhciBCbj1jbGFzcyBCbiBleHRlbmRzIHlvLkNsaWVudHtjb25zdHJ1Y3Rvcih0KXtzdXBlcih0KTt0aGlzLmNvbmZpZz10fWdldCBuZW9uQ29uZmlnKCl7cmV0dXJuIHRoaXMuXG5jb25uZWN0aW9uLnN0cmVhbX1jb25uZWN0KHQpe2xldHtuZW9uQ29uZmlnOm59PXRoaXM7bi5mb3JjZURpc2FibGVQZ1NTTCYmKHRoaXMuc3NsPXRoaXMuY29ubmVjdGlvbi5zc2w9XG4hMSksdGhpcy5zc2wmJm4udXNlU2VjdXJlV2ViU29ja2V0JiZjb25zb2xlLndhcm4oXCJTU0wgaXMgZW5hYmxlZCBmb3IgYm90aCBQb3N0Z3JlcyAoZS5nLiA/c3NsbW9kZT1yZVxcXG5xdWlyZSBpbiB0aGUgY29ubmVjdGlvbiBzdHJpbmcgKyBmb3JjZURpc2FibGVQZ1NTTCA9IGZhbHNlKSBhbmQgdGhlIFdlYlNvY2tldCB0dW5uZWwgKHVzZVNlY3VyZVdlYlNvXFxcbmNrZXQgPSB0cnVlKS4gRG91YmxlIGVuY3J5cHRpb24gd2lsbCBpbmNyZWFzZSBsYXRlbmN5IGFuZCBDUFUgdXNhZ2UuIEl0IG1heSBiZSBhcHByb3ByaWF0ZSB0byBkaXNhYmxcXFxuZSBTU0wgaW4gdGhlIFBvc3RncmVzIGNvbm5lY3Rpb24gcGFyYW1ldGVycyBvciBzZXQgZm9yY2VEaXNhYmxlUGdTU0wgPSB0cnVlLlwiKTtsZXQgaT10eXBlb2YgdGhpcy5jb25maWchPVxuXCJzdHJpbmdcIiYmdGhpcy5jb25maWc/Lmhvc3QhPT12b2lkIDB8fHR5cGVvZiB0aGlzLmNvbmZpZyE9XCJzdHJpbmdcIiYmdGhpcy5jb25maWc/LmNvbm5lY3Rpb25TdHJpbmchPT1cbnZvaWQgMHx8bS5lbnYuUEdIT1NUIT09dm9pZCAwLHM9bS5lbnYuVVNFUj8/bS5lbnYuVVNFUk5BTUU7aWYoIWkmJnRoaXMuaG9zdD09PVwibG9jYWxob3N0XCImJnRoaXMudXNlcj09PVxucyYmdGhpcy5kYXRhYmFzZT09PXMmJnRoaXMucGFzc3dvcmQ9PT1udWxsKXRocm93IG5ldyBFcnJvcihgTm8gZGF0YWJhc2UgaG9zdCBvciBjb25uZWN0aW9uIHN0cmluZyB3YVxcXG5zIHNldCwgYW5kIGtleSBwYXJhbWV0ZXJzIGhhdmUgZGVmYXVsdCB2YWx1ZXMgKGhvc3Q6IGxvY2FsaG9zdCwgdXNlcjogJHtzfSwgZGI6ICR7c30sIHBhc3N3b3JkOiBudWxsXFxcbikuIElzIGFuIGVudmlyb25tZW50IHZhcmlhYmxlIG1pc3Npbmc/IEFsdGVybmF0aXZlbHksIGlmIHlvdSBpbnRlbmRlZCB0byBjb25uZWN0IHdpdGggdGhlc2UgcGFyYW1ldGVcXFxucnMsIHBsZWFzZSBzZXQgdGhlIGhvc3QgdG8gJ2xvY2FsaG9zdCcgZXhwbGljaXRseS5gKTtsZXQgbz1zdXBlci5jb25uZWN0KHQpLHU9bi5waXBlbGluZVRMUyYmdGhpcy5zc2wsXG5jPW4ucGlwZWxpbmVDb25uZWN0PT09XCJwYXNzd29yZFwiO2lmKCF1JiYhbi5waXBlbGluZUNvbm5lY3QpcmV0dXJuIG87bGV0IGw9dGhpcy5jb25uZWN0aW9uO2lmKHUmJmwub24oXG5cImNvbm5lY3RcIiwoKT0+bC5zdHJlYW0uZW1pdChcImRhdGFcIixcIlNcIikpLGMpe2wucmVtb3ZlQWxsTGlzdGVuZXJzKFwiYXV0aGVudGljYXRpb25DbGVhcnRleHRQYXNzd29yZFwiKSxcbmwucmVtb3ZlQWxsTGlzdGVuZXJzKFwicmVhZHlGb3JRdWVyeVwiKSxsLm9uY2UoXCJyZWFkeUZvclF1ZXJ5XCIsKCk9Pmwub24oXCJyZWFkeUZvclF1ZXJ5XCIsdGhpcy5faGFuZGxlUmVhZHlGb3JRdWVyeS5cbmJpbmQodGhpcykpKTtsZXQgZj10aGlzLnNzbD9cInNzbGNvbm5lY3RcIjpcImNvbm5lY3RcIjtsLm9uKGYsKCk9Pnt0aGlzLl9oYW5kbGVBdXRoQ2xlYXJ0ZXh0UGFzc3dvcmQoKSx0aGlzLlxuX2hhbmRsZVJlYWR5Rm9yUXVlcnkoKX0pfXJldHVybiBvfWFzeW5jIF9oYW5kbGVBdXRoU0FTTENvbnRpbnVlKHQpe2lmKHR5cGVvZiBjcnlwdG8+XCJ1XCJ8fGNyeXB0by5zdWJ0bGU9PT1cbnZvaWQgMHx8Y3J5cHRvLnN1YnRsZS5pbXBvcnRLZXk9PT12b2lkIDApdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IHVzZSBTQVNMIGF1dGggd2hlbiBgY3J5cHRvLnN1YnRsZWAgXFxcbmlzIG5vdCBkZWZpbmVkXCIpO2xldCBuPWNyeXB0by5zdWJ0bGUsaT10aGlzLnNhc2xTZXNzaW9uLHM9dGhpcy5wYXNzd29yZCxvPXQuZGF0YTtpZihpLm1lc3NhZ2UhPT1cIlNBU1xcXG5MSW5pdGlhbFJlc3BvbnNlXCJ8fHR5cGVvZiBzIT1cInN0cmluZ1wifHx0eXBlb2YgbyE9XCJzdHJpbmdcIil0aHJvdyBuZXcgRXJyb3IoXCJTQVNMOiBwcm90b2NvbCBlcnJvclwiKTtsZXQgdT1PYmplY3QuXG5mcm9tRW50cmllcyhvLnNwbGl0KFwiLFwiKS5tYXAoWD0+e2lmKCEvXi49Ly50ZXN0KFgpKXRocm93IG5ldyBFcnJvcihcIlNBU0w6IEludmFsaWQgYXR0cmlidXRlIHBhaXIgZW50XFxcbnJ5XCIpO2xldCBWPVhbMF0sTmU9WC5zdWJzdHJpbmcoMik7cmV0dXJuW1YsTmVdfSkpLGM9dS5yLGw9dS5zLGY9dS5pO2lmKCFjfHwhL15bIS0rLS1+XSskLy50ZXN0KGMpKXRocm93IG5ldyBFcnJvcihcblwiU0FTTDogU0NSQU0tU0VSVkVSLUZJUlNULU1FU1NBR0U6IG5vbmNlIG1pc3NpbmcvdW5wcmludGFibGVcIik7aWYoIWx8fCEvXig/OlthLXpBLVowLTkrL117NH0pKig/OlthLXpBLVowLTkrL117Mn09PXxbYS16QS1aMC05Ky9dezN9PSk/JC8uXG50ZXN0KGwpKXRocm93IG5ldyBFcnJvcihcIlNBU0w6IFNDUkFNLVNFUlZFUi1GSVJTVC1NRVNTQUdFOiBzYWx0IG1pc3Npbmcvbm90IGJhc2U2NFwiKTtpZighZnx8IS9eWzEtOV1bMC05XSokLy5cbnRlc3QoZikpdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVkVSLUZJUlNULU1FU1NBR0U6IG1pc3NpbmcvaW52YWxpZCBpdGVyYXRpb24gY291bnRcIik7aWYoIWMuc3RhcnRzV2l0aChcbmkuY2xpZW50Tm9uY2UpKXRocm93IG5ldyBFcnJvcihcIlNBU0w6IFNDUkFNLVNFUlZFUi1GSVJTVC1NRVNTQUdFOiBzZXJ2ZXIgbm9uY2UgZG9lcyBub3Qgc3RhcnQgd2l0aCBjXFxcbmxpZW50IG5vbmNlXCIpO2lmKGMubGVuZ3RoPT09aS5jbGllbnROb25jZS5sZW5ndGgpdGhyb3cgbmV3IEVycm9yKFwiU0FTTDogU0NSQU0tU0VSVkVSLUZJUlNULU1FU1NBR0U6IFxcXG5zZXJ2ZXIgbm9uY2UgaXMgdG9vIHNob3J0XCIpO2xldCB5PXBhcnNlSW50KGYsMTApLGc9ZC5mcm9tKGwsXCJiYXNlNjRcIiksQT1uZXcgVGV4dEVuY29kZXIsQz1BLmVuY29kZShzKSxcblE9YXdhaXQgbi5pbXBvcnRLZXkoXCJyYXdcIixDLHtuYW1lOlwiSE1BQ1wiLGhhc2g6e25hbWU6XCJTSEEtMjU2XCJ9fSwhMSxbXCJzaWduXCJdKSxQPW5ldyBVaW50OEFycmF5KGF3YWl0IG4uXG5zaWduKFwiSE1BQ1wiLFEsZC5jb25jYXQoW2csZC5mcm9tKFswLDAsMCwxXSldKSkpLEw9UDtmb3IodmFyIF89MDtfPHktMTtfKyspUD1uZXcgVWludDhBcnJheShhd2FpdCBuLnNpZ24oXG5cIkhNQUNcIixRLFApKSxMPWQuZnJvbShMLm1hcCgoWCxWKT0+TFtWXV5QW1ZdKSk7bGV0IHg9TCxIPWF3YWl0IG4uaW1wb3J0S2V5KFwicmF3XCIseCx7bmFtZTpcIkhNQUNcIixoYXNoOntcbm5hbWU6XCJTSEEtMjU2XCJ9fSwhMSxbXCJzaWduXCJdKSxsZT1uZXcgVWludDhBcnJheShhd2FpdCBuLnNpZ24oXCJITUFDXCIsSCxBLmVuY29kZShcIkNsaWVudCBLZXlcIikpKSxOPWF3YWl0IG4uXG5kaWdlc3QoXCJTSEEtMjU2XCIsbGUpLGllPVwibj0qLHI9XCIraS5jbGllbnROb25jZSxzZT1cInI9XCIrYytcIixzPVwiK2wrXCIsaT1cIit5LG9lPVwiYz1iaXdzLHI9XCIrYyxCPWllK1wiLFwiK3NlK1xuXCIsXCIrb2UsJD1hd2FpdCBuLmltcG9ydEtleShcInJhd1wiLE4se25hbWU6XCJITUFDXCIsaGFzaDp7bmFtZTpcIlNIQS0yNTZcIn19LCExLFtcInNpZ25cIl0pO3ZhciBmZT1uZXcgVWludDhBcnJheShcbmF3YWl0IG4uc2lnbihcIkhNQUNcIiwkLEEuZW5jb2RlKEIpKSksQ2U9ZC5mcm9tKGxlLm1hcCgoWCxWKT0+bGVbVl1eZmVbVl0pKSxoZT1DZS50b1N0cmluZyhcImJhc2U2NFwiKTtsZXQgX2U9YXdhaXQgbi5cbmltcG9ydEtleShcInJhd1wiLHgse25hbWU6XCJITUFDXCIsaGFzaDp7bmFtZTpcIlNIQS0yNTZcIn19LCExLFtcInNpZ25cIl0pLGFlPWF3YWl0IG4uc2lnbihcIkhNQUNcIixfZSxBLmVuY29kZShcblwiU2VydmVyIEtleVwiKSksSj1hd2FpdCBuLmltcG9ydEtleShcInJhd1wiLGFlLHtuYW1lOlwiSE1BQ1wiLGhhc2g6e25hbWU6XCJTSEEtMjU2XCJ9fSwhMSxbXCJzaWduXCJdKTt2YXIgaj1kLlxuZnJvbShhd2FpdCBuLnNpZ24oXCJITUFDXCIsSixBLmVuY29kZShCKSkpO2kubWVzc2FnZT1cIlNBU0xSZXNwb25zZVwiLGkuc2VydmVyU2lnbmF0dXJlPWoudG9TdHJpbmcoXCJiYXNlXFxcbjY0XCIpLGkucmVzcG9uc2U9b2UrXCIscD1cIitoZSx0aGlzLmNvbm5lY3Rpb24uc2VuZFNDUkFNQ2xpZW50RmluYWxNZXNzYWdlKHRoaXMuc2FzbFNlc3Npb24ucmVzcG9uc2UpfX07XG5hKEJuLFwiTmVvbkNsaWVudFwiKTt2YXIgdXQ9Qm47V2UoKTt2YXIgZ289eGUoSXQoKSk7ZnVuY3Rpb24gd2wocixlKXtpZihlKXJldHVybntjYWxsYmFjazplLHJlc3VsdDp2b2lkIDB9O2xldCB0LG4saT1hKGZ1bmN0aW9uKG8sdSl7bz90KG8pOm4odSl9LFwiY2JcIiksXG5zPW5ldyByKGZ1bmN0aW9uKG8sdSl7bj1vLHQ9dX0pO3JldHVybntjYWxsYmFjazppLHJlc3VsdDpzfX1hKHdsLFwicHJvbWlzaWZ5XCIpO3ZhciBGbj1jbGFzcyBGbiBleHRlbmRzIG1vLlBvb2x7Y29uc3RydWN0b3IoKXtcbnN1cGVyKC4uLmFyZ3VtZW50cyk7RSh0aGlzLFwiQ2xpZW50XCIsdXQpO0UodGhpcyxcImhhc0ZldGNoVW5zdXBwb3J0ZWRMaXN0ZW5lcnNcIiwhMSk7RSh0aGlzLFwiYWRkTGlzdGVuZVxcXG5yXCIsdGhpcy5vbil9b24odCxuKXtyZXR1cm4gdCE9PVwiZXJyb3JcIiYmKHRoaXMuaGFzRmV0Y2hVbnN1cHBvcnRlZExpc3RlbmVycz0hMCksc3VwZXIub24odCxuKX1xdWVyeSh0LG4saSl7XG5pZighZ2UucG9vbFF1ZXJ5VmlhRmV0Y2h8fHRoaXMuaGFzRmV0Y2hVbnN1cHBvcnRlZExpc3RlbmVyc3x8dHlwZW9mIHQ9PVwiZnVuY3Rpb25cIilyZXR1cm4gc3VwZXIucXVlcnkoXG50LG4saSk7dHlwZW9mIG49PVwiZnVuY3Rpb25cIiYmKGk9bixuPXZvaWQgMCk7bGV0IHM9d2wodGhpcy5Qcm9taXNlLGkpO2k9cy5jYWxsYmFjazt0cnl7bGV0IG89bmV3IGdvLmRlZmF1bHQoXG50aGlzLm9wdGlvbnMpLHU9ZW5jb2RlVVJJQ29tcG9uZW50LGM9ZW5jb2RlVVJJLGw9YHBvc3RncmVzcWw6Ly8ke3Uoby51c2VyKX06JHt1KG8ucGFzc3dvcmQpfUAke3Uoby5ob3N0KX1cXFxuLyR7YyhvLmRhdGFiYXNlKX1gLGY9dHlwZW9mIHQ9PVwic3RyaW5nXCI/dDp0LnRleHQseT1uPz90LnZhbHVlcz8/W107YXMobCx7ZnVsbFJlc3VsdHM6ITAsYXJyYXlNb2RlOnQuXG5yb3dNb2RlPT09XCJhcnJheVwifSkucXVlcnkoZix5LHt0eXBlczp0LnR5cGVzPz90aGlzLm9wdGlvbnM/LnR5cGVzfSkudGhlbihBPT5pKHZvaWQgMCxBKSkuY2F0Y2goQT0+aShcbkEpKX1jYXRjaChvKXtpKG8pfXJldHVybiBzLnJlc3VsdH19O2EoRm4sXCJOZW9uUG9vbFwiKTt2YXIgTG49Rm47V2UoKTt2YXIga249eGUob3QoKSksX3A9XCJtanNcIjt2YXIgZXhwb3J0X0RhdGFiYXNlRXJyb3I9a24uRGF0YWJhc2VFcnJvcjt2YXIgZXhwb3J0X2RlZmF1bHRzPWtuLmRlZmF1bHRzO3ZhciBleHBvcnRfdHlwZXM9a24udHlwZXM7XG5leHBvcnR7dXQgYXMgQ2xpZW50LGV4cG9ydF9EYXRhYmFzZUVycm9yIGFzIERhdGFiYXNlRXJyb3Isd2UgYXMgTmVvbkRiRXJyb3IsQWUgYXMgTmVvblF1ZXJ5UHJvbWlzZSxMbiBhcyBQb29sLFxuJGUgYXMgU3FsVGVtcGxhdGUsR2UgYXMgVW5zYWZlUmF3U3FsLF9wIGFzIF9idW5kbGVFeHQsZXhwb3J0X2RlZmF1bHRzIGFzIGRlZmF1bHRzLGFzIGFzIG5lb24sZ2UgYXMgbmVvbkNvbmZpZyxcbmV4cG9ydF90eXBlcyBhcyB0eXBlc307XG4vKiEgQnVuZGxlZCBsaWNlbnNlIGluZm9ybWF0aW9uOlxuXG5pZWVlNzU0L2luZGV4LmpzOlxuICAoKiEgaWVlZTc1NC4gQlNELTMtQ2xhdXNlIExpY2Vuc2UuIEZlcm9zcyBBYm91a2hhZGlqZWggPGh0dHBzOi8vZmVyb3NzLm9yZy9vcGVuc291cmNlPiAqKVxuXG5idWZmZXIvaW5kZXguanM6XG4gICgqIVxuICAgKiBUaGUgYnVmZmVyIG1vZHVsZSBmcm9tIG5vZGUuanMsIGZvciB0aGUgYnJvd3Nlci5cbiAgICpcbiAgICogQGF1dGhvciAgIEZlcm9zcyBBYm91a2hhZGlqZWggPGh0dHBzOi8vZmVyb3NzLm9yZz5cbiAgICogQGxpY2Vuc2UgIE1JVFxuICAgKilcbiovXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@neondatabase/serverless/index.mjs\n");

/***/ })

};
;