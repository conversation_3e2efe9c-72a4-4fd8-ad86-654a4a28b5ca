import { EmailService } from './email.service';
import { SendEmailDto } from './dto/send-email.dto';
export declare class EmailController {
    private readonly emailService;
    constructor(emailService: EmailService);
    sendEmail(sendEmailDto: SendEmailDto): Promise<{
        jobId: import("bull").JobId;
        message: string;
    }>;
    sendTestEmail(testData: {
        to: string;
        subject?: string;
        content?: string;
    }): Promise<{
        jobId: import("bull").JobId;
        message: string;
    }>;
    getTemplates(): Promise<{
        templates: string[];
    }>;
    getQueueStatus(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        total: number;
    }>;
    sendTemplateEmail(templateName: string, templateData: {
        to: string;
        variables: Record<string, any>;
    }): Promise<{
        jobId: import("bull").JobId;
        message: string;
    }>;
}
