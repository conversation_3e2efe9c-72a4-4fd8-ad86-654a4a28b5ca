import { EmailService } from './email.service';
import { SendEmailDto } from './dto/send-email.dto';
export declare class EmailController {
    private readonly emailService;
    constructor(emailService: EmailService);
    sendEmail(sendEmailDto: SendEmailDto): Promise<any>;
    sendTestEmail(testData: {
        to: string;
        subject?: string;
    }): Promise<any>;
    getTemplates(): Promise<any>;
    getQueueStatus(): Promise<any>;
    sendOrderNotification(orderId: number, notificationData: {
        type: string;
        recipients?: string[];
    }): Promise<any>;
}
