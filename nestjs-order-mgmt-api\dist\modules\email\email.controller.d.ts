import { EmailService } from './email.service';
import { SendEmailDto } from './dto/send-email.dto';
export declare class EmailController {
    private readonly emailService;
    constructor(emailService: EmailService);
    sendEmail(sendEmailDto: SendEmailDto): Promise<{
        jobId: import("bull").JobId;
        message: string;
    }>;
    sendTestEmail(testData: {
        to: string;
        subject?: string;
    }): Promise<any>;
    getTemplates(): Promise<any>;
    getQueueStatus(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        total: number;
    }>;
    sendOrderNotification(orderId: number, notificationData: {
        type: string;
        recipients?: string[];
    }): Promise<any>;
}
