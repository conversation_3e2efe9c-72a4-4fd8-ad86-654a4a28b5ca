import { Repository } from 'typeorm';
import { Template, TemplateType, TemplateCategory } from './entities/template.entity';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { TemplateQueryDto } from './dto/template-query.dto';
export declare class TemplatesService {
    private readonly templateRepository;
    constructor(templateRepository: Repository<Template>);
    private registerHandlebarsHelpers;
    create(createTemplateDto: CreateTemplateDto): Promise<{
        template: Template;
        message: string;
    }>;
    findAll(query: TemplateQueryDto): Promise<{
        templates: Template[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<Template>;
    findByCategory(category: string): Promise<Template[]>;
    update(id: number, updateTemplateDto: UpdateTemplateDto): Promise<{
        template: Template;
        message: string;
    }>;
    updateStatus(id: number, isActive: boolean): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    renderTemplate(id: number, variables: Record<string, any>): Promise<{
        subject: string;
        content: string;
        htmlContent: any;
        type: TemplateType;
        category: TemplateCategory;
    }>;
    previewTemplate(id: number): Promise<{
        subject: string;
        content: string;
        htmlContent: any;
        type: TemplateType;
        category: TemplateCategory;
    }>;
    duplicateTemplate(id: number): Promise<{
        template: Template;
        message: string;
    }>;
    validateTemplate(content: string, htmlContent?: string): Promise<{
        isValid: boolean;
        errors: string[];
    }>;
    getCategories(): Promise<{
        value: TemplateCategory;
        label: string;
    }[]>;
    getAvailableVariables(): Promise<{
        name: string;
        description: string;
        type: string;
    }[]>;
    private generateSampleData;
}
