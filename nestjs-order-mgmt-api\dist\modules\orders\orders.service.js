"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const order_entity_1 = require("./entities/order.entity");
const property_entity_1 = require("../properties/entities/property.entity");
const inspector_entity_1 = require("../inspectors/entities/inspector.entity");
const schedule_entity_1 = require("../schedules/entities/schedule.entity");
let OrdersService = class OrdersService {
    constructor(orderRepository, propertyRepository, inspectorRepository, scheduleRepository) {
        this.orderRepository = orderRepository;
        this.propertyRepository = propertyRepository;
        this.inspectorRepository = inspectorRepository;
        this.scheduleRepository = scheduleRepository;
    }
    async create(createOrderDto, user) {
        const orderNumber = await this.generateOrderNumber();
        if (createOrderDto.propertyId) {
            const property = await this.propertyRepository.findOne({
                where: { id: createOrderDto.propertyId },
            });
            if (!property) {
                throw new common_1.NotFoundException('Property not found');
            }
        }
        if (createOrderDto.assignedInspectorIds && createOrderDto.assignedInspectorIds.length > 0) {
            const inspectors = await this.inspectorRepository.find({
                where: { id: (0, typeorm_2.In)(createOrderDto.assignedInspectorIds), isActive: true },
            });
            if (inspectors.length !== createOrderDto.assignedInspectorIds.length) {
                throw new common_1.NotFoundException('One or more inspectors not found or inactive');
            }
        }
        let clientIds = [];
        if (user.role === 'client') {
            clientIds = [user.userId];
        }
        else {
            if (createOrderDto.clientIds && createOrderDto.clientIds.length > 0) {
                clientIds = createOrderDto.clientIds;
            }
            else if (createOrderDto.clientId) {
                clientIds = [createOrderDto.clientId];
            }
        }
        if (clientIds.length > 0) {
            const clients = await this.orderRepository.manager.find('User', {
                where: { id: (0, typeorm_2.In)(clientIds), role: 'client' },
            });
            if (clients.length !== clientIds.length) {
                throw new common_1.NotFoundException('One or more clients not found or invalid');
            }
        }
        const order = this.orderRepository.create({
            ...createOrderDto,
            orderNumber,
            clientIds,
            status: order_entity_1.OrderStatus.PENDING,
        });
        const savedOrder = await this.orderRepository.save(order);
        return {
            order: await this.findOne(savedOrder.id, user),
            message: 'Order created successfully',
        };
    }
    async findAll(query, user) {
        const { page = 1, limit = 10, status, clientId, inspectorId, propertyId, search, startDate, endDate, sortBy = 'createdAt', sortOrder = 'DESC', } = query;
        const queryBuilder = this.orderRepository.createQueryBuilder('order');
        queryBuilder.leftJoinAndSelect('order.property', 'property');
        queryBuilder.leftJoinAndSelect('order.inspector', 'inspector');
        queryBuilder.leftJoinAndSelect('order.client', 'client');
        if (status) {
            queryBuilder.andWhere('order.status = :status', { status });
        }
        if (clientId) {
            queryBuilder.andWhere(':clientId = ANY(order.clientIds)', { clientId });
        }
        if (inspectorId) {
            queryBuilder.andWhere(':inspectorId = ANY(order.assignedInspectorIds)', { inspectorId });
        }
        if (propertyId) {
            queryBuilder.andWhere('order.propertyId = :propertyId', { propertyId });
        }
        if (search) {
            queryBuilder.andWhere('(order.orderNumber ILIKE :search OR order.clientName ILIKE :search OR order.clientEmail ILIKE :search)', { search: `%${search}%` });
        }
        if (startDate && endDate) {
            queryBuilder.andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        if (user.role === 'client') {
            queryBuilder.andWhere(':userId = ANY(order.clientIds)', { userId: user.userId });
        }
        else if (user.role === 'inspector') {
            queryBuilder.andWhere(':userId = ANY(order.assignedInspectorIds)', { userId: user.userId });
        }
        queryBuilder.orderBy(`order.${sortBy}`, sortOrder);
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [orders, total] = await queryBuilder.getManyAndCount();
        return {
            orders,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id, user) {
        const order = await this.orderRepository.findOne({
            where: { id },
            relations: ['property', 'inspector', 'client'],
        });
        if (!order) {
            throw new common_1.NotFoundException('Order not found');
        }
        if (user.role === 'client' && !order.clientIds?.includes(user.userId)) {
            throw new common_1.ForbiddenException('You can only view your own orders');
        }
        else if (user.role === 'inspector' && !order.assignedInspectorIds?.includes(user.userId)) {
            throw new common_1.ForbiddenException('You can only view your assigned orders');
        }
        return order;
    }
    async update(id, updateOrderDto, user) {
        const order = await this.findOne(id, user);
        if (user.role === 'client' && !order.clientIds?.includes(user.userId)) {
            throw new common_1.ForbiddenException('You can only update your own orders');
        }
        if (updateOrderDto.status) {
            this.validateStatusTransition(order.status, updateOrderDto.status);
        }
        if (updateOrderDto.assignedInspectorIds && updateOrderDto.assignedInspectorIds.length > 0) {
            const inspectors = await this.inspectorRepository.find({
                where: { id: (0, typeorm_2.In)(updateOrderDto.assignedInspectorIds), isActive: true },
            });
            if (inspectors.length !== updateOrderDto.assignedInspectorIds.length) {
                throw new common_1.NotFoundException('One or more inspectors not found or inactive');
            }
        }
        await this.orderRepository.update(id, updateOrderDto);
        const updatedOrder = await this.findOne(id, user);
        return {
            order: updatedOrder,
            message: 'Order updated successfully',
        };
    }
    async remove(id, user) {
        const order = await this.findOne(id, user);
        if (order.status !== order_entity_1.OrderStatus.PENDING) {
            throw new common_1.BadRequestException('Only pending orders can be deleted');
        }
        if (user.role === 'client' && !order.clientIds?.includes(user.userId)) {
            throw new common_1.ForbiddenException('You can only delete your own orders');
        }
        await this.orderRepository.remove(order);
        return {
            message: 'Order deleted successfully',
        };
    }
    async assignInspectors(id, inspectorIds, user) {
        const order = await this.findOne(id, user);
        if (order.status !== order_entity_1.OrderStatus.PENDING) {
            throw new common_1.BadRequestException('Can only assign inspectors to pending orders');
        }
        const inspectors = await this.inspectorRepository.find({
            where: { id: (0, typeorm_2.In)(inspectorIds), isActive: true, isAvailable: true },
        });
        if (inspectors.length !== inspectorIds.length) {
            throw new common_1.NotFoundException('One or more inspectors not found or not available');
        }
        await this.orderRepository.update(id, {
            assignedInspectorIds: inspectorIds,
            status: order_entity_1.OrderStatus.SCHEDULED,
        });
        return {
            message: 'Inspectors assigned successfully',
            order: await this.findOne(id, user),
        };
    }
    async assignInspector(id, inspectorId, user) {
        return this.assignInspectors(id, [inspectorId], user);
    }
    async scheduleInspection(id, scheduleData, user) {
        const order = await this.findOne(id, user);
        if (order.status !== order_entity_1.OrderStatus.SCHEDULED) {
            throw new common_1.BadRequestException('Order must be assigned before scheduling');
        }
        const schedule = await this.scheduleRepository.findOne({
            where: {
                id: scheduleData.scheduleId,
                available: true,
            },
        });
        if (!order.assignedInspectorIds?.includes(schedule.inspectorId)) {
            throw new common_1.BadRequestException('Schedule does not belong to assigned inspectors');
        }
        if (!schedule) {
            throw new common_1.NotFoundException('Schedule slot not available');
        }
        await this.scheduleRepository.update(scheduleData.scheduleId, {
            inspectionOrderId: id,
            available: false,
        });
        await this.orderRepository.update(id, {
            status: order_entity_1.OrderStatus.SCHEDULED,
            scheduledDate: schedule.date,
            scheduledTime: schedule.startTime,
        });
        return {
            message: 'Inspection scheduled successfully',
            order: await this.findOne(id, user),
        };
    }
    async completeInspection(id, completionData, user) {
        const order = await this.findOne(id, user);
        if (order.status !== order_entity_1.OrderStatus.IN_PROGRESS) {
            throw new common_1.BadRequestException('Order must be in progress to complete');
        }
        if (user.role === 'inspector' && !order.assignedInspectorIds?.includes(user.userId)) {
            throw new common_1.ForbiddenException('You can only complete your assigned inspections');
        }
        await this.orderRepository.update(id, {
            status: order_entity_1.OrderStatus.COMPLETED,
            completedAt: new Date(),
            inspectionReport: completionData.report,
            inspectionNotes: completionData.notes,
        });
        if (order.assignedInspectorIds && order.assignedInspectorIds.length > 0) {
            for (const inspectorId of order.assignedInspectorIds) {
                await this.inspectorRepository.increment({ id: inspectorId }, 'completedInspections', 1);
            }
        }
        return {
            message: 'Inspection completed successfully',
            order: await this.findOne(id, user),
        };
    }
    async cancelOrder(id, reason, user) {
        const order = await this.findOne(id, user);
        if ([order_entity_1.OrderStatus.COMPLETED, order_entity_1.OrderStatus.CANCELLED].includes(order.status)) {
            throw new common_1.BadRequestException('Cannot cancel completed or already cancelled orders');
        }
        if (order.status === order_entity_1.OrderStatus.SCHEDULED) {
            await this.scheduleRepository.update({ inspectionOrderId: id }, { inspectionOrderId: null, available: true });
        }
        await this.orderRepository.update(id, {
            status: order_entity_1.OrderStatus.CANCELLED,
            cancellationReason: reason,
            cancelledAt: new Date(),
        });
        return {
            message: 'Order cancelled successfully',
            order: await this.findOne(id, user),
        };
    }
    async getOrderStats() {
        const totalOrders = await this.orderRepository.count();
        const ordersByStatus = await this.orderRepository
            .createQueryBuilder('order')
            .select('order.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('order.status')
            .getRawMany();
        const recentOrders = await this.orderRepository.find({
            take: 10,
            order: { createdAt: 'DESC' },
            relations: ['property', 'inspector'],
        });
        return {
            totalOrders,
            ordersByStatus,
            recentOrders,
        };
    }
    async getClientOrders(clientId, query) {
        return this.findAll({ ...query, clientId }, { role: 'client', userId: clientId });
    }
    async getOrdersForClients(clientIds, query) {
        const queryBuilder = this.orderRepository.createQueryBuilder('order');
        queryBuilder.leftJoinAndSelect('order.property', 'property');
        queryBuilder.leftJoinAndSelect('order.inspector', 'inspector');
        queryBuilder.where('order.clientIds && :clientIds', { clientIds });
        if (query.status) {
            queryBuilder.andWhere('order.status = :status', { status: query.status });
        }
        if (query.search) {
            queryBuilder.andWhere('(order.orderNumber ILIKE :search OR order.clientName ILIKE :search OR order.clientEmail ILIKE :search)', { search: `%${query.search}%` });
        }
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        const [orders, total] = await queryBuilder.getManyAndCount();
        return {
            orders,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getInspectorOrders(inspectorId, query) {
        return this.findAll({ ...query, inspectorId }, { role: 'inspector', userId: inspectorId });
    }
    async getOrdersByInspector(inspectorId) {
        return this.orderRepository
            .createQueryBuilder('order')
            .leftJoinAndSelect('order.property', 'property')
            .where(':inspectorId = ANY(order.assignedInspectorIds)', { inspectorId })
            .getMany();
    }
    async addClientToOrder(orderId, clientId, user) {
        const order = await this.findOne(orderId, user);
        if (!order.clientIds) {
            order.clientIds = [];
        }
        if (order.clientIds.includes(clientId)) {
            throw new common_1.BadRequestException('Client is already associated with this order');
        }
        const client = await this.orderRepository.manager.findOne('User', {
            where: { id: clientId, role: 'client' },
        });
        if (!client) {
            throw new common_1.NotFoundException('Client not found');
        }
        order.clientIds.push(clientId);
        await this.orderRepository.save(order);
        return {
            message: 'Client added to order successfully',
            order: await this.findOne(orderId, user),
        };
    }
    async removeClientFromOrder(orderId, clientId, user) {
        const order = await this.findOne(orderId, user);
        if (!order.clientIds || !order.clientIds.includes(clientId)) {
            throw new common_1.BadRequestException('Client is not associated with this order');
        }
        order.clientIds = order.clientIds.filter(id => id !== clientId);
        await this.orderRepository.save(order);
        return {
            message: 'Client removed from order successfully',
            order: await this.findOne(orderId, user),
        };
    }
    async updateOrderClients(orderId, clientIds, user) {
        const order = await this.findOne(orderId, user);
        if (clientIds.length > 0) {
            const clients = await this.orderRepository.manager.find('User', {
                where: { id: (0, typeorm_2.In)(clientIds), role: 'client' },
            });
            if (clients.length !== clientIds.length) {
                throw new common_1.NotFoundException('One or more clients not found');
            }
        }
        order.clientIds = clientIds;
        await this.orderRepository.save(order);
        return {
            message: 'Order clients updated successfully',
            order: await this.findOne(orderId, user),
        };
    }
    async generateOrderNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const startOfMonth = new Date(year, new Date().getMonth(), 1);
        const endOfMonth = new Date(year, new Date().getMonth() + 1, 0);
        const monthlyCount = await this.orderRepository.count({
            where: {
                createdAt: (0, typeorm_2.Between)(startOfMonth, endOfMonth),
            },
        });
        const sequence = String(monthlyCount + 1).padStart(4, '0');
        return `ORD-${year}${month}-${sequence}`;
    }
    validateStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            [order_entity_1.OrderStatus.PENDING]: [order_entity_1.OrderStatus.SCHEDULED, order_entity_1.OrderStatus.CANCELLED],
            [order_entity_1.OrderStatus.SCHEDULED]: [order_entity_1.OrderStatus.SCHEDULED, order_entity_1.OrderStatus.CANCELLED],
            [order_entity_1.OrderStatus.IN_PROGRESS]: [order_entity_1.OrderStatus.COMPLETED, order_entity_1.OrderStatus.CANCELLED],
            [order_entity_1.OrderStatus.COMPLETED]: [],
            [order_entity_1.OrderStatus.CANCELLED]: [],
        };
        if (!validTransitions[currentStatus].includes(newStatus)) {
            throw new common_1.BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
        }
    }
};
exports.OrdersService = OrdersService;
exports.OrdersService = OrdersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_entity_1.Order)),
    __param(1, (0, typeorm_1.InjectRepository)(property_entity_1.Property)),
    __param(2, (0, typeorm_1.InjectRepository)(inspector_entity_1.Inspector)),
    __param(3, (0, typeorm_1.InjectRepository)(schedule_entity_1.Schedule)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], OrdersService);
//# sourceMappingURL=orders.service.js.map