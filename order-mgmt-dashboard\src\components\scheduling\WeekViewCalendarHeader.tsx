
import React from 'react';
import { format } from 'date-fns';

interface WeekViewCalendarHeaderProps {
  days: Date[];
}

const WeekViewCalendarHeader: React.FC<WeekViewCalendarHeaderProps> = ({ days }) => {
  const today = new Date();
  const isToday = (date: Date) => {
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  return (
    <div className="grid grid-cols-[100px_repeat(7,1fr)] border-b sticky top-0 bg-white z-10 shadow-sm">
      <div className="p-2 font-medium text-gray-700 flex items-center justify-center">
        <span className="bg-gray-100 px-2 py-1 rounded-md text-sm">Time</span>
      </div>
      {days.map(day => {
        const dayIsToday = isToday(day);
        return (
          <div
            key={day.toISOString()}
            className={`p-2 text-center border-l ${dayIsToday ? 'bg-blue-50' : ''}`}
          >
            <div className={`font-medium ${dayIsToday ? 'text-blue-700' : ''}`}>
              {format(day, 'EEE')}
            </div>
            <div className={`text-sm ${dayIsToday ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
              {format(day, 'MMM d')}
            </div>
            {dayIsToday && (
              <div className="w-2 h-2 bg-blue-500 rounded-full mx-auto mt-1"></div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default WeekViewCalendarHeader;

