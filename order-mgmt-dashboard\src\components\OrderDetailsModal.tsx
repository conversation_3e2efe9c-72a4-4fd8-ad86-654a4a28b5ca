import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { GeneralSection } from "./order-form/GeneralSection";
import { PropertySection } from "./order-form/PropertySection";
import { ServicesSection } from "./order-form/ServicesSection";
import { AgentsSection } from "./order-form/AgentsSection";
import { FeesSection } from "./order-form/FeesSection";
import { ExternalLink, ChevronLeft, ChevronRight, X } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog";
import React, { useState, useEffect } from "react";
import { OrderFormData } from "@/types/order";
import { zodResolver } from "@hookform/resolvers/zod";
import { orderFormSchema } from "@/schemas/order";
import { useToast } from "@/hooks/use-toast";

interface Activity {
  id: string;
  date: string;
  type: string;
  description: string;
}

type OrderDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  mode: "view" | "edit" | "add" | "create";
  order?: {
    id?: string;
    orderNumber?: string;
    propertyAddress?: string;
    inspectionDate?: string;
    status?: string;
    cost?: number;
    clientName?: string;
    clientEmail?: string;
    clientPhone?: string;
    clientTags?: string[];
    activities?: Activity[];
  };
  onSave?: (id: string, formData: OrderFormData) => void;
};

const steps = [
  { id: "general", title: "General" },
  { id: "property", title: "Property" },
  { id: "services", title: "Add-ons" },
  { id: "agents", title: "Agents" },
  { id: "fees", title: "Fees" },
];

const DEFAULT_FORM_VALUES: OrderFormData = {
  id: "",
  clientName: "",
  clientEmail: "",
  clientPhone: "",
  status: "pending",
  propertyType: "",
  yearBuilt: "",
  propertyAge: "",
  foundationType: "",
  gateCode: "",
  lockboxCode: "",
  mlsNumber: "",
  isClientAttending: false,
  isOccupied: false,
  hasUtilities: false,
  hasAlarm: false,
  services: {
    flexfund: false,
    mold: false,
  },
  agentName: "",
  agentEmail: "",
  agentPhone: "",
  isSeller: false,
  isBuyer: false,
  inspectionFee: "",
  thirdPartyFee: "",
  discountFee: "",
  processingFee: "",
};

export default function OrderDetailsModal({
  isOpen,
  onClose,
  mode,
  order,
  onSave,
}: OrderDetailsModalProps) {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const form = useForm<OrderFormData>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: DEFAULT_FORM_VALUES,
  });

  useEffect(() => {
    if (!order) {
      return form.reset(DEFAULT_FORM_VALUES);
    }

    form.reset({
      clientName: order.clientName || "",
      clientEmail: order.clientEmail || "",
      clientPhone: order.clientPhone || "",
      status: order.status || "pending",
      propertyType: "",
      yearBuilt: "",
      propertyAge: "",
      foundationType: "",
      gateCode: "",
      lockboxCode: "",
      mlsNumber: "",
      isClientAttending: false,
      isOccupied: false,
      hasUtilities: false,
      hasAlarm: false,
      services: {
        flexfund: false,
        mold: false,
      },
      agentName: "",
      agentEmail: "",
      agentPhone: "",
      isSeller: false,
      isBuyer: false,
      inspectionFee: order.cost?.toString() || "",
      thirdPartyFee: "",
      discountFee: "",
      processingFee: "",
    });
  }, [order, mode, form, isOpen]);

  const handleOpenInNewTab = () => {
    if (order?.id) {
      window.open(`/orders/${order.id}`, "_blank", "noopener,noreferrer");
    }
  };

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const renderStepContent = () => {
    const currentStepId = steps[currentStep].id;
    switch (currentStepId) {
      case "general":
        return <GeneralSection form={form} />;
      case "property":
        return <PropertySection form={form} />;
      case "services":
        return <ServicesSection form={form} />;
      case "agents":
        return <AgentsSection form={form} />;
      case "fees":
        return <FeesSection form={form} />;
      default:
        return null;
    }
  };

  const onSubmit = async (data: OrderFormData) => {
    try {
      await onSave(order?.id, data);
      onClose();
    } catch (error) {
      console.error("Error saving order:", error);
      toast({
        title: "Unable to Save Order",
        description: "We couldn't save your changes. Please check your connection and try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="w-[400px] max-w-full h-[70vh] max-h-[70vh] flex flex-col p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center gap-2">
            <DialogTitle className="text-lg font-semibold">
              {mode === "edit" ? "Edit Inspection Order" : "New Inspection Order"}
            </DialogTitle>
            {order?.id && (
              <button
                className="ml-2 text-gray-500 hover:text-blue-500"
                onClick={handleOpenInNewTab}
                type="button"
                tabIndex={0}
                aria-label="Open order in new tab"
              >
                <ExternalLink size={18} />
              </button>
            )}
          </div>
        </DialogHeader>

        {mode !== "view" && (
          <div className="px-6 mb-4">
            {/* Steps indicator */}
            <div className="flex justify-between mb-2">
              {steps.map((step, idx) => (
                <div
                  key={step.id}
                  className={`flex flex-col items-center ${idx <= currentStep ? "text-primary" : "text-gray-400"}`}
                >
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center text-xs
                    ${idx <= currentStep ? "bg-primary text-white" : "bg-gray-200 text-gray-500"}`}
                  >
                    {idx + 1}
                  </div>
                  <div className="text-xs mt-1">{step.title}</div>
                </div>
              ))}
            </div>

            {/* Progress bar */}
            <div className="w-full bg-gray-200 h-1 rounded-full">
              <div
                className="bg-primary h-1 rounded-full transition-all"
                style={{
                  width: `${((currentStep + 1) / steps.length) * 100}%`,
                }}
              ></div>
            </div>
          </div>
        )}

        <Form {...form}>
          <form 
            className="flex-1 overflow-hidden flex flex-col"
            onSubmit={form.handleSubmit(onSubmit, (errors) => {
              toast({
                title: "Please Check Your Information",
                description: `Please complete order information before submit`,
                duration: 5000,
              });
            })}
          >
            <div className="flex-1 overflow-auto px-6">
              {mode === "view" ? (
                <div className="space-y-4">
                  <div className="font-medium text-gray-800">Client Name</div>
                  <div>{order?.clientName || <span className="text-gray-400">-</span>}</div>
                  <div className="font-medium text-gray-800">Client Email</div>
                  <div>{order?.clientEmail || <span className="text-gray-400">-</span>}</div>
                </div>
              ) : (
                renderStepContent()
              )}
            </div>

            {mode !== "view" && (
              <div className="border-t p-6 flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 0}
                  className={currentStep === 0 ? "opacity-50" : ""}
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                {currentStep < steps.length - 1 ? (
                  <Button 
                    type="button" 
                    onClick={(e) => {
                      e.preventDefault();
                      nextStep();
                    }}
                  >
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button type="submit">{mode === "add" ? "Create" : "Save Changes"}</Button>
                )}
              </div>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
