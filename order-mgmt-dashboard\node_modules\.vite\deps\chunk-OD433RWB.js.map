{"version": 3, "sources": ["../../@radix-ui/primitive/src/primitive.tsx"], "sourcesContent": ["function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "mappings": ";AAAA,SAAS,qBACP,sBACA,iBACA,EAAE,2BAA2B,KAAK,IAAI,CAAC,GACvC;AACA,SAAO,SAAS,YAAY,OAAU;AACpC,iEAAuB;AAEvB,QAAI,6BAA6B,SAAS,CAAG,MAA4B,kBAAkB;AACzF,aAAO,mDAAkB;IAC3B;EACF;AACF;", "names": []}