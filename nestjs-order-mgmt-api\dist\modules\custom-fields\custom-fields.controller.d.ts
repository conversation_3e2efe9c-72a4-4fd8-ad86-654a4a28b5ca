import { CustomFieldsService } from './custom-fields.service';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { CustomFieldQueryDto } from './dto/custom-field-query.dto';
import { SetCustomFieldValueDto } from './dto/set-custom-field-value.dto';
export declare class CustomFieldsController {
    private readonly customFieldsService;
    constructor(customFieldsService: CustomFieldsService);
    create(createCustomFieldDto: CreateCustomFieldDto): Promise<{
        customField: import("./entities/custom-field.entity").CustomField;
        message: string;
    }>;
    findAll(query: CustomFieldQueryDto, user: any): Promise<{
        customFields: import("./entities/custom-field.entity").CustomField[];
    }>;
    getByEntityType(entityType: string, user: any): Promise<{
        entityType: string;
        customFields: import("./entities/custom-field.entity").CustomField[];
    }>;
    getEntityValues(entityType: string, entityId: number, user: any): Promise<{
        entityType: string;
        entityId: number;
        fields: {
            value: any;
            hasValue: boolean;
            id: number;
            name: string;
            key: string;
            description: string;
            type: import("./entities/custom-field.entity").CustomFieldType;
            entityType: import("./entities/custom-field.entity").CustomFieldEntity;
            options: {
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: {
                    required?: boolean;
                    minLength?: number;
                    maxLength?: number;
                    min?: number;
                    max?: number;
                    pattern?: string;
                };
                display?: {
                    width?: string;
                    columns?: number;
                    rows?: number;
                };
            };
            defaultValue: string;
            isRequired: boolean;
            isActive: boolean;
            isVisible: boolean;
            isSearchable: boolean;
            sortOrder: number;
            group: string;
            permissions: string[];
            createdAt: Date;
            updatedAt: Date;
            values: import("./entities/custom-field-value.entity").CustomFieldValue[];
        }[];
    }>;
    findOne(id: number): Promise<import("./entities/custom-field.entity").CustomField>;
    update(id: number, updateCustomFieldDto: UpdateCustomFieldDto): Promise<{
        customField: import("./entities/custom-field.entity").CustomField;
        message: string;
    }>;
    activate(id: number): Promise<{
        message: string;
    }>;
    deactivate(id: number): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    setValue(setValueDto: SetCustomFieldValueDto, user: any): Promise<{
        message: string;
        value: any;
    }>;
    setEntityValues(entityType: string, entityId: number, values: {
        [fieldKey: string]: any;
    }, user: any): Promise<{
        results: any[];
        message: string;
    }>;
    deleteValue(entityType: string, entityId: number, fieldId: number, user: any): Promise<{
        message: string;
    }>;
    getAvailableTypes(): Promise<{
        value: import("./entities/custom-field.entity").CustomFieldType;
        label: string;
    }[]>;
    getAvailableEntities(): Promise<{
        value: import("./entities/custom-field.entity").CustomFieldEntity;
        label: string;
    }[]>;
    duplicate(id: number): Promise<{
        customField: import("./entities/custom-field.entity").CustomField;
        message: string;
    }>;
}
