import { SchedulesService } from './schedules.service';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { ScheduleQueryDto } from './dto/schedule-query.dto';
import { CheckConflictDto } from './dto/check-conflict.dto';
import { BulkCreateScheduleDto } from './dto/bulk-create-schedule.dto';
export declare class SchedulesController {
    private readonly schedulesService;
    constructor(schedulesService: SchedulesService);
    create(createScheduleDto: CreateScheduleDto, user: any): Promise<{
        schedule: import("./entities/schedule.entity").Schedule;
        message: string;
    }>;
    bulkCreate(bulkCreateDto: BulkCreateScheduleDto, user: any): Promise<{
        results: any[];
        message: string;
    }>;
    findAll(query: ScheduleQueryDto, user: any): Promise<{
        schedules: import("./entities/schedule.entity").Schedule[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    checkConflicts(checkConflictDto: CheckConflictDto): Promise<{
        hasConflicts: boolean;
        conflicts: import("./entities/schedule.entity").Schedule[];
    }>;
    getAvailability(inspectorId: number, startDate: string, endDate: string): Promise<{
        inspectorId: number;
        startDate: string;
        endDate: string;
        availability: {};
    }>;
    getCalendar(inspectorId: number, month?: string, year?: string): Promise<{
        month: number;
        year: number;
        schedules: {
            id: number;
            date: string;
            startTime: string;
            endTime: string;
            available: boolean;
            order: {
                id: number;
                orderNumber: string;
                clientName: string;
            };
        }[];
    }>;
    findOne(id: number, user: any): Promise<import("./entities/schedule.entity").Schedule>;
    update(id: number, updateScheduleDto: UpdateScheduleDto, user: any): Promise<{
        schedule: import("./entities/schedule.entity").Schedule;
        message: string;
    }>;
    assignOrder(id: number, assignData: {
        orderId: number;
    }, user: any): Promise<{
        message: string;
        schedule: import("./entities/schedule.entity").Schedule;
    }>;
    unassignOrder(id: number, user: any): Promise<{
        message: string;
        schedule: import("./entities/schedule.entity").Schedule;
    }>;
    remove(id: number, user: any): Promise<{
        message: string;
    }>;
    getStats(): Promise<{
        totalSchedules: number;
        availableSchedules: number;
        assignedSchedules: number;
        schedulesByInspector: any[];
    }>;
    createRecurring(recurringData: any, user: any): Promise<{
        schedules: any[];
        message: string;
    }>;
}
