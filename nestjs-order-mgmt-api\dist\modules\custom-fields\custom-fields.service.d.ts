import { Repository } from 'typeorm';
import { CustomField, CustomFieldType, CustomFieldEntity } from './entities/custom-field.entity';
import { CustomFieldValue } from './entities/custom-field-value.entity';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { CustomFieldQueryDto } from './dto/custom-field-query.dto';
import { SetCustomFieldValueDto } from './dto/set-custom-field-value.dto';
export declare class CustomFieldsService {
    private readonly customFieldRepository;
    private readonly customFieldValueRepository;
    constructor(customFieldRepository: Repository<CustomField>, customFieldValueRepository: Repository<CustomFieldValue>);
    create(createCustomFieldDto: CreateCustomFieldDto): Promise<{
        customField: CustomField;
        message: string;
    }>;
    findAll(query: CustomFieldQueryDto, user: any): Promise<{
        customFields: CustomField[];
    }>;
    findOne(id: number): Promise<CustomField>;
    getByEntityType(entityType: string, user: any): Promise<{
        entityType: string;
        customFields: CustomField[];
    }>;
    getEntityValues(entityType: string, entityId: number, user: any): Promise<{
        entityType: string;
        entityId: number;
        fields: {
            value: any;
            hasValue: boolean;
            id: number;
            name: string;
            key: string;
            description: string;
            type: CustomFieldType;
            entityType: CustomFieldEntity;
            options: {
                choices?: {
                    value: string;
                    label: string;
                }[];
                placeholder?: string;
                helpText?: string;
                validation?: {
                    required?: boolean;
                    minLength?: number;
                    maxLength?: number;
                    min?: number;
                    max?: number;
                    pattern?: string;
                };
                display?: {
                    width?: string;
                    columns?: number;
                    rows?: number;
                };
            };
            defaultValue: string;
            isRequired: boolean;
            isActive: boolean;
            isVisible: boolean;
            isSearchable: boolean;
            sortOrder: number;
            group: string;
            permissions: string[];
            createdAt: Date;
            updatedAt: Date;
            values: CustomFieldValue[];
        }[];
    }>;
    update(id: number, updateCustomFieldDto: UpdateCustomFieldDto): Promise<{
        customField: CustomField;
        message: string;
    }>;
    updateStatus(id: number, isActive: boolean): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    setValue(setValueDto: SetCustomFieldValueDto, user: any): Promise<{
        message: string;
        value: any;
    }>;
    setEntityValues(entityType: string, entityId: number, values: {
        [fieldKey: string]: any;
    }, user: any): Promise<{
        results: any[];
        message: string;
    }>;
    deleteValue(entityType: string, entityId: number, fieldId: number, user: any): Promise<{
        message: string;
    }>;
    duplicate(id: number): Promise<{
        customField: CustomField;
        message: string;
    }>;
    getAvailableTypes(): Promise<{
        value: CustomFieldType;
        label: string;
    }[]>;
    getAvailableEntities(): Promise<{
        value: CustomFieldEntity;
        label: string;
    }[]>;
    private hasPermission;
    private validateValue;
    private parseValue;
    private isComplexType;
}
